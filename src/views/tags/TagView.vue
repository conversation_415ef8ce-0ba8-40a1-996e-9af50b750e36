<template>
  <div class="tags-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Tags"
      add-label="Tag"
      sheets="Tags"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
    />

    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No tags found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >{{ item.name }}</span>
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Tag"
                :name="item.name"
                :id="item.id"
                activate-url="/tags/:id/activate"
                deactivate-url="/tags/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <tag-form
      v-model="dialog"
      :isEdit="isEdit"
      :tagData="editTagData"
      @createTag="handleCreateTag"
      @updateTag="handleUpdateTag"
    />
  </div>
</template>
  
<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRoute } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { tableHeaders } from "@/helpers/tableHeaders";
import { useTagStore } from "@/stores/tag";
import { filterData } from "@/helpers/searchFilter";
import TagForm from "@/views/tags/TagForm.vue";

const tagStore = useTagStore();

const search = ref("");
const loading = ref(false);

const headers = ref(tableHeaders["tags"]);
const sortBy = ref([{ key: "name", order: "asc" }]);
const dialog = ref(false);
const isEdit = ref(false);

const selectedStatus = ref(null);

const selectStatus = status => {
  selectedStatus.value = status ?? null;
};

const items = computed(() => tagStore.getTags || []);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  if (selectedStatus.value !== null) {
    result = result.filter(item => item.activeStatus === selectedStatus.value);
  }
  return result;
});

const applyFilters = filters => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders.tags(filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter(h => h.enable));

const handleSearch = query => {
  search.value = query;
};

const editTagData = ref(null);

const add = () => {
  dialog.value = true;
  isEdit.value = false;
  editTagData.value = null;
};

const edit = async id => {
  try {
    const tag = await tagStore.fetchTagById(id);
    editTagData.value = tag;
    dialog.value = true;
    isEdit.value = true;
  } catch (error) {
    console.error(error);
  }
};

const handleCreateTag = async (tag, done) => {
  try {
    await tagStore.createTag(tag);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const handleUpdateTag = async (tag, done) => {
  try {
    await tagStore.updateTag(tag);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const toggleActivate = async ({ id }) => {
  try {
    await tagStore.updateTagActiveStatus(id);
    await refresh();
  } catch (error) {
    console.error(error);
  }
};

const fetch = async () => {
  await tagStore.fetchTags();
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await fetch()
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await tagStore.fetchTags();
});
</script>
  
<style scoped>
</style>
  