export const calculateRecipeCost = (ingredient, quantityInRecipeUnit) => {
  const { unitCost, recipeUnit } = ingredient;
  // unitCost is cost for 1 purchaseUnit (ex: 1 kg)
  // recipeUnit.conversion = how many recipeUnits in 1 purchaseUnit
  const constPerRecipeUnit = unitCost / (recipeUnit.conversion || 1);
  return quantityInRecipeUnit * constPerRecipeUnit;
};

export const calculateTaxAmountAndTotalPrice = (row, type) => {
  const qty = type == "pr" ? row.quantity : row.receivedQty;

  if (!row.unitCost || !qty) return 0;

  // Calculate subtotal before discount
  const subTotal = Number(row.unitCost) * Number(qty);

  // Apply discount on subtotal
  const discountedSubTotal = subTotal - Number(row.discount || 0);

  // Calculate total tax rate (from selected taxes + other tax)
  const selectedTaxAmount = row.tax.reduce(
    (acc, item) => acc + (Number(item.value) || 0),
    0
  );
  row.taxRate = selectedTaxAmount;

  // Apply tax on discounted subtotal
  const taxAmount = (discountedSubTotal * row.taxRate) / 100;

  return {
    taxAmount,
    totalPrice: discountedSubTotal + taxAmount + row.cess,
  };
};
