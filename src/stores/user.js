import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId, getTenants } from "@/helpers/auth";


export const useUserStore = defineStore("user", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const users = ref([]);

  const getUsers = computed(() => users.value);
  const setUsers = (data) => {
    users.value = data;
  };

  const fetchUsers = async () => {
    try {
      const response = await httpClient.get(`users`);
      setUsers(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchUserById = async (id) => {
    try {
      const response = await httpClient.get(`users/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createUser = async (data) => {
    const tenantList = getTenants();
    const tenantName = tenantList.find(
      (tenant) => tenant.id === tenantId.value
    ).name;
    try {
      await httpClient.post("users", {
        ...data,
        tenantId: tenantId.value,
        tenantName
      });
      showSnackbar(
        "green",
        `User created successfully and an invite for the ${tenantName} tenant has been sent.`
      );
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateUser = async (data) => {
    try {
      await httpClient.put(`users/${data.id}`, {
        ...data,
        tenantId: tenantId.value
      });
      showSnackbar("green", "User updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateUserActiveStatus = async (id) => {
    try {
      await httpClient.put(`users/${id}/activateStatus`);
      fetchUsers();
      showSnackbar("green", "User status updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getUsers,
    setUsers,
    fetchUsers,
    fetchUserById,
    createUser,
    updateUser,
    updateUserActiveStatus
  };
});
