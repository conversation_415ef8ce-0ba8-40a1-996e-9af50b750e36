<template>
  <div>
    <!-- Report Header -->
    <ReportHeader
      v-model="payload"
      enable-items
      :loading="loading"
      @search="onSearch"
    />

    <v-container fluid>
      <v-row no-gutters>
        <v-card border class="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="stockLogHeaders"
            :items="items"
            :hide-default-footer="items.length < 21"
            :loading="loading"
            items-per-page="-1"
            no-data-text="No stock logs found"
          >
            <template #item.unit="{ item }">
              <span v-if="item.inventoryItem.countingUnit"
                >{{ item.inventoryItem.countingUnit.symbol }}
              </span>
              <span v-else>{{ item.inventoryItem.uom }} </span>
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";

import httpClient from "@/plugin/Axios";
import { NewReportPayload } from "@/helpers/useReportPayload";
import { stockLogHeaders } from "@/helpers/tableHeaders";

// components
import ReportHeader from "@/components/utils/ReportHeader.vue";

const items = ref([]);
const loading = ref(false);
const payload = ref(NewReportPayload("stock-log"));

const onSearch = async (reqPayload) => {
  loading.value = true;
  items.value = [];
  try {
    const res = await httpClient.post("/reports/stock-ledgers", reqPayload);
    items.value = res.data;
  } catch ({ response }) {
    // @todo:
    throw Error(response.data.message);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  onSearch(payload.value);
});
</script>
