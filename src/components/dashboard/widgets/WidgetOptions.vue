<template>
  <div class="widget-options d-flex align-center">
    <!-- options -->
    <v-select
      v-model="selected"
      :items="enabledOptions"
      item-title="title"
      item-value="value"
      density="compact"
      rounded="lg"
      color="primary"
      variant="solo-filled"
      flat
      item-props
      single-line
      hide-details
      @update:model-value="onChange"
    >
      <!-- Customize how selected item is displayed -->
      <template #selection="{ item }">
        <div class="d-flex align-center">
          <v-icon small class="me-2">{{ item.props.prependIcon }}</v-icon>
          <span>{{ item.title }}</span>
        </div>
      </template>
    </v-select>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";

const props = defineProps({
  modelValue: {
    type: Number,
    required: true
  },
  helper: {
    type: Object,
    default: () => ({ qty: "Count", value: "Value" })
  },
  enabledOptions: {
    type: Array,
    default: () => [1, 2, 3]
  }
});

const emit = defineEmits(["update:modelValue"]);

const options = [
  {
    value: 2,
    prependIcon: "mdi-chart-box-outline",
    title: `By ${props.helper.value}`
  },
  {
    value: 3,
    prependIcon: "mdi-chart-box-outline",
    title: `By ${props.helper.qty}`
  },
  {
    value: 1,
    prependIcon: "mdi-view-list-outline",
    title: "List view"
  }
];

// Filter only enabled options
const enabledOptions = computed(() =>
  options.filter(o => props.enabledOptions.includes(o.value))
);

const selected = ref(props.modelValue || enabledOptions.value[0]?.value);

const onChange = val => {
  emit("update:modelValue", val);
};
</script>

<style scoped>
</style>
