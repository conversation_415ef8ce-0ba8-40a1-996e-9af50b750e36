<template>
  <div>
    <auto-complete
      v-model="model"
      :items="taxes"
      v-bind="$attrs"
      :label="label"
      item-title="name"
      item-value="id"
      :filter-keys="['title', 'raw.value']"
      :loading="loading"
      :showSelectAll="false"
      :multiple="true"
    >
      <template v-slot:item="{ item, props }">
        <v-list-item v-bind="props">
          <template #prepend>
            <v-checkbox
              :model-value="itemIncluded(item.value)"
              density="compact"
              hide-details
              color="primary"
              class="mr-2"
              tabindex="-1"
              @click.stop
            ></v-checkbox>
          </template>
          <template #append> {{ item.raw.value }}% </template>
        </v-list-item>
      </template>
    </auto-complete>
  </div>
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Tax",
  },
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const taxes = computed(() => {
  return masterStore.getTaxes();
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});

const itemIncluded = (id) => {
  return model.value.some((item) => item.id === id);
};
</script>
