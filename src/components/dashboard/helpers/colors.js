// 🌈 Vibrant brand-inspired color palette (semi-transparent)
// Ideal for charts, cards, and metrics visualization.
const background_colors = [
  "#0080bb", // Google - Deep Blue
  "#58508d", // Bing - Indigo
  "#bc5090", // DuckDuckGo - Pink Magenta
  "#ff6361", // Brave - Coral Red
  "#ffa600", // Kagi - <PERSON>
  "#7b1fa2", // Yahoo - Purple
  "#2e7d32", // Ecosia - Green
  "#0288d1", // StartPage - Light Blue
  "#c62828", // Qwant - Deep Red
  "#d32f2f", // Yandex - Red
  "#1976d2", // Baidu - Blue
  "#388e3c", // Naver - Dark Green
  "#8e24aa", // Ask - Violet
  "#f57c00", // Seznam - Orange
  "#5d4037", // <PERSON>jee<PERSON> - <PERSON>
  "#3949ab", // You.com - Indigo
  "#00796b", // Neeva - Teal
  "#9e9d24", // Swisscows - Olive
  "#6d4c41", // Metager - Coffee
  "#ad1457", // Gigablast - Deep Pink
];

/**
 * Returns an array of brand-style vibrant colors.
 * If more colors are needed than predefined,
 * additional colors are generated randomly.
 *
 * @param {number} totalLength - Number of colors required
 * @returns {string[]} Array of color hex values
 */
export function getBgColors(totalLength) {
  const finalColors = [];
  const availableLen = background_colors.length;

  if (totalLength <= availableLen) {
    // Use available colors up to requested length
    return background_colors.slice(0, totalLength);
  }

  // Add all predefined colors
  finalColors.push(...background_colors);

  // Generate random hex colors for overflow
  for (let i = availableLen; i < totalLength; i++) {
    finalColors.push(
      `#${Math.floor(Math.random() * 16777215)
        .toString(16)
        .padStart(6, "0")}`
    );
  }

  return finalColors;
}
