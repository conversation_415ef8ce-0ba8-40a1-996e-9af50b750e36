/**
 * v-smart-focus
 * When Tab is pressed and nothing inside this container is focused,
 * focuses the first focusable element (scoped within this element).
 * Doesn’t affect anything outside its scope.
 */
export default {
  mounted(el, binding) {
    const selector =
      binding.value ||
      'input:not([disabled]):not([readonly]):not([type="hidden"]), textarea:not([disabled]):not([readonly]), select:not([disabled])';

    const focusFirst = () => {
      const first = el.querySelector(selector);
      first?.focus?.();
    };

    const handleKey = (e) => {
      // only intercept Tab, not Shift+Tab
      if (e.key !== 'Tab' || e.shiftKey) return;

      // check if focus is inside this scope
      const active = document.activeElement;
      const isInside = active && el.contains(active);

      // if nothing or body has focus, grab it
      if (!isInside || active === document.body) {
        e.preventDefault();
        focusFirst();
      }
    };

    el._smartScopeFocusHandler = handleKey;
    window.addEventListener('keydown', handleKey);
  },

  unmounted(el) {
    window.removeEventListener('keydown', el._smartScopeFocusHandler);
    delete el._smartScopeFocusHandler;
  },
};
