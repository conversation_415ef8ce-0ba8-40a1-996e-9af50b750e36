import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";

export const useTenantStore = defineStore("tenant", () => {
  const { showSnackbar } = useSnackbarStore();
  const tenant = ref([]);

  const getTenants = computed(() => tenant.value || []);
  const setTenants = (data) => {
    tenant.value = data;
  };

  const fetchTenants = async () => {    
    try {
      const response = await httpClient.get();
      setTenants(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updatedTenantSetting = async (data) => {
    try {
      await httpClient.post(`/setting`, data);
      showSnackbar("green", "Approval Setting Updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const freezeMonth = async (data) => {
    try {
      await httpClient.post(`/freeze`, data);
      showSnackbar("green", "Month Updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    setTenants,
    getTenants,
    fetchTenants,
    updatedTenantSetting,
    freezeMonth
  };
});
