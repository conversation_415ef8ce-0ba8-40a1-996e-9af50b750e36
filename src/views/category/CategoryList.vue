<template>
  <div class="categories-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Categories"
      add-label="Category"
      sheets="Categories"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      @apply-filters="applyFilters"
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No categories found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >{{ item.name }}</span>
            </template>
            <template v-slot:item.data-table-expand="{ internalItem, isExpanded, toggleExpand }">
              <v-btn
                :append-icon="isExpanded(internalItem) ? 'mdi-chevron-up' : 'mdi-chevron-down'"
                text="Subcategories"
                color="medium-emphasis"
                size="small"
                variant="tonal"
                border
                slim
                @click="toggleExpand(internalItem)"
              ></v-btn>
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Category"
                :id="item.id"
                :name="item.name"
                activate-url="/categories/:id/activate"
                deactivate-url="/categories/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>

            <template v-slot:expanded-row="{ columns, item }">
              <tr>
                <td :colspan="headers.length">
                  <v-card tile flat>
                    <v-card-text>
                      <div v-if="item.subCategories && item.subCategories.length > 0 ">
                        <div
                          v-for="(sub, index) in item.subCategories"
                          :key="`${item.id}-${index}`"
                          class="subcategory-item py-1"
                        >{{ index + 1 }}. {{ sub.name }}</div>
                      </div>
                      <div
                        v-else
                        class="text-center pa-2 text-medium-emphasis"
                      >No subcategories found</div>
                    </v-card-text>
                  </v-card>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { useCategoryStore } from "@/stores/category";
import { tableHeaders } from "@/helpers/tableHeaders";
import { filterData } from "@/helpers/searchFilter";

const categoryStore = useCategoryStore();
const router = useRouter();

const search = ref(null);
const loading = ref(false);

const items = computed(() => categoryStore.getCategories || []);
const headers = ref(tableHeaders["categories"]);
const sortBy = ref([{ key: "name", order: "asc" }]);

const selectedStatus = ref(null);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  if (selectedStatus.value !== null) {
    result = result.filter(item => item.activeStatus === selectedStatus.value);
  }
  return result;
});

const selectStatus = status => {
  selectedStatus.value = status ?? null;
};

const applyFilters = filters => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders["setCategories"](filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter(h => h.enable));

const add = () => {
  router.push({ name: "create-category" });
};

const edit = id => {
  router.push({ name: "edit-category", params: { id } });
};

const handleSearch = v => {
  search.value = v;
};

const fetch = async () => {
  await categoryStore.fetchCategories();
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await fetch()
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  refresh();
});
</script>

<style scoped>
</style>
