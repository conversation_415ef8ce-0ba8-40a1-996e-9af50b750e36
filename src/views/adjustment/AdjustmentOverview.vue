<template>
  <v-card border rounded="lg" class="mb-2">
    <v-expansion-panels variant="accordion" multiple flat focusable border>
      <!-- Transfer Details -->
      <v-expansion-panel v-if="data.id">
        <v-expansion-panel-title>
          #{{ data.adjustmentNumber }}
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="adjustmentDetails" />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<script setup>
import LabelValueView from "@/components/utils/LabelValueView.vue";
const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const adjustmentDetails = [
  { label: "Adjustment No.", value: props.data.adjustmentNumber },
  { label: "Location", value: props.data.locationName },
  { label: "Workarea", value: props.data.workAreaName },
  { label: "Created By", value: props.data.requestedBy?.name },
  { label: "Created At", value: props.data.requestedBy?.time },
];
</script>
