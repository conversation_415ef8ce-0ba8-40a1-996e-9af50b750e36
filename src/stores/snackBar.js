import { defineStore } from "pinia";
import { ref } from "vue";

export const useSnackbarStore = defineStore("snackbar", () => {
  const snackbar = ref({
    show: false,
    timeout: 0,
    color: "success",
    message: null,
  });
  const showSnackbar = (color, message, timeout = 3000) => {
    snackbar.value = {
      show: true,
      timeout: timeout,
      color: color || "success",
      message: message || "",
    };
  };
  const hideSnackbar = () => {
    snackbar.value = {
      show: false,
      message: "",
    };
  };

  return {
    snackbar,
    showSnackbar,
    hideSnackbar,
  };
});
