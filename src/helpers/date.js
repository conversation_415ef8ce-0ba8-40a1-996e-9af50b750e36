const formatDate = (date, needTime = true) => {
  const options = { day: "2-digit", month: "short", year: "numeric" };
  const datePart = date.toLocaleDateString("en-GB", options).replace(/ /g, "-");
  if (!needTime) return `${datePart}`;
  const timePart = date.toLocaleTimeString("en-GB", {
    hour: "2-digit",
    minute: "2-digit"
  });
  return `${datePart} ${timePart}`;
};

const getCurrentDate = () => {
  const now = new Date();
  return formatDate(now);
};

const getCurrentDatePlus14Hours = () => {
  const now = new Date();
  const plus12Hours = new Date(now.getTime() + 14 * 60 * 60 * 1000);
  return formatDate(plus12Hours);
};

// format like '24 Jun 2025, 01:58:47 PM'
const dateTimeFormat = (timestamp) => {
  if (!timestamp || !timestamp._seconds) return "-";

  const date = new Date(timestamp._seconds * 1000 + Math.floor(timestamp._nanoseconds / 1_000_000));

  return new Intl.DateTimeFormat("en-IN", {
    year: "numeric",
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  }).format(date);
};

// format like '24 Jun 2025'
const dateFormat = (timestamp) => {
  if (!timestamp || !timestamp._seconds) return "-";

  const date = new Date(timestamp._seconds * 1000 + Math.floor(timestamp._nanoseconds / 1_000_000));

  return new Intl.DateTimeFormat("en-IN", {
    day: "2-digit",
    month: "short",
    year: "numeric" 
  }).format(date);
};

// format like '01:58:47 PM'
const timeFormat = (timestamp) => {
  if (!timestamp || !timestamp._seconds) return "-";

  const date = new Date(timestamp._seconds * 1000 + Math.floor(timestamp._nanoseconds / 1_000_000));

  return new Intl.DateTimeFormat("en-IN", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  }).format(date);
};

// format like '24 Jun 2025, 01:58:47 pm'
const formatTimestamp = (timeLine) => {
  const timestamp = timeLine.at(-1)?.time;
  if (!timestamp || !timestamp._seconds) return "-";
  const date = new Date(timestamp._seconds * 1000);
  return new Intl.DateTimeFormat("en-IN", {
    year: "numeric",
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  }).format(date);
};

// format like '24 Jun 2025'
const convertTimestamptoDate = (timestamp) => {
  if (!timestamp || !timestamp._seconds) return "-";
  const date = new Date(timestamp._seconds * 1000);
  return new Intl.DateTimeFormat("en-IN", {
    year: "numeric",
    month: "short",
    day: "2-digit"
  }).format(date);
};

// format like '24 Jun 2025'
const ConvertIOStoDate = (stringDate) => {
  const date = new Date(stringDate);
  return date.toLocaleDateString("en-IN", {
    day: "2-digit",
    month: "short",
    year: "numeric"
  });
};

// format like '24 Jun 2025, 01:58:47 pm'
const ConvertIOStoDateTime = (stringDate) => {
  const date = new Date(stringDate);
  return date.toLocaleDateString("en-IN", {
    day: "2-digit",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};

// format like '01:58:47 pm'
const ConvertIOStoTime = (stringDate) => {    
  const date = new Date(stringDate);
  return date.toLocaleTimeString("en-IN", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,   // ensures AM/PM format
  });
};

const getCurrentMonth = () => {
  return new Date().toLocaleString("default", { month: "long" });
};

export {
  getCurrentDate,
  getCurrentDatePlus14Hours,
  formatDate,
  formatTimestamp,
  convertTimestamptoDate,
  ConvertIOStoDate,
  ConvertIOStoDateTime,
  ConvertIOStoTime,
  dateTimeFormat,
  dateFormat,
  timeFormat,
  getCurrentMonth
};
