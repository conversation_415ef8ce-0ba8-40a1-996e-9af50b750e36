<template>
    <page-loader text="Checking your superpowers… 🦸" />
</template>

<script setup>
import { onMounted } from "vue";
import { useRouter } from "vue-router";

// Helpers
import { getAuthorizationToken } from "@/helpers/auth";
import globalData from "@/composables/global";

import PageLoader from "@/components/utils/PageLoader.vue";

const router = useRouter();

/**
 * Initialize user session.
 * Checks if authorization token exists.
 * If not, redirects to auth logout URL.
 * If yes, navigates to home page.
 */
const initSession = async () => {
  const token = getAuthorizationToken();

  if (!token) {
    console.log("No token found, redirecting to auth...");
    // Redirect to auth logout URL with client_id
    window.location.replace(`${globalData.$authUrl}?client_id=${globalData.$clientId}`);
  } else {
    console.log("Token found, proceeding to home page...");
    router.push("/"); // Navigate to home/dashboard
  }
};

// Run after component is mounted
onMounted(() => {
  initSession();
});
</script>

<style scoped>
</style>
