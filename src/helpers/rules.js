const rules = {
  email: (v) => {
    if (!v) return true;
    return (
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v) ||
      "E-mail Must be Valid"
    );
  },
  password: (v) => !!v || "Password is Required",
  tenantId: (v) => !!v || "Tenant ID is Required",
  require: (v) => !!v || "Field required",
  requireList: (v) => {
    if (Array.isArray(v)) return v.length > 0 || "Field is required";
    return !!v || "Field is required";
  },
  phoneRules: [
    (v) => {
      if (!v) return true;
      return /^[+|\-|0-9]+[0-9]$/.test(v) || "Enter a Valid Phone Number";
    },
    (v) => {
      if (!v) return true;
      return v.length === 10 || "Phone Number Must be 10 Characters";
    },
  ],
  positive: (v) => v >= 0 || "Must be Positive",
  min1: (v) => v >= 1 || "Minimum Value is 1",
  cinNumber: (v) => {
    if (!v) return true;
    const regex = /^[LU][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$/;
    return regex.test(v) || "Invalid CIN Number";
  },
  gstNumber: (v) => {
    if (!v) return true;
    const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return regex.test(v) || "Invalid GST Number";
  },
  panNumber: (v) => {
    if (!v) return true;
    const regex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return regex.test(v) || "Invalid PAN Number";
  },
  tinNumber: (v) => {
    if (!v) return true;
    const regex = /^(0[1-9]|1\d|2\d|3[0-7])\d{9}$/;
    return regex.test(v) || "Invalid TIN Number";
  },
  pinCode: (v) => {
    if (!v) return true;
    const regex = /^\d{6}$/;
    return regex.test(v) || "Invalid Pin Code";
  },
  unit: (v) => {
    if (!v) return true;
    const regex = /^[^\s]{1,10}$/;
    return regex.test(v) || "Invalid Unit Symbol";
  },
  unitLength: (v) => {
    if (!v) return true;
    return (v.length >= 1 && v.length <= 10) || "Must be 1 to 10 characters";
  },
  price: (v) => !isNaN(parseFloat(v)) || "Must be a Number",

  quantity: (v) => !v || !isNaN(parseFloat(v)) || "Quantity Must be a Number",

  maxLength: (length) => (v) =>
    !v || v.length <= length || `Max ${length} Characters`,
  minLength: (length) => (v) =>
    !v || v.length >= length || `Min ${length} Characters`,
  maxValue: (v, max = 0) => {
    if (!v) return true;
    return !max || (max && v <= max) || `Max ${max}`;
  },
};

export default rules;
