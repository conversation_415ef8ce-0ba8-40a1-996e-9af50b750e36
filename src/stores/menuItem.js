import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useMenuItemStore = defineStore("menuItem", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const menuItems = ref([]);

  const getMenuItems = computed(() => menuItems.value);
  const setMenuItems = (data) => {
    menuItems.value = data;
  };

  const fetchMenuItems = async () => {
    try {
      const response = await httpClient.get("menu-items");
      setMenuItems(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchMenuItemById = async (id, accountId) => {
    try {
      const response = await httpClient.get(`menu-items/${id}`, {
        params: { accountId }
      });
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateMenuItem = async (data) => {
    const accId = data.account.id;
    data.posId = data.posId ? data.posId : "123456";
    data.tenantId = tenantId.value;
    try {
      await httpClient.put(`menu-items/${data.id}`, data, {
        params: { accountId: accId }
      });
      await fetchMenuItems();
      showSnackbar("success", "Menu Item updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateRecipeUnit = (item, inventoryList, recipeList) => {
    if (!item.selectedItemType || !item.selectedItemName) return;
    let unit = "";

    if (item.selectedItemType === "Inventory") {
      const i = inventoryList.find((it) => it.id === item.selectedItemName);
      unit = i?.recipeUnit?.symbol || "";
    } else if (item.selectedItemType === "Recipe") {
      const r = recipeList.find((it) => it.id === item.selectedItemName);
      unit = r?.recipeUnit || "";
    }

    item.recipeUnit = { symbol: unit };
    item.servingLevels?.forEach((level) => {
      level.recipeUnit = unit;
    });
  };

  return {
    getMenuItems,
    setMenuItems,
    fetchMenuItems,
    fetchMenuItemById,
    updateMenuItem,
    updateRecipeUnit
  };
});
