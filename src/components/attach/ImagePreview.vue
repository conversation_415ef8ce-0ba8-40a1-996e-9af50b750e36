<template>
  <div class="d-flex align-center justify-center fill-height">
    <!-- LOADING INDICATOR -->
    <v-progress-circular v-if="loading" indeterminate color="primary" />

    <!-- SUCCESSFUL IMAGE PREVIEW -->
    <v-img v-else-if="!error" :src="signedUrl" cover height="100%" @error="onImgError">
      <template v-slot:placeholder>
        <div class="d-flex align-center justify-center fill-height">
          <v-progress-circular v-if="loading" indeterminate color="primary" />
        </div>
      </template>
    </v-img>

    <!-- FALLBACK NO PREVIEW -->
    <NoPreview v-else :file="file" />
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "@/stores/snackBar";

import NoPreview from "./NoPreview.vue";

const props = defineProps({
  file: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    required: true
  },
  itemId: {
    type: String
  }
});

const loading = ref(false);
const signedUrl = ref(null);
const error = ref(false);

const snackbarStore = useSnackbarStore();

/** Fetch signed URL for preview */
async function loadSignedUrl() {
  if (!props.file?.filePath) return;

  loading.value = true;
  error.value = false;

  try {
    const res = await httpClient.get(
      `/attachments/read-url?type=${props.type}&docId=${
        props.itemId
      }&filePath=${encodeURIComponent(props.file.filePath)}`
    );

    signedUrl.value = res.data?.fileUrl;

    if (!signedUrl.value) throw new Error("Missing preview URL");
  } catch (e) {
    console.error("Signed URL fetch failed", e);
    error.value = true;
  } finally {
    loading.value = false;
  }
}

/** Image cannot load → fallback */
function onImgError() {
  error.value = true;
}

watch(() => props.file, loadSignedUrl, { immediate: true });
</script>

<style scoped>
</style>
