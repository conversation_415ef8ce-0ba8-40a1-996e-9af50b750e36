<template>
  <div>
    <!-- Trigger <PERSON>ton -->
    <v-btn variant="tonal" color="primary" @click="dialog = true">
      <v-icon>mdi-chevron-down</v-icon>
    </v-btn>

    <!-- Dialog -->
    <v-dialog v-model="dialog" max-width="900" persistent scrollable>
      <v-card>
        <div
          class="d-flex justify-space-between align-center"
        >
          <div class="pb-2">
            <v-card-title class="pb-0"
              >Add workArea for direct indent</v-card-title
            >
            <v-card-subtitle>
              Choose work areas and specify quantities for each</v-card-subtitle
            >
          </div>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="dialog = false"
          />
        </div>

        <v-card-text>
          <v-card border rounded="lg" width="100%">
            <v-data-table
              class="table-bordered table-horizontal-scroll"
              :headers="headers"
              :items="editableItems"
              items-per-page="-1"
              fixed-header
              hide-default-footer
            >
              <!-- Editable quantity cells -->
              <template
                v-for="header in headers"
                v-slot:[`item.${header.key}`]="{ item }"
              >
                <v-text-field
                  v-if="header.editable"
                  v-model.number="item[header.key]"
                  type="number"
                  variant="outlined"
                  density="compact"
                  color="primary"
                  hide-details
                  @keydown.up.prevent
                  @keydown.down.prevent
                />
                <span v-else>{{ item[header.key] }} </span>
              </template>
            </v-data-table>
          </v-card>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions class="d-flex flex-column ma-2">
          <div class="d-flex justify-end ga-2 w-100">
            <v-btn color="primary" variant="flat" @click="submit">
              Apply
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  headers: {
    type: Array,
    default: () => [],
  },
});

const editableItems = ref([...props.items]);

const emit = defineEmits(["submit"]);

const dialog = ref(false);

const submit = () => {
  emit("submit", editableItems.value);
  dialog.value = false;
};

watch(
  () => props.items,
  (newItems) => {
    editableItems.value = newItems;
  }
);
</script>
