// filters.js
// Centralized filter configuration for different pages/modules

// Reusable filter definitions
const filtersConfig = {
  status: {
    componentId: "ToggleFilter",
    title: "Status",
    key: "status",
    items: [
      { name: "All", value: null },
      { name: "Active", value: true },
      { name: "In-active", value: false },
    ],
  },

  unitType: {
    componentId: "ToggleFilter",
    title: "Unit Type",
    key: "unitType",
    items: [
      { name: "All", value: null },
      { name: "Mass", value: "Mass" },
      { name: "Volume", value: "Volume" },
    ],
  },

  itemType: {
    componentId: "ToggleFilter",
    title: "Item Type",
    key: "itemType",
    items: [
      { name: "All", value: null },
      { name: "Bought", value: "bought" },
      { name: "Made", value: "made" },
    ],
  },

  locationType: {
    componentId: "ToggleFilter",
    title: "Location Type",
    key: "locationType",
    items: [
      { name: "All", value: null },
      { name: "Outlet", value: "OUTLET" },
      { name: "Central Kitchen", value: "CENTRAL KITCHEN" },
      { name: "Central Warehouse", value: "CENTRAL WAREHOUSE" },
    ],
  },

  recipeType: {
    componentId: "ToggleFilter",
    title: "Recipe Type",
    key: "recipeType",
    items: [
      { name: "All", value: null },
      { name: "Recipe", value: "recipe" },
      { name: "SubRecipe", value: "subRecipe" },
    ],
  },
};

// Filters per page/module 
export const locationsFilters = [filtersConfig.status, filtersConfig.locationType];
export const workAreaFilters = [filtersConfig.status];
export const tagsFilters = [filtersConfig.status];
export const vendorFilters = [filtersConfig.status];
export const categoriesFilters = [filtersConfig.status];
export const houseUnitsFilters = [filtersConfig.status, filtersConfig.unitType];
export const inventoryItemsFilters = [filtersConfig.status, filtersConfig.itemType];
export const recipesFilters = [filtersConfig.status, filtersConfig.recipeType];
export const usersFilters = [filtersConfig.status];
export const rolesFilters = [filtersConfig.status];
