<template>
  <!-- Profile menu dropdown -->
  <v-menu v-model="menu" :close-on-content-click="false" location="bottom">
    <!-- Activator: profile icon -->
    <template #activator="{ props }">
      <v-icon
        icon="mdi-account-circle"
        color="primary"
        size="x-large"
        class="me-2"
        v-bind="props"
      />
    </template>

    <!-- Menu content -->
    <v-card
      min-width="250px"
      prepend-icon="mdi-account-circle"
      :title="userName"
    >
      <template #item>
        <v-chip density="compact" label color="primary">{{
          tenant.roleName
        }}</v-chip>
      </template>
      <v-list>
        <v-divider />

        <v-list-item :to="{name: 'tenant-settings'}">
          <template #prepend>
            <v-icon icon="mdi-office-building-cog-outline" />
          </template>
          <v-list-item-title>Settings</v-list-item-title>
        </v-list-item>
        <v-divider />

        <!-- Switch Tenant (if multiple tenants) -->
        <v-list-item
          v-if="tenants.length > 1"
          @click="() => router.push('/select-tenant')"
        >
          <template #prepend>
            <v-icon icon="mdi-home-switch-outline" />
          </template>
          <v-list-item-title>Switch Tenant</v-list-item-title>
        </v-list-item>

        <v-divider v-if="tenants.length > 1" />

        <!-- Theme Changer -->
        <ThemeChanger />
        <v-divider />

        <!-- Sign Out -->
        <v-list-item @click="handleLogout">
          <template #prepend>
            <v-icon icon="mdi-logout" color="error" />
          </template>
          <v-list-item-title>Sign Out</v-list-item-title>
        </v-list-item>
      </v-list>
    </v-card>
  </v-menu>
</template>

<script setup>
// Vue imports
import { ref, inject } from "vue";
import { useRoute, useRouter } from "vue-router";

// App-specific imports
import { getTenants, getTenantId, getUser, logout } from "@/helpers/auth";
import ThemeChanger from "@/components/utils/ThemeChanger.vue";

// Tenant & User Info
const tenants = getTenants();
const tenant = tenants.find((t) => t.id === getTenantId.value) || {};
const userName = getUser()?.userName;

// Router & Confirm
const router = useRouter();
const route = useRoute();
const $confirm = inject("confirm");
const menu = ref(false);

// Logout handler
const handleLogout = async () => {
  if (!$confirm) {
    console.error("Global confirm dialog not available");
    return;
  }

  const confirmed = await $confirm("Are you sure you want to logout?");
  if (!confirmed) return;

  logout();
};
</script>

<style scoped>
/* Optional: add spacing or styling for v-list-item if needed */
</style>
