<template>
  <v-card border class="rounded-lg" :prepend-icon="widget.icon">
    <!-- Card title slot -->
    <template #title>
      <span class="text-subtitle-2">{{ widget.title }}</span>
      <v-btn v-if="widget.to" icon flat end size="small" :to="{name: widget.to}">
        <v-icon>mdi-open-in-new</v-icon>
      </v-btn>
    </template>
    <template #subtitle>
      <div class="text-caption text-lowercase">{{ widget.subtitle }}</div>
    </template>

    <!-- Card append slot: widget options -->
    <template #append>
      <widget-options v-model="viewMode" :helper="helper" />
    </template>

    <v-divider></v-divider>

    <v-card-text class="pa-0">
      <slot name="graph" v-if="graphMode">
        <!-- Graph view: shown for viewMode != 1 -->
        <widget-graphs
          :key="`graph-mode-${viewMode}`"
          :graph-type="widget.graphType"
          :graph-data="widget.dataset"
          :view-mode="viewMode"
          :top-n="widget.topN"
          :title="widget.byValue"
        ></widget-graphs>
      </slot>

      <slot name="list" v-else>
        <!-- List view: shown when viewMode == 1 -->
        <widget-list :key="`list-mode-${viewMode}`" :data="widget.dataset" :helper="helper"></widget-list>
      </slot>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { computed, ref } from "vue";
import WidgetOptions from "./WidgetOptions.vue";
import WidgetGraphs from "./WidgetGraphs.vue";
import WidgetList from "./WidgetList.vue";

const props = defineProps({
  widget: {
    type: Object,
    required: true
  }
});

// Helper object for options
const helper = computed(() => ({
  label: props.widget.title,
  qty: props.widget.byQty,
  value: props.widget.byValue
}));

const viewMode = ref(props.widget.viewMode || 2);

// Graph mode is true whenever viewMode is not 1
const graphMode = computed(() => viewMode.value !== 1);
</script>

<style scoped>
/* Optional scoped styles */
</style>
