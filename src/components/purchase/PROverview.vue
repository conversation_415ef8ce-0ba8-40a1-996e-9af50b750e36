<template>
  <v-card border rounded="lg">
    <v-expansion-panels variant="accordion" multiple flat focusable border>
      <!-- PR Details -->
      <v-expansion-panel v-if="data.id">
        <v-expansion-panel-title>
          #{{ data.prNumber }}
          <template #actions>
            <v-btn
              :color="getStatusColor(data.status)"
              variant="tonal"
              :text="data.status"
              size="small"
            />
            <v-icon class="ml-2" />
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="prDetails" />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<script setup>
import { getStatusColor } from "@/helpers/status";
import LabelValueView from "@/components/utils/LabelValueView.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const prDetails = [
  { label: "Location", value: props.data.location.name || "-" },
  {
    label: "Vendor",
    value: props.data.vendorType == "2" ? props.data.vendor.name : "-",
  },
  { label: "Delivery Date", value: props.data.deliveryDate || "-" },
  { label: "Created By", value: props.data.requestedBy.name || "-" },
];
</script>

<style scoped></style>
