<template>
  <v-container fluid class="px-0 mb-10">
    <v-card border rounded="lg" width="100%">
      <v-data-table
        class="table-bordered"
        :items="poList"
        :headers="headers"
        items-per-page="-1"
        hide-default-footer
      >
        <template #item.index="{ index }">{{ index + 1 }}</template>

        <template #item.itemName="{ item, index }">
          <formatted-item-name
            :item="item"
            show-foc
            editable
            @update-foc="$emit('edit', item, index)"
          ></formatted-item-name>
        </template>
        <template #item.orderedQty="{ item }">
          {{ item.orderedQty || item.quantity }}
        </template>
        <template #item.pendingQty="{ item }">
          {{ item.pendingQty || item.quantity - (item.receivedQty || 0) }}
        </template>

        <template #item.receivedQty="{ item, index }">
          <v-text-field
            v-if="props.selectedWorkArea.length <= 1"
            v-model.number="item.receivedQty"
            type="number"
            min="1"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item, 'receivedQty')"
            @update:model-value="$emit('edit', item, index)"
          >
          </v-text-field>
          <direct-issue-quantity
            v-else
            v-model.number="item.receivedQty"
            :item="item"
            :selectedWorkArea="selectedWorkArea"
            @submit="(v) => updateReceivedQty(v, item, index)"
          />
        </template>

        <template #item.purchaseUOM="{ item }">
          {{ item.pkgUOM || item.purchaseUOM }}
        </template>

        <template #item.unitCost="{ item, index }">
          <v-text-field
            v-model.number="item.unitCost"
            type="number"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item, 'unitCost')"
            @update:model-value="$emit('edit', item, index)"
          >
          </v-text-field>
        </template>

        <template #item.totalDiscount="{ item, index }">
          <v-text-field
            v-model.number="item.totalDiscount"
            type="number"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item, 'totalDiscount')"
            @update:model-value="$emit('edit', item, index)"
          >
          </v-text-field>
        </template>

        <template #item.totalCess="{ item, index }">
          <v-text-field
            v-model.number="item.totalCess"
            type="number"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item, 'totalCess')"
            @update:model-value="$emit('edit', item, index)"
          ></v-text-field>
        </template>

        <template #item.taxRate="{ item, index }">
          <tax-field
            v-model="item.taxes"
            multiple
            return-object
            :hint="`Total: ${String(selectedTaxAmount(item))}%`"
            persistent-hint
            @update:model-value="$emit('edit', item, index)"
          ></tax-field>
        </template>
        <template #item.totalExclusiveTax="{ item }">
          {{ truncateNumber(item.netAmount) }}
        </template>

        <template #item.taxAmount="{ item }">
          {{ truncateNumber(item.totalTaxAmount) }}
        </template>

        <template #item.totalInclusiveTax="{ item }">{{
          truncateNumber(item.totalAmount)
        }}</template>

        <template #bottom>
          <cart-summary
            editable
            :charges="cart.charges"
            :data="{
              total: cart.totalAmount,
              subtotal: cart.netAmount,
              discount: cart.totalDiscount,
              focAmount: cart.totalFocAmount,
              transportCharge: 0,
              otherCharges: 0,
              totalTaxAmount: cart.totalTaxAmount,
            }"
            @update-charges="$emit('calculate')"
          />
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>

<script setup>
import DirectIssueQuantity from "./DirectIssueQuantity.vue";
import { truncateNumber } from "@/helpers/money";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import CartSummary from "./CartSummary.vue";
import TaxField from "@/components/fields/TaxField.vue";

const props = defineProps({
  poList: {
    type: Array,
    default: () => [],
  },
  headers: {
    type: Array,
    default: () => [],
  },
  selectedWorkArea: {
    type: Array,
    default: () => [],
  },
  cart: {
    type: Object,
    default: () => {},
  },
});

const emit = defineEmits(["edit", "updateQty", "calculate"]);

const selectedTaxAmount = (item) => {
  return item.taxes.reduce((acc, tax) => {
    return acc + tax.value;
  }, 0);
};

const updateReceivedQty = (v, item, index) => {
  emit("edit", item, index);
  emit("updateQty", v);
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Special rule: quantity must always be at least 1
  if (field === "receivedQty") {
    if (item.receivedQty < 1 || !item.receivedQty) {
      item.receivedQty = 1;
    }
    return;
  }

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }

  // Logical constraint: discount cannot exceed unit cost
  if (item.totalDiscount > item.unitCost * item.quantity) {
    item.totalDiscount = item.unitCost * item.quantity;
  }
};

defineExpose({
  get exposedTotalPrice() {
    return totalPrice.value;
  },
  get exposedTotalTaxAmount() {
    return totalTaxAmount.value;
  },
  get exposedTotalExclusiveTax() {
    return totalExclusiveTax.value;
  },
  get exposedTotalDiscount() {
    return totalDiscount.value;
  },
  get exposedTotalFocAmount() {
    return totalFocAmount.value;
  },
  get exposedTotalCess() {
    return totalCess.value;
  },
});
</script>
