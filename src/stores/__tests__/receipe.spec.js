import { setActivePinia, createPinia } from "pinia";
import { describe, beforeEach, it, expect, vi } from "vitest";
import { useReceipeStore } from "@/stores/receipe";
import httpClient from "@/plugin/Axios";

// Mock snackbar
const showSnackbarMock = vi.fn();

vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

function mockAxiosError(message) {
  return {
    response: {
      data: { message },
    },
  };
}

describe("Receipe Store", () => {
  let receipeStore;

  beforeEach(() => {
    setActivePinia(createPinia());
    receipeStore = useReceipeStore();
    vi.clearAllMocks();
  });

  describe("fetchReceipes", () => {
    it("should fetch and set receipes", async () => {
      const mockData = [{ id: 1, name: "Test Receipe" }];
      httpClient.get.mockResolvedValueOnce({ data: mockData });

      await receipeStore.fetchReceipes();

      expect(receipeStore.getReceipes).toEqual(mockData);
      expect(httpClient.get).toHaveBeenCalledWith(
        expect.stringContaining("/receipes")
      );
    });

    it("should handle fetch error", async () => {
      const errorMessage = "Fetch error";
      httpClient.get.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.fetchReceipes()).rejects.toThrow(errorMessage);
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("fetchReceipeById", () => {
    it("should return single receipe data", async () => {
      const id = 1;
      const mockReceipe = { id, name: "Sample" };
      httpClient.get.mockResolvedValueOnce({ data: mockReceipe });

      const result = await receipeStore.fetchReceipeById(id);

      expect(result).toEqual(mockReceipe);
      expect(httpClient.get).toHaveBeenCalledWith(
        expect.stringContaining(`/receipes/${id}`)
      );
    });

    it("should handle error on fetching by ID", async () => {
      const errorMessage = "Fetch by ID failed";
      httpClient.get.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.fetchReceipeById(999)).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("createReceipe", () => {
    it("should create receipe and fetch updated list", async () => {
      const newReceipe = { name: "New Receipe" };
      httpClient.post.mockResolvedValueOnce({});
      httpClient.get.mockResolvedValueOnce({ data: [newReceipe] });

      await receipeStore.createReceipe(newReceipe);

      expect(httpClient.post).toHaveBeenCalledWith(
        expect.stringContaining("/receipes"),
        newReceipe
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Receipe created successfully"
      );
    });

    it("should handle creation error", async () => {
      const errorMessage = "Creation failed";
      httpClient.post.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.createReceipe({})).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("updateReceipe", () => {
    it("should update receipe and fetch updated list", async () => {
      const updatedReceipe = { id: 1, name: "Updated" };
      httpClient.put.mockResolvedValueOnce({});
      httpClient.get.mockResolvedValueOnce({ data: [updatedReceipe] });

      await receipeStore.updateReceipe(updatedReceipe);

      expect(httpClient.put).toHaveBeenCalledWith(
        expect.stringContaining(`/receipes/${updatedReceipe.id}`),
        updatedReceipe
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Receipe updated successfully"
      );
    });

    it("should handle update error", async () => {
      const errorMessage = "Update failed";
      httpClient.put.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.updateReceipe({ id: 2 })).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("deleteReceipe", () => {
    it("should delete receipe and fetch updated list", async () => {
      const id = 7;
      httpClient.delete.mockResolvedValueOnce({});
      httpClient.get.mockResolvedValueOnce({ data: [] });

      await receipeStore.deleteReceipe(id);

      expect(httpClient.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/receipes/${id}`)
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Receipe deleted successfully"
      );
    });

    it("should handle delete error", async () => {
      const errorMessage = "Delete failed";
      httpClient.delete.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.deleteReceipe(99)).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });
});
