<template>
  <div>
    <!-- Report Header -->
    <ReportHeader
      v-model="payload"
      enable-date
      enable-vendors
      enable-items
      :loading="loading"
      @search="onSearch"
    />

    <!-- Inventory Dashboard -->
    <InventoryDashboard :loading="loading" :dashboardData="dashboardData" />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

import httpClient from "@/plugin/Axios";
import { NewReportPayload } from "@/helpers/useReportPayload";

import ReportHeader from "@/components/utils/ReportHeader.vue";
import InventoryDashboard from "@/components/dashboard/InventoryDashboard.vue";

const loading = ref(false);
const payload = ref(NewReportPayload("inventory-dashboard"));
const dashboardData = ref({
  openingStock: 0,
  closingStock: 0,
  purchasesVendor: 0,
  ibtIn: 0,
  returnsFromWork: 0,
  indentConsumed: 0,
  ibtOut: 0,
  returnsVendor: 0,
  spoilageLoss: 0,
  activeSkus: 0,
  netMovement: 0,
  turnoverEfficiency: 0,
  financialOverview: [],
  rupeeValueTracking: [],
  varianceAction: [],
  riskIntelligence: [],
  topInvestmentItems: [],
  categoryPortfolio: []
});

const onSearch = async (reqPayload) => {
  loading.value = true;
  dashboardData.value = {};
  try {
    const res = await httpClient.post("/dashboards/inventory", reqPayload);
    dashboardData.value = res.data;
  } catch ({ response }) {
    // @todo:
    throw Error(response.data.message);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  onSearch(payload.value);
});
</script>
