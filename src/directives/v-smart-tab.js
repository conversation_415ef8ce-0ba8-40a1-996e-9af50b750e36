import { nextTick } from "vue";

/**
 * Mounted hook for v-smart-tab directive.
 *
 * This hook is used to enhance tabbing experience within a form.
 * It listens for Enter and Tab key presses and moves focus to the next or previous focusable element.
 * It also works with Vuetify's validation system.
 *
 * The hook also exposes several methods via the binding.value object:
 * - focusNext(current): move focus to the next focusable element
 * - focusPrev(current): move focus to the previous focusable element
 * - focusFirst(): move focus to the first focusable element
 */
export default {
    mounted(el, binding) {
        el.dataset.smartTab = true;
        const KEY_ENTER = 'Enter';
        const KEY_TAB = 'Tab';

        const selector = binding.value?.selector ||
            `input:not([disabled]):not([readonly]):not([type="hidden"]),
             textarea:not([disabled]):not([readonly])`

        const getFocusable = () =>
            Array.from(el.querySelectorAll(selector)).filter(
                el =>
                    !el.disabled &&
                    !el.readOnly &&
                    el.offsetParent !== null &&
                    el.tabIndex >= 0
            );

        const moveFocus = (current, reverse = false, key) => {
            const focusable = getFocusable();
            const index = focusable.indexOf(current);
            if (index === -1) return false;

            let nextIndex = reverse ? index - 1 : index + 1;

            // If at the end and moving forward → submit
            if (nextIndex >= focusable.length) {
                if (key === KEY_ENTER) {
                    if (typeof binding.value?.onSubmit === 'function') {
                        binding.value.onSubmit();
                        return true;
                    }
                }
                return false;
            }

            // If at the start and moving backward → stay put
            if (reverse && nextIndex < 0) return false;

            focusable[nextIndex]?.focus();
            return true;
        };

        const handler = e => {
            if (![KEY_ENTER, KEY_TAB].includes(e.key)) return;

            const reverse = e.shiftKey && e.key === KEY_TAB;
            const current = e.target;

            // Vuetify validation if available
            const vInput = current.closest('.v-input')?.__vueParentComponent?.ctx;
            if (vInput?.validate && !vInput.validate()) {
                e.preventDefault();
                return;
            }

            const moved = moveFocus(current, reverse, e.key);
            if (moved) e.preventDefault();
        };

        el.addEventListener('keydown', handler);

        const navigate = async (current, reverse = false) => {
            await nextTick();
            moveFocus(current, reverse);
        };

        // Programmatic API
        if (binding.value && typeof binding.value === 'object') {
            binding.value.focusNext = (current = document.activeElement) =>
                navigate(current, false);
            binding.value.focusPrev = (current = document.activeElement) =>
                navigate(current, true);
            binding.value.focusFirst = () => {
                nextTick(() => {
                    const focusable = getFocusable();
                    focusable[0]?.focus();
                })
            };
        }
    },
};

