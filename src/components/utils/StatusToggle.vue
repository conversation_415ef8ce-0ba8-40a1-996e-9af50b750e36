<template>
  <div>
    <slot name="toggle">
      <v-switch
        density="compact"
        color="success"
        base-color="error"
        ripple
        inset
        hide-details
        :loading="loading"
        :model-value="status"
        @click.prevent="onToggle"
      />
    </slot>
  </div>
</template>

<script setup>
import { inject, ref } from "vue";
import { invokeApi } from "@/plugin/Axios";

const props = defineProps({
  status: { type: Boolean, default: false },
  id: { type: [String, Number], required: false },
  name: { type: String, required: true },
  entity: { type: String },
  noConfirm: { type: Boolean, default: false },
  activateUrl: { type: String, default: "" },
  deactivateUrl: { type: String, default: "" },
  params: { type: Object, default: () => ({}) } // extra params
});

const emit = defineEmits(["update", "toggle"]);
const loading = ref(false);
const $confirm = inject("confirm");

// Handle toggle action with optional confirmation and API call.

async function onToggle() {
  const newStatus = !props.status; // Determine new status (toggle)

  // If confirmation is required
  if (!props.noConfirm) {
    if (!$confirm) {
      console.error("Global confirm dialog not available");
      return;
    }

    const action = newStatus ? "activate" : "deactivate";
    const confirmMessage = `Are you sure you want to <b>${action}</b> ${
      props.entity ? `the ${props.entity} ` : ""
    } <b>${props.name}</b>?`;

    const ok = await $confirm(confirmMessage);
    if (!ok) {
      return; // Exit if user cancels
    }
  }

  // Resolve URL with params (if any)
  const url = newStatus ? props.activateUrl : props.deactivateUrl;

  // If no URL, just emit update (manual mode)
  if (!url) {
    emit("update", newStatus);
    emit("toggle", newStatus); //fallback, @todo: remove once updated everywhere
    return;
  }

  try {
    loading.value = true;
    // Call API to update status using helper
    await invokeApi({
      method: "put",
      url,
      params: {
        ...props.params,
        id: props.id
      },
      showSuccess: "Status updated successfully", // show server message on success
      showError: "Failed to update status" // show server message on error
    });

    emit("update", newStatus); // Notify parent to refresh data
  } catch (error) {
    // Error is already handled inside callApi (snackbar), optional extra handling here
    console.error("Failed to update status", error);
  } finally {
    loading.value = false;
  }
}
</script>
