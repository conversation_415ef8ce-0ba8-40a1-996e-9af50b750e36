<template>
  <!-- Edit Mode -->
  <div v-if="edit">
    <v-textarea
      v-model="localValue"
      :label="title"
      variant="outlined"
      color="primary"
      single-line
      rows="4"
      auto-grow
      hide-details
      @update:model-value="(val) => $emit('update:modelValue', val)"
    />
  </div>

  <!-- View Mode -->
  <div v-else>
    <div v-if="modelValue" class="whitespace-pre-line">{{ modelValue }}</div>
    <div v-else class="text-medium-emphasis">— No content available —</div>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  modelValue: {
    type: String,
    default: ""
  },
  edit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["update:modelValue"]);

const localValue = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val)
});
</script>

<style scoped>
.whitespace-pre-line {
  white-space: pre-line;
}
</style>
