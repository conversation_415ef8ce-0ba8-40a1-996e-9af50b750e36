<template>
  <!-- Metric card -->
  <v-card
    border
    rounded="lg"
    class="flex-grow-1"
    :color="item.color"
    :variant="!item.highlight ? 'tonal' : 'elevated'"
  >
    <!-- Optional avatar/icon at the top -->
    <template #append>
      <v-avatar
        :color="!item.highlight? item.color: 'currentColor'"
        :icon="item.icon"
        variant="text"
      />
    </template>

    <!-- Card title: shows the value -->
    <template #title>
      <v-skeleton-loader type="list-item" v-if="loading"></v-skeleton-loader>
      <span
        v-else
        class="text-h5 font-weight-bold"
        :class="{'text-surface-variant': !item.highlight}"
      >{{ item.value }}</span>
    </template>

    <!-- Card body: label and description -->
    <v-card-item :class="{'text-surface-variant': !item.highlight}">
      <div>
        <div class="text-subtitle-2 mb-1">
          {{ item.title }}
          <v-btn 
          v-if="item.to" 
          icon="mdi-open-in-new" 
          variant="text" end size="small" :to="{name: item.to}" />
        </div>
        <div class="text-caption text-lowercase">{{ item.subtitle }}</div>
      </div>
    </v-card-item>
  </v-card>
</template>

<script setup>
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});
</script>

<style scoped>
</style>
