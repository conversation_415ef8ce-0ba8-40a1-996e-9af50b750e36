<template>
  <auto-complete
    v-model="model"
    :items="filteredHouseUnits"
    v-bind="$attrs"
    :label="label"
    item-title="name"
    item-value="id"
    :loading="loading"
    :filter-keys="['title', 'raw.symbol']"
  >
    <template v-slot:item="{ props, item }">
      <v-list-item
        v-bind="props"
        :title="`${item.raw.name} (${item.raw.symbol})`"
      >
      </v-list-item>
    </template>
    <template #selection="{ item }">
      <span v-if="showConversion">
        {{ getConversionFactor(item.raw.symbol) }}&nbsp;x&nbsp;</span
      >
      {{ item.raw.name }} ({{ item.raw.symbol }})
    </template>

    <!-- <template #prepend-inner>
      <span v-if="countingUnit?.conversion" class="d-flex"
        >{{ countingUnit?.conversion }}&nbsp; <span>x</span>
      </span>
    </template> -->
  </auto-complete>
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";
import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "House Units",
  },
  /** Component-specific attributes */
  purchaseUnit: {
    type: Object,
    default: () => null,
  },
  parentUnit: {
    type: Object,
    default: () => null,
  },
  showConversion: {
    type: Boolean,
    default: false,
  },
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const houseUnits = computed(() => {
  return masterStore.getHouseUnits({});
});

const filteredHouseUnits = computed(() => {
  let list = houseUnits.value;

  if (props.parentUnit?.symbol) {
    list = getConversionChain(props.parentUnit?.symbol);
  }

  const grouped = [];
  const custom = [];
  const standard = [];

  list.forEach((u) => {
    if (u.default) return standard.push(u);
    return custom.push(u);
  });

  if (standard.length) {
    grouped.push({ type: "subheader", name: "STANDARD UNIT" }, ...standard, {
      type: "divider",
      text: "or",
    });
  }

  if (custom.length) {
    grouped.push({ type: "subheader", name: "HOUSE UNIT" }, ...custom);
  }

  return grouped;
});

const getConversionChain = (startSymbol) => {
  // Internal: Convert array into a map for O(1) lookup
  const unitMap = {};
  houseUnits.value.forEach((u) => {
    unitMap[u.symbol] = u;
  });

  const chain = [];
  const visited = new Set();

  let current = startSymbol;

  while (current && !visited.has(current)) {
    visited.add(current);

    const unit = unitMap[current];
    if (!unit) break;

    chain.push(unit); // Add current unit to result chain
    current = unit.toUnit; // Move deeper (kg → g → mg)
  }

  return chain;
};

const getConversionFactor = (symbol) => {
  const purchase = props.purchaseUnit;
  const units = houseUnits.value; // full list of all units

  if (!purchase) return 1;

  // If it's the same as purchase unit
  if (purchase.symbol === symbol) {
    return 1;
  }

  let factor = 1;
  let current = purchase;

  // Walk down the conversion chain until we reach target symbol
  while (current && current.toUnit) {
    factor *= current.quantity;

    // Reached the target unit
    if (current.toUnit === symbol) {
      return factor;
    }

    // Move to the next unit in the chain
    current = units.find((u) => u.symbol === current.toUnit);
  }

  // If no chain matches, fallback
  return 1;
};

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});
</script>
