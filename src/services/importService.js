import <PERSON> from "papaparse";
import * as XLSX from "xlsx";
import httpClient from "@/plugin/Axios";
import { getTenantId } from "@/helpers/auth";
import { ref } from "vue";

const tenantId = ref(getTenantId);

const url = `/import-product-configuration`;

/**
 * Imports sheet data from a file and posts it to the backend.
 * @param {Object} options
 * @param {File} options.file - The file to import (xlsx)
 * @param {string} options.sheetName - The sheet name to send to backend
 * @param {string} [options.endpoint] - The backend endpoint URL (optional, defaults to DEFAULT_URL)
 * @returns {Promise} Resolves with backend response or rejects with error
 */
export function importSheetData({ file, sheetName, endpoint = url }) {
  return new Promise((resolve, reject) => {
    const fileName = file.name.toLowerCase();

    if (fileName.endsWith(".xlsx")) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("sheetName", sheetName);
      formData.append("tenantId", tenantId.value);

      httpClient
        .post(`${endpoint}?sheets=${sheetName}`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((res) => resolve(res))
        .catch((err) => reject(err));
    } else {
      reject(new Error("Only .xlsx files are supported for import."));
    }
  });
}

export default {
  importSheetData,
};
