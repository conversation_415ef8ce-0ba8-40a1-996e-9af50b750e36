<template>
  <div>
    <v-card-actions class="px-4 pb-4 mt-4">
      <v-row>
        <v-col cols="8">
          <v-text-field
            v-model="roleName"
            label="Role Name"
            color="primary"
            hide-details="auto"
            variant="outlined"
            density="compact"
            :rules="[rules.require]"
            class="text-left"
          />
        </v-col>
      </v-row>
      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary">
        <v-icon>mdi-close</v-icon>Close
      </v-btn>
      <v-btn
        :disabled="!roleName.trim()"
        text="Save"
        @click="onSave"
        variant="flat"
        color="primary"
      />
    </v-card-actions>

    <v-divider />

    <v-container fluid>
      <v-row no-gutters>
        <v-card border class="lg" width="100%">
            <!-- <v-data-table
              class="table-bordered"
              :headers="rolePrivilegeHeaders"
              :items="privileges"
              item-value="name"
              :expanded="expandedRows"
              hide-default-footer
              :loading="loading"
              no-data-text="No privileges found"
            >
              <template v-slot:item="{ item }">
                <tr class="">
                  <td>
                    <v-icon
                      size="small"
                      class="expand-icon mr-2 cursor-pointer"
                      @click.stop="toggleExpand(item.name)"
                    >
                      {{
                        expandedRows.includes(item.name)
                          ? "mdi-chevron-down"
                          : "mdi-chevron-right"
                      }}
                    </v-icon>
                  </td>
                  <td class="pl-8 d-flex align-center">
                    <span
                      class="cursor-pointer font-weight-bold text-subtitle-2"
                      @click="toggleExpand(item.name)"
                    >
                      {{ item.name }}
                    </span>
                  </td>
                  <td>All</td>

                  <td class="text-end">
                    <div class="d-inline-flex">
                      <v-switch
                        v-model="item.checked" 
                        color="success"
                        inset
                        density="compact"
                        hide-details
                        @update:model-value="onGroupToggle(item)"
                      />
                    </div>
                  </td>
                </tr>

                <tr
                  v-for="(priv, index) in item.privileges"
                  :key="`${item.name}-${index}`"
                  v-if="expandedRows.includes(item.name)"
                >
                  <td></td>
                  <td class="pl-12">{{ priv.label }}</td>

                  <td>{{ priv.description }}</td>

                  <td class="text-end">
                    <div class="d-inline-flex">
                      <v-switch
                        v-model="priv.checked"
                        color="success"
                        inset
                        density="compact"
                        hide-details
                        @update:model-value="onPrivilegeToggle(priv, item)"
                      />
                    </div>
                  </td>
                </tr>
              </template>
            </v-data-table> -->
            <v-data-table
              class="table-bordered"
              :group-by="groupBy"
              :headers="rolePrivilegeHeaders"
              :items="privileges"
              :items-per-page="-1"
              item-value="code"
              hide-default-footer
            >
              <!-- ✅ Group Header with switch -->
              <template v-slot:group-header="{ item, columns, toggleGroup, isGroupOpen }">
                <tr>
                  <td :colspan="columns.length" class="py-2 px-3">
                    <div
                      class="d-flex align-center justify-space-between w-100"
                      v-ripple
                      @click="toggleGroup(item)"
                    >
                      <div class="d-flex align-center">
                        <v-btn
                          :icon="isGroupOpen(item) ? '$expand' : '$next'"
                          color="medium-emphasis"
                          density="comfortable"
                          size="small"
                          variant="outlined"
                        ></v-btn>
                        <span class="ms-4">{{ item.value }}</span>
                      </div>

                      <!-- ✅ Group switch (stop click propagation) -->
                      <v-switch
                        v-model="groupSwitch[item.value]"
                        hide-details
                        inset
                        color="success"
                        density="compact"
                        @click.stop="toggleGroupPrivileges(item.value)"
                      ></v-switch>
                    </div>
                  </td>
                </tr>
              </template>

              <!-- ✅ Row-level switch -->
              <template v-slot:item.actions="{ item }">
              <div class="text-end d-inline-flex">
                <v-switch
                  v-model="item.checked"
                  hide-details
                  inset
                  color="success"
                  density="compact"
                ></v-switch>
              </div>
            </template>
            </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import rules from "@/helpers/rules";
import {
  getPrivileges,
  getRoleById,
  updateRole,
  createRole,
} from "@/services/roleService";
import { useSnackbarStore } from "@/stores/snackBar";
import { rolePrivilegeHeaders } from "@/helpers/tableHeaders";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";

const roleName = ref("");
const privileges = ref([]);
const loading = ref(false);

const router = useRouter();
const route = useRoute();
const isEdit = computed(() => !!route.params.id);
const snackbarStore = useSnackbarStore();

const expandedRows = ref([]);

const fetchPrivileges = async () => {
  loading.value = true;
  try {
    const res = await getPrivileges();

    if (res.status === "success" && res.payload) {
      privileges.value = Object.entries(res.payload).flatMap(([type, privs]) =>
        privs.map((p) => ({
          ...p,
          type,
          checked: false,
        }))
      );

      // ✅ initialize groups & expand all
      const types = [...new Set(privileges.value.map((p) => p.type))];
      groupSwitch.value = Object.fromEntries(types.map((t) => [t, false]));
      expandedRows.value = [...types]; // <— expand all
    }
  } finally {
    loading.value = false;
  }
};


const fetchRole = async (roleId) => {
  try {
    loading.value = true;
    const res = await getRoleById(roleId);

    if (res.status === "success" && res.payload) {
      roleName.value = res.payload.name;

      // Update each privilege’s checked state
      privileges.value = privileges.value.map((p) => ({
        ...p,
        checked: res.payload.privileges.includes(p.code),
      }));

      expandedRows.value = [...new Set(privileges.value.map((p) => p.type))];
    }
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

//const expandedRows = ref([...new Set(privileges.value.map((p) => p.type))]);

const groupSwitch = ref({});
privileges.value.forEach((p) => {
  if (!(p.type in groupSwitch.value)) groupSwitch.value[p.type] = false;
});

const toggleGroupPrivileges = (groupName) => {
  const newValue = !groupSwitch.value[groupName];
  groupSwitch.value[groupName] = newValue;

  privileges.value.forEach((p) => {
    if (p.type === groupName) p.checked = newValue;
  });
};

watch(
  privileges,
  (val) => {
    const grouped = {};
    val.forEach((p) => {
      if (!grouped[p.type]) grouped[p.type] = [];
      grouped[p.type].push(p);
    });

    for (const [type, items] of Object.entries(grouped)) {
      groupSwitch.value[type] = items.every((i) => i.checked);
    }
  },
  { deep: true }
);

const navigatePrevious = () => {
  router.back();
};

const onSave = async () => {
  loading.value = true;
  const selectedPrivileges = privileges.value
  .filter((p) => p.checked)
  .map((p) => p.code);


  try {
    const payload = {
      name: roleName.value,
      tenantId: localStorage.getItem("_tenantId"),
      privileges: selectedPrivileges,
    };

    const res = isEdit.value
      ? await updateRole(route.params.id, payload)
      : await createRole(payload);

    snackbarStore.showSnackbar(
      res.status === "success" ? "success" : "error",
      res.message ||
        (isEdit.value ? "Role update failed" : "Role creation failed")
    );

    if (res.status === "success") router.push({ name: "roles" });
  } catch (err) {
    snackbarStore.showSnackbar("error", err.message || "Something went wrong");
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  await fetchPrivileges();

  if (isEdit.value) {
    await fetchRole(route.params.id);
  } else {
    expandedRows.value = privileges.value.map((g) => g.name);
  }
});

const groupBy = [{ key: 'type', order: 'asc' }]


</script>
