<template>
  <v-menu>
    <template #activator="{ props }">
      <v-btn
        v-if="icon"
        v-bind="props"
        variant="text"
        icon="mdi-dots-vertical-circle-outline"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
      <v-btn
        v-else
        v-bind="props"
        variant="outlined"
        prepend-icon="mdi-dots-vertical"
        text="Options"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
    </template>

    <v-list class="pa-0">
      <div v-for="(action, index) in availableOptions" :key="action.id">
        <v-list-item
          @click="handleAction(action.id)"
          :prepend-icon="action.icon"
          :title="action.label"
        />
        <v-divider v-if="index < availableOptions.length - 1" />
      </div>
    </v-list>
  </v-menu>
  <ReasonDialog v-model="rejectDialog" @reject="reject"></ReasonDialog>
</template>

<script setup>
import { computed, inject, ref } from "vue";
import { purchaseStatus } from "@/constants/status";
import { PRIV_CODES } from "@/constants/privilegeCodes";
import { checkUserPrivilege } from "@/router/middleware";
import { useRouter } from "vue-router";

import ReasonDialog from "@/components/base/ReasonDialog.vue";
import { usePurchaseRequestStore } from "@/stores/purchaseRequest";
import httpClient from "@/plugin/Axios"; 

const purchaseRequestStore = usePurchaseRequestStore();

const props = defineProps({
  icon: {
    type: Boolean,
    default: false,
  }, // "icon" or "text"
  status: {
    type: String,
    required: true,
  },
  itemId: {
    type: [String, Number],
    required: true,
  },
});

const emit = defineEmits(["refresh"]);
const router = useRouter();

// Map status → allowed action IDs
const statusActionMap = {
  [purchaseStatus.DRAFT]: ["edit"],
  [purchaseStatus.SUBMITTED]: ["approve", "reject", "printPDF", "printXLS"],
  [purchaseStatus.APPROVED]: ["convert_po", "printPDF", "printXLS"],
  [purchaseStatus.COMPLETED]: ["printPDF", "printXLS"],
  [purchaseStatus.REJECTED]: [],
  [purchaseStatus.DELETED]: [],
};

// Actions with privilege codes
const actions = [
  {
    id: "edit",
    label: "Edit",
    icon: "mdi-pencil",
    privilege_code: PRIV_CODES.PUR_PR,
  },
  {
    id: "approve",
    label: "Approve",
    icon: "mdi-check-decagram",
    privilege_code: PRIV_CODES.PUR_APPROVE_PR,
  },
  {
    id: "reject",
    label: "Reject",
    icon: "mdi-close-octagon-outline",
    privilege_code: PRIV_CODES.PUR_APPROVE_PR,
  },
  {
    id: "convert_po",
    label: "Convert to PO",
    icon: "mdi-file-compare",
    privilege_code: PRIV_CODES.PUR_PO,
  },
  {
    id: "printPDF",
    label: "Export as PDF",
    icon: "mdi-file-pdf-box",
    privilege_code: PRIV_CODES.PUR_PR,
  },
  // {
  //   id: "printXLS",
  //   label: "Export as XLS",
  //   icon: "mdi-file-excel-box",
  //   privilege_code: PRIV_CODES.PUR_PR,
  // },
];

// Filter actions based on status & privilege
const availableOptions = computed(() => {
  const allowed = statusActionMap[props.status] || [];
  return actions.filter(
    (action) =>
      allowed.includes(action.id) && checkUserPrivilege(action.privilege_code)
  );
});

const convertPo = async () => {
  await purchaseRequestStore.convertToPurchaseOrder(props.itemId);
  router.push({ name: "purchase-orders" });
};

const handleAction = (actionId) => {
  switch (actionId) {
    case "edit":
      router.push({
        name: "edit-purchase-request",
        params: { id: props.itemId },
      });
      break;
    case "approve":
      handleApprove();
      break;
    case "reject":
      rejectDialog.value = true;
      break;
    case "convert_po":
      convertPo();
      break;
    case "printPDF":
      exportPDF();
      break;
    case "printXLS":
      exportPDF();
      break;
    default:
      break;
  }
};

const $confirm = inject("confirm");

const handleApprove = async () => {
  if (!$confirm) {
    console.error("Global confirm dialog not available");
    return;
  }

  const confirmed = await $confirm(
    "Are you sure you want to approve this purchase request?",
    { title: "Approve Purchase Request" }
  );
  if (!confirmed) return;
  await purchaseRequestStore.approvePurchaseRequest(props.itemId);
  emit("refresh");
};

const rejectDialog = ref(false);
const reject = async (reason) => {
  try {
    await purchaseRequestStore.rejectPurchaseRequest(props.itemId, reason);
    rejectDialog.value = false;
    emit("refresh");
  } catch (err) {
    console.error(err);
  }
};

const $loader = inject("loader");

const exportPDF = async () => {
  $loader.show("Please wait...");
  try {
    await httpClient.get(`purchase-requests/${props.itemId}/pdf`, { responseType: "blob" });
  } catch ({ response }) {
    console.error(response.data.message);
  } finally {
    $loader.hide();
  }
};

const disabled = computed(() => availableOptions.value.length === 0);
</script>
