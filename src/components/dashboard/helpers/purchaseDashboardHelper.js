// src/helpers/purchaseHelper.js

// --- Core Financial Metrics ---
export const metricDefs = [
  {
    id: "total",
    key: "total",
    title: "Total Purchase",
    subtitle: "Total purchase amount (base - discount + charge + tax)",
    icon: "mdi-trending-up",
    color: "#E91E63", // pink
    highlight: true,
    value: 0,
    to: "grn-report",
  },
  {
    id: "baseAmount",
    key: "baseAmount",
    title: "Base Price",
    subtitle: "Original price before discounts or charges",
    icon: "mdi-wallet-outline",
    color: "#F06292", // lighter pink
    value: 0,
  },
  {
    id: "discount",
    key: "discount",
    title: "Discount",
    subtitle: "Price reductions applied to the base amount",
    icon: "mdi-sale-outline",
    color: "#F8BBD0", // pastel pink
    value: 0,
  },
  {
    id: "foc",
    key: "foc",
    title: "FOC",
    subtitle: "Total value of free items",
    icon: "mdi-tag-heart-outline",
    color: "#EC407A", // darker pink
    value: 0,
  },
  {
    id: "charge",
    key: "charge",
    title: "Charges",
    subtitle: "Additional charges or fees on the order",
    icon: "mdi-cash-multiple",
    color: "#EC407A", // medium pink
    value: 0,
  },
  {
    id: "tax",
    key: "tax",
    title: "Tax",
    subtitle: "Applicable taxes on the final amount",
    icon: "mdi-receipt-text-outline",
    color: "#D81B60", // darker pink
    value: 0,
  },

  // --- Operational Metrics ---
  {
    id: "grn",
    key: "grn",
    title: "GRN",
    subtitle: "Number of GRNs recorded",
    icon: "mdi-invoice-text-multiple-outline",
    color: "#4CAF50", // green
    action: true,
    value: 0,
    to: "grn-report",
  },
  {
    id: "vendors",
    key: "vendors",
    title: "Vendors",
    subtitle: "Unique vendors from which purchases were made",
    icon: "mdi-account-group-outline",
    color: "#2196F3", // blue
    value: 0,
    to: "grn-vendor-wise-report",
  },
  {
    id: "uniqueItems",
    key: "uniqueItems",
    title: "Unique Items Purchased",
    subtitle: "Number of distinct items purchased",
    icon: "mdi-cube-outline",
    color: "#009688", // teal
    value: 0,
    to: "grn-item-wise-report",
  },
  {
    id: "locations",
    key: "locations",
    title: "Locations",
    subtitle: "Number of locations where purchase happened",
    icon: "mdi-map-marker-outline",
    color: "#3F51B5", // indigo
    value: 0,
    to: "grn-location-wise-report",
  },

  // --- Compliance / Quality Metrics ---
  /*{
    id: "hsnCompliant",
    key: "hsnCompliant",
    title: "HSN Compliance %",
    subtitle: "Percentage of HSN-compliant purchases",
    icon: "mdi-file-percent-outline",
    color: "#FF9800", // amber
    value: 0
  },
  {
    id: "returnPercent",
    key: "returnPercent",
    title: "Return %",
    subtitle: "Return to vendor value as a percentage of total",
    icon: "mdi-backup-restore",
    color: "#D32F2F", // Dark red
    value: 0
  }*/
];

// --- Widget Definitions ---
// src/helpers/widgetHelpers.js

export const widgetDefs = {
  dailyPurchaseTrends: {
    id: "dailyPurchaseTrends",
    to: "grn-daily-report",
    title: "Daily Purchase Trends",
    subtitle: "Financial Performance - Monitor daily spend & cash flow",
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "line",
    icon: "mdi-calendar-outline",
    color: "#42A5F5", // blue
    dataset: [],
  },
  topVendors: {
    id: "topVendors",
    title: "Top Vendors by Purchase Value",
    to: "grn-vendor-wise-report",
    subtitle:
      "Strategic Partnership Focus - Highlight vendor concentration risks",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 3,
    graphType: "pie",
    icon: "mdi-account-group-outline",
    color: "#FF7043", // orange
    dataset: [],
  },
  locationPurchaseAnalysis: {
    id: "locationPurchaseAnalysis",
    to: "grn-location-wise-report",
    title: "Location-wise Purchase Analysis",
    subtitle: "Compare purchasing activity across locations",
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "bar",
    icon: "mdi-map-marker-outline",
    color: "#26A69A", // teal
    dataset: [],
  },
  categoryPurchaseDistribution: {
    id: "categoryPurchaseDistribution",
    to: "grn-category-wise-report",
    title: "Category-wise Purchase Distribution",
    subtitle: "Operational view of where money is spent",
    byQty: "Count",
    byValue: "Value",
    viewMode: 3,
    graphType: "pie",
    icon: "mdi-shape-outline",
    color: "#FFB74D", // amber
    dataset: [],
  },
  subcategoryPurchaseDistribution: {
    id: "subcategoryPurchaseDistribution",
    to: "grn-sub-category-wise-report",
    title: "Subcategory-wise Purchase Distribution",
    subtitle: "Detailed view within categories",
    byQty: "Count",
    byValue: "Value",
    viewMode: 3,
    graphType: "pie",
    icon: "mdi-shape",
    color: "#4FC3F7", // light blue
    dataset: [], // only subcategory-level data
  },
  highValueItems: {
    id: "highValueItems",
    to: "grn-item-wise-report",
    title: "High-Value Items Analysis",
    subtitle: "Track items driving most of the purchase spend",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "pie",
    icon: "mdi-cube-outline",
    color: "#AB47BC", // purple
    dataset: [],
  },
  taxEfficiencyByCategory: {
    id: "taxEfficiencyByCategory",
    title: "Tax Efficiency Analysis by Category",
    subtitle: "Compare category-wise tax rates and optimize compliance",
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "bar",
    icon: "mdi-file-percent-outline",
    color: "#FFA726", // deep amber
    dataset: [],
  },
};
