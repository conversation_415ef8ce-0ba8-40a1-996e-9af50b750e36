<template>
  <v-navigation-drawer v-model="drawer" fixed location="start" width="330" elevation="1" color="nav">
    <!-- header tenant info  -->
    <template #prepend>
      <v-divider />
      <v-card class="w-100" tile :title="tenant.name" color="nav">
        <template #append>
          <v-btn
            variant="text"
            icon="mdi-office-building-cog-outline"
            color="primary"
            :to="{name: 'tenant-settings'}"
          />
        </template>
        <template #item>
          <v-row no-gutters>
            <v-col cols="12">
              <v-icon icon="mdi-account-circle" color="primary" start></v-icon>
              <span>{{ tenant.userName }}</span>
            </v-col>
            <v-col cols="12">
              <v-chip
                density="compact"
                class="mt-1"
                label
                color="primary"
                variant="elevated"
                size="small"
              >{{ tenant.roleName }}</v-chip>
            </v-col>
          </v-row>
        </template>
        <v-divider></v-divider>
      </v-card>
    </template>

    <!-- footer -->
    <template #append>
      <v-divider />
      <v-card
        class="mx-auto w-100" color="nav"
        tile
        subtitle="© 2025 Digitory, All Rights Reserved."
        :append-avatar="logo"
        href="https://digitory.com"
        target="_blank"
      ></v-card>
    </template>

    <v-list>
      <!-- Loop through all top-level menu items -->
      <template v-for="item in navigationMenus">
        <!-- Menu item with sub-items (dropdown group) -->
        <v-list-group v-if="item.subItems" :key="`nav--${item.id}`">
          <!-- Group activator (clickable parent) -->
          <template v-slot:activator="{ props }">
            <v-list-item v-bind="props" :prepend-icon="item.prependIcon" :title="item.title" />
          </template>

          <!-- Loop through sub-items -->
          <v-list-item
            v-for="subItem in item.subItems"
            :key="subItem.id"
            replace
            ripple
            :to="subItem.to"
            active-class="bg-primary"
            :disabled="subItem.disabled"
          >
            <template #prepend>
              <v-row>
                <v-col class="me-3">
                  <v-icon
                    style="opacity: var(--v-medium-emphasis-opacity);"
                  >{{ subItem.prependIcon }}</v-icon>
                </v-col>
              </v-row>
            </template>

            <template #title>
              <span class="text-body-2">{{ subItem.title }}</span>
            </template>
            <!-- Optional Add Action button for sub-items -->
            <template v-slot:append v-if="subItem.actionRoute">
              <v-tooltip location="end">
                <template v-slot:activator="{ props }">
                  <v-btn
                    v-bind="props"
                    density="comfortable"
                    :icon="subItem.appendIcon"
                    color="transparent"
                    variant="flat"
                    active-color="primary"
                    :to="{ name: subItem.actionRoute }"
                  />
                </template>
                <span>{{ subItem.actionRoute }}</span>
              </v-tooltip>
            </template>
          </v-list-item>
        </v-list-group>

        <!-- Single menu item (no sub-items) -->
        <v-list-item
          v-else
          :key="`single-menu--${item.id}`"
          :prepend-icon="item.prependIcon"
          replace
          ripple
          :to="item.to"
          active-class="bg-primary"
          :title="item.title"
          :disabled="item.disabled"
        >
          <!-- Optional Add Action button for sub-items -->
          <template v-slot:append v-if="item.actionRoute">
            <v-tooltip location="end">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  density="comfortable"
                  :icon="item.appendIcon"
                  color="transparent"
                  variant="flat"
                  active-color="primary"
                  :to="{ name: item.actionRoute }"
                />
              </template>
              <span>{{ item.actionRoute }}</span>
            </v-tooltip>
          </template>
        </v-list-item>
      </template>
    </v-list>
  </v-navigation-drawer>
</template>

<script setup>
import logo from "@/assets/images/logo.png";

// Helpers
import { getTenant } from "@/helpers/auth";
import { getNavigationMenus } from "@/router/navigationMenu";

// Tenant details
const tenant = getTenant();

// Drawer state (for navigation)
const drawer = defineModel();

// Get navigation menu based on tenant privileges
const navigationMenus = getNavigationMenus(tenant.isAdmin, tenant.privileges);
</script>

<style>
</style>
