<template>
  <v-container fluid class="px-0">
    <v-card border rounded="lg" width="100%">
      <v-data-table
        class="table-bordered"
        :items="items"
        :headers="purchaseOrderItemHeaders"
        items-per-page="-1"
        hide-default-footer
      >
        <template #item.index="{ index }">{{ index + 1 }}</template>

        <template #item.itemName="{ item }">
          <formatted-item-name :item="item"></formatted-item-name>
        </template>

        <template #item.orderedQty="{ item }">
          {{ item.quantity }}
        </template>

        <template #item.pendingQty="{ item }">
          {{ item.pendingQty || item.quantity - (item.receivedQty || 0) }}
        </template>

        <template #item.receivedQty="{ item }">
          {{ item.receivedQty }}
        </template>

        <template #item.purchaseUOM="{ item }">
          {{ item.pkgUOM || item.purchaseUOM }}
        </template>

        <template #item.unitCost="{ item }">
          {{ truncateNumber(item.unitCost) }}
        </template>

        <template #item.totalDiscount="{ item }">
          {{ truncateNumber(item.totalDiscount) }}
        </template>

        <template #item.taxRate="{ item }"> {{ item.taxRate }}% </template>

        <template #item.netAmount="{ item }">
          {{ truncateNumber(item.netAmount) }}
        </template>

        <template #item.totalTaxAmount="{ item }">
          {{ truncateNumber(item.totalTaxAmount) }}
        </template>

        <template #item.totalAmount="{ item }">{{
          truncateNumber(item.totalAmount)
        }}</template>

        <!-- 👇 Aligned Footer Totals -->
        <template #tfoot>
          <tr class="sticky-bottom-row">
            <!-- index -->
            <td></td>
            <!-- Item Name -->
            <td class="font-weight-bold">Total</td>
            <!-- stock -->
            <td></td>
            <!-- order quantity -->
            <td></td>
            <!-- pending quantity -->
            <td></td>
            <!--received quantity-->
            <td></td>
            <!-- UOM -->
            <td></td>
            <!-- unit cost -->
            <td></td>
            <!-- Discount amt -->
            <td></td>
            <!-- Cess -->
            <td></td>
            <!-- Tax Rate -->
            <td></td>
            <!-- Exclusive Tax Total -->
            <td class="text-end font-weight-bold pr-4">
              {{ truncateNumber(cart.netAmount) }}
            </td>
            <td class="text-end font-weight-bold pr-4">
              {{ truncateNumber(cart.totalTaxAmount) }}
            </td>
            <!-- Inclusive Tax Total -->
            <td class="text-end font-weight-bold pr-4">
              {{ truncateNumber(cart.totalAmount) }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>

<script setup>
import { truncateNumber } from "@/helpers/money";
import { getPurchaseItemHeaders } from "@/helpers/tableHeaders";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  cart: {
    type: Object,
    default: () => {},
  },
});

const purchaseOrderItemHeaders = getPurchaseItemHeaders({
  mode: "po",
  type: "view",
});
</script>
