<template>
  <div>
    <!-- Report Header -->
    <ReportHeader
      v-model="payload"
      enable-date
      enable-vendors
      enable-items
      :loading="loading"
      @search="onSearch"
    />

    <!-- Cogs Dashboard -->
    <CogsDashboard :loading="loading" :dashboardData="dashboardData" />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

import httpClient from "@/plugin/Axios";
import { NewReportPayload } from "@/helpers/useReportPayload";

import ReportHeader from "@/components/utils/ReportHeader.vue";
import CogsDashboard from "@/components/dashboard/CogsDashboard.vue";

const loading = ref(false);
const payload = ref(NewReportPayload("cogs-dashboard"));
const dashboardData = ref({
  totalRevenue: 0,
  totalCogs: 0,
  cogsRatio: 0,
  grossProfit: 0,
  performanceAnalysis: [],
  actionDashboard: [],
  reconciliation: [],
  transferWorkAreas: [],
  transferStore: [],
  costAnalysis: []
});

const onSearch = async (reqPayload) => {
  loading.value = true;
  dashboardData.value = {};
  try {
    const res = await httpClient.post("/dashboards/cogs", reqPayload);
    dashboardData.value = res.data;
  } catch ({ response }) {
    // @todo:
    throw Error(response.data.message);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  onSearch(payload.value);
});
</script>
