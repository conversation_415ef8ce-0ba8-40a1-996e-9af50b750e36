import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useContractStore = defineStore("contract", () => {
  const tenantId = ref(getTenantId);

  const { showSnackbar } = useSnackbarStore();
  const contracts = ref([]);

  const getContracts = computed(() => contracts.value || []);
  const setContracts = (data) => {
    contracts.value = data;
  };

  const fetchContracts = async () => {
    try {
      const response = await httpClient.get(`contracts`);
      setContracts(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchContractById = async (id) => {
    try {
      const response = await httpClient.get(`contracts/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createContract = async (data) => {
    try {
      const response = await httpClient.post("contracts", {
        ...data,
        tenantId: tenantId.value,
      });      
      const contractData = response.data;       
      showSnackbar("green", "Contract created successfully");
      return contractData; 
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateContract = async (data) => {
    try {
      await httpClient.put(`contracts/${data.id}`, {
        ...data,
        tenantId: tenantId.value,
      });
      showSnackbar("green", "Contract updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const closeContract = async (id) => {    
    try {
      await httpClient.put(`contracts/${id}/close`);
      showSnackbar("green", "Contract Closed successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    fetchContracts,
    fetchContractById,
    createContract,
    updateContract,
    getContracts,
    setContracts,
    closeContract
  };
});
