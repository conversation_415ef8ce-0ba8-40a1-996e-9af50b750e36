import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useClosingStore = defineStore("closing", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const closingData = ref([]);
  const closingItems = ref([]);

  const getClosingData = computed(() => closingData.value);
  const setClosingData = (data) => {
    closingData.value = data;
  };

  const getClosingItems = computed(() => closingItems.value);
  const setClosingItems = (data) => {
    closingItems.value = data;
  };

  const fetchClosingItems = async (data) => {
    try {
      const response = await httpClient.post("closing/items", data);
      setClosingItems(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchClosingById = async (id) => {
    try {
      const response = await httpClient.get(`closing/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchClosingData = async (filters = {}) => {
    try {
      const response = await httpClient.post("closing", filters);
      setClosingData(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createClosingData = async (data) => {
    const payload = { ...data };
    try {
      await httpClient.post("closing/create", payload);
      showSnackbar("green", "Closing created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    setClosingData,
    getClosingData,
    fetchClosingItems,
    createClosingData,
    getClosingItems,
    setClosingItems,
    fetchClosingData,
    fetchClosingById,
  };
});
