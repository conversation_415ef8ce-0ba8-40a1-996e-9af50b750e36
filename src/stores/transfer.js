import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useTransferStore = defineStore("transfer", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const transfers = ref([]);

  const getTransfers = computed(() => transfers.value || []);
  const setTransfers = (data) => {
    transfers.value = data;
  };

  const fetchTransfers = async (filters = {}) => {    
    try {
      const response = await httpClient.post("transfers", filters);
      setTransfers(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchTransferById = async (id, type) => {
    try {
      const response = await httpClient.get(`transfers/${id}`, { params: { type } });
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createTransfer = async (data) => {
    const payload = { ...data };
    try {
      await httpClient.post("transfers/create", payload);
      showSnackbar("green", "Transfer created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const dispatchTransfer = async (data) => {
    const payload = { ...data, tenantId: tenantId.value };
    try {
      await httpClient.put(`transfers/${data.id}/dispatch`, payload);
      showSnackbar("green", "Transfer Dispatched successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const receiveTransfer = async (data,dispatchId) => {    
    const payload = { ...data, tenantId: tenantId.value };    
    try {
      await httpClient.put(`transfers/${data.id}/receive`, payload, { params: { dispatchId } });
      showSnackbar("green", "Transfer Received successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const closeTransfer = async (id) => {
    try {
      await httpClient.put(`transfers/${id}/close`);
      showSnackbar("green", "Transfer Closed successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    setTransfers,
    getTransfers,
    fetchTransfers,
    fetchTransferById,
    createTransfer,
    dispatchTransfer,
    receiveTransfer,
    closeTransfer
  };
});
