<template>
  <v-expansion-panels focusable flat border tile>
    <v-expansion-panel>
      <v-expansion-panel-title
        collapse-icon="mdi-filter-variant-remove"
        expand-icon="mdi-filter-variant"
      >
        Filters
      </v-expansion-panel-title>

      <v-expansion-panel-text>
        <v-row>
          <!-- Category -->
          <v-col cols="12">
            <category-field v-model="filter.categories" multiple />
          </v-col>

          <!-- Sub-Category depends on categories -->
          <v-col cols="12">
            <sub-category-field
              v-model="filter.subCategories"
              :categories="filter.categories"
              multiple
              :disabled="!filter.categories.length"
            />
          </v-col>

          <!-- Vendor: auto-set when selectedVendorId provided externally -->
          <v-col cols="12" v-if="!noVendor">
            <vendor-field
              v-model="filter.vendors"
              multiple
              :disabled="!!selectedVendorId"
            />
          </v-col>
        </v-row>
      </v-expansion-panel-text>
    </v-expansion-panel>

    <v-divider />
  </v-expansion-panels>
</template>

<script setup>
import { ref, watch } from "vue";

// Child components
import CategoryField from "@/components/fields/CategoryField.vue";
import SubCategoryField from "@/components/fields/SubCategoryField.vue";
import VendorField from "@/components/fields/VendorField.vue";

/**
 * Props
 * modelValue: external filter object
 * selectedVendorId: optional restriction direct from parent context
 */
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      categories: [],
      subCategories: [],
      vendors: [],
    }),
  },
  selectedVendorId: {
    type: [String, Number, null],
    default: null,
  },
  noVendor: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:modelValue"]);

/**
 * Internal reactive filter model derived from modelValue.
 * Spread to avoid mutating parent directly.
 */
const filter = ref({ ...props.modelValue });

/**
 * Emit changes whenever internal filter updates.
 * deep:true ensures array mutations trigger emit.
 */
watch(
  filter,
  (val) => {
    emit("update:modelValue", { ...val });
  },
  { deep: true }
);

/**
 * Sync with external vendor restriction.
 * If selectedVendorId exists, lock filter to that vendor only.
 */
watch(
  () => props.selectedVendorId,
  (val) => {
    if (val) {
      filter.value.vendors = [val];
    } else {
      filter.value.vendors = [];
    }
  },
  { immediate: true }
);
</script>

<style scoped></style>
