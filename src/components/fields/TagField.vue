<template>
  <div>
    <auto-complete
      v-model="model"
      :items="tags"
      v-bind="$attrs"
      :label="label"
      item-title="name"
      item-value="id"
      :loading="loading"
      :multiple="multiple"
    >
    </auto-complete>
  </div>
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Tag",
  },
  multiple: {
    type: Boolean,
    default: false,
  },
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const tags = computed(() => {
  return masterStore.getTags();
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});
</script>
