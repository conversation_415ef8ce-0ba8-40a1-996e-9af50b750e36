<template>
  <v-card border rounded="lg" class="mb-2">
    <v-expansion-panels variant="accordion" multiple flat focusable border>
      <!-- Transfer Details -->
      <v-expansion-panel v-if="data.id">
        <v-expansion-panel-title>
          #{{ dispatchDetails.dispatchNo }}
          <template #actions>
            <v-btn
              :color="dispatchDetails.status === 'completed' ? 'success' : 'warning'"
              variant="tonal"
              size="small"
            >
              {{ dispatchDetails.status === 'completed' ? 'Received' : 'Dispatched' }}
            </v-btn>
            <v-icon class="ml-2" />
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="details" />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<script setup>
import { computed } from "vue";
import LabelValueView from "@/components/utils/LabelValueView.vue";
import { dateTimeFormat } from "@/helpers/date";

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  dispatchDetails: {
    type: Object,
    default: () => ({}),
  },
});

const transferDetails = [
    { label: "Dispatch No.", value: props.dispatchDetails.dispatchNo },
    { label: "Dispatched By", value: props.dispatchDetails?.dispatchedBy?.name },
    {
        label: "Dispatched At",
        value: dateTimeFormat(props.dispatchDetails?.dispatchedBy?.time),
    },
    { label: "Transfer No.", value: props.data.transferNumber },
    { label: "Requester", value: props.data.requester?.name },
    { label: "Issuer", value: props.data.issuer?.name },
    { label: "Requested By", value: props.data.requestedBy?.name },
    { label: "Requested At", value: props.data.requestedBy?.time },
];

const details = computed(() =>
  transferDetails.filter((detail) => detail.value)
);
</script>
