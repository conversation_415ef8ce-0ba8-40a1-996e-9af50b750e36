<template>
  <div>
    <v-fab
      v-if="!openNav"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel v-model="filter" :no-vendor="true" />

        <v-card-text>
          <v-form ref="form">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12" class="mb-2">
                  <inventory-item-field
                    ref="itemField"
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                  />
                </v-col>

                <v-divider v-if="selectedItem && Object.keys(selectedItem).length > 0" class="mb-5"/>

                <!-- PACKAGE SECTION -->
                <span v-if="selectedItem && Object.keys(selectedItem).length > 0" class="ml-3 mb-5 text-medium-emphasis">PACKAGE SECTION</span>

                <div
                  v-for="pkg in filteredPackages"
                  :key="pkg.id"
                  class="px-3"
                >
                  <v-text-field
                    v-model.number="formData.pkgQuantities[pkg.id]"
                    label="Closing Qty"
                    type="number"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData.pkgQuantities, pkg.id)"
                  >
                    <template #append-inner>
                      {{ pkg.id === "default" ? formData.countingUOM : pkg.name }}
                    </template>
                  </v-text-field>
                </div>

                <v-divider v-if="selectedItem && Object.keys(selectedItem).length > 0" class="mb-5"/>

                <!-- OPEN SECTION -->
                <span v-if="selectedItem && Object.keys(selectedItem).length > 0" class="ml-3 mb-5 text-medium-emphasis">OPEN SECTION</span>

                <div
                  v-for="(pkg, pkgIndex) in filteredPackages"
                  :key="pkg.id"
                  class="px-3 mb-3"
                >
                  <v-divider
                    v-if="pkgIndex > 0"
                    class="mb-4"
                  />

                  <div class="d-flex align-center mb-2">
                    <v-text-field
                      v-model.number="formData.openQuantities[pkg.id][0]"
                      label="Closing Qty"
                      type="number"
                      variant="outlined"
                      density="compact"
                      color="primary"
                      hide-details="auto"
                    >
                      <template #append-inner>
                        {{ pkg.id === 'default' ? formData.countingUOM : pkg.name }}
                      </template>
                    </v-text-field>

                    <v-btn
                      icon="mdi-plus"
                      color="primary"
                      variant="text"
                      @click="addQty(pkg.id)"
                    />
                  </div>

                  <div
                    v-for="(qty, index) in formData.openQuantities[pkg.id].slice(1)"
                    :key="index"
                    class="d-flex align-center mb-2"
                  >
                    <v-text-field
                      v-model.number="formData.openQuantities[pkg.id][index + 1]"
                      label="Closing Qty"
                      type="number"
                      variant="outlined"
                      density="compact"
                      color="primary"
                      hide-details="auto"
                    >
                      <template #append-inner>
                        {{ pkg.id === 'default' ? formData.countingUOM : pkg.name }}
                      </template>
                    </v-text-field>

                    <v-btn
                      icon="mdi-close"
                      color="error"
                      variant="text"
                      @click="removeQty(pkg.id, index + 1)"
                    />
                  </div>
                </div>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <!-- @todo: item summary -->
          <template #actions>
            <v-btn @click="handleSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { nextTick, ref, computed, onMounted, watch } from "vue";
import rules from "@/helpers/rules";
import { transferRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useInventoryItemStore } from "@/stores/inventoryItem";

// components
import InventoryFiltersPanel from "@/components/purchase/InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  locationId: {
    type: String,
    default: null,
  },
  existingItems: { type: Array, default: () => [] },
});

const inventoryStore = useInventoryItemStore();

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

formData.value = {
  ...DEFAULT_RECORD,
  pkgQuantities: {},
  openQuantities: {}   
};

const addQty = (pkgId) => {
  formData.value.openQuantities[pkgId].push(0);
};

const removeQty = (pkgId, index) => {
  if (index > 0) {
    formData.value.openQuantities[pkgId].splice(index, 1);
  }
};

const packageList = ref([]);
const selectedItem = ref([]);
const itemField = ref(null);

const getItemDetails = async (id) => {
  const item = await inventoryStore.fetchItemDetails({
    id: id,
    locationId: props.locationId,
    pkgId: "default",
  });

  formData.value.itemName = item.itemName;
  formData.value.itemCode = item.itemCode;
  formData.value.itemId = item.id;
  formData.value.categoryId = item.category?.id;
  formData.value.categoryName = item.category?.name;
  formData.value.subCategoryId = item.subCategory?.id;
  formData.value.subCategoryName = item.subCategory?.name;
  formData.value.unitCost = item.unitCost;
  formData.value.countingUOM = item.countingUnit.symbol;
  formData.value.requestedQuantity = item.requestedQuantity;

  packageList.value = [
    { name: `${item.purchaseUnit.symbol} (default)`, id: "default" },
    ...item.packages,
  ];

  formData.value.pkgQuantities = {};
  formData.value.openQuantities = {};

  packageList.value.forEach(pkg => {
    formData.value.pkgQuantities[pkg.id] = 0;
    formData.value.openQuantities[pkg.id] = [0];  
  });
};

const filteredPackages = computed(() => {
  if (!selectedItem.value || !selectedItem.value.id) {
    return [];
  }
  const selectedPkgs = props.existingItems
    .filter((i) => i.itemId === selectedItem.value.id)
    .map((i) => i.pkg?.id);
  return packageList.value.filter((pkg) => !selectedPkgs.includes(pkg.id));
});

const onSelectInventoryItem = async (selected) => {
  selectedItem.value = selected;
  if (!selected) return;
  getItemDetails(selected.id);
};

const itemNameField = ref(null);
const handleSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...formData.value };
  emit("add", payload);
  packageList.value = [];

  // ✅ Reset form data
  formData.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
  selectedItem.value = null;
  form.value.resetValidation();
  // ✅ Focus back to the first field
  await nextTick();
  itemNameField.value?.focus();
  focusFirstField();
};

const blurField = (obj, key) => {
  if (!obj || typeof key !== "string") return;

  if (obj[key] < 0 || !obj[key]) {
    obj[key] = 0;
  }
};

let hasFocusedInitially = false;

const focusFirstField = () => {
  if (itemField.value?.$el) {
    const input = itemField.value.$el.querySelector('input, .v-input input');
    if (input) input.focus();
  }
};

onMounted(async () => {
  await nextTick();
  focusFirstField();
  hasFocusedInitially = true;

  window.addEventListener('keydown', (e) => {
    if (e.key === 'Tab' && document.activeElement === document.body) {
      e.preventDefault();
      focusFirstField();
    }
  });
});

watch(openNav, async (val) => {
  if (val) {
    await nextTick()
    focusFirstField()   // same method you already have
  }
})

watch(filteredPackages, (pkgs) => {
  formData.value.pkg = pkgs[0];
});

</script>
