import { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from "pinia";
import { describe, beforeEach, it, vi, expect } from "vitest";
import httpClient from "@/plugin/Axios";
import { useHouseUnitStore } from "@/stores/houseUnit";

// Mock Snackbar
const showSnackbarMock = vi.fn();

// Axios mock
vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Snackbar store mock
vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

// Helper to mock axios error
function mockAxiosError(message) {
  return {
    response: {
      data: { message },
    },
  };
}

describe("House Unit Store", () => {
  let houseUnitStore;

  beforeEach(() => {
    setActivePinia(createPinia());
    houseUnitStore = useHouseUnitStore();

    httpClient.get.mockReset();
    httpClient.post.mockReset();
    httpClient.put.mockReset();
    httpClient.delete.mockReset();
    showSnackbarMock.mockReset();
  });

  describe("fetchHouseUnits", () => {
    it("should fetch all house units successfully", async () => {
      const mockData = [{ id: 1, name: "Unit A" }];
      httpClient.get.mockResolvedValue({ data: mockData });

      await houseUnitStore.fetchHouseUnits();

      expect(houseUnitStore.getHouseUnits).toEqual(mockData);
      expect(httpClient.get).toHaveBeenCalledWith(
        expect.stringContaining("/house-units")
      );
    });

    it("should handle error when fetching house units fails", async () => {
      const errorMessage = "Fetch failed";
      httpClient.get.mockRejectedValue(mockAxiosError(errorMessage));

      await expect(houseUnitStore.fetchHouseUnits()).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("createHouseUnits", () => {
    it("should create a house unit successfully", async () => {
      const unit = { name: "Unit B" };
      const mockData = [{ id: 1, name: "Unit B" }];

      httpClient.post.mockResolvedValueOnce({ status: 201 });
      httpClient.get.mockResolvedValueOnce({ data: mockData });

      await houseUnitStore.createHouseUnits(unit);

      expect(httpClient.post).toHaveBeenCalledWith(
        expect.stringContaining("/house-units"),
        expect.objectContaining(unit)
      );
      expect(httpClient.get).toHaveBeenCalled();
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "House Unit created successfully"
      );
    });

    it("should handle error when creating house unit fails", async () => {
      const unit = { name: "Fail Unit" };
      const errorMessage = "Create failed";

      httpClient.post.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(houseUnitStore.createHouseUnits(unit)).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("updateHouseUnits", () => {
    it("should update a house unit successfully", async () => {
      const updatedUnit = { id: 1, name: "Updated Unit" };
      const mockData = [updatedUnit];

      httpClient.put.mockResolvedValueOnce({ status: 200 });
      httpClient.get.mockResolvedValueOnce({ data: mockData });

      await houseUnitStore.updateHouseUnits(updatedUnit);

      expect(httpClient.put).toHaveBeenCalledWith(
        expect.stringContaining("/house-units/1"),
        expect.objectContaining(updatedUnit)
      );
      expect(httpClient.get).toHaveBeenCalled();
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "House Unit updated successfully"
      );
    });

    it("should handle error when updating house unit fails", async () => {
      const updatedUnit = { id: 1, name: "Fail Unit" };
      const errorMessage = "Update failed";

      httpClient.put.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(
        houseUnitStore.updateHouseUnits(updatedUnit)
      ).rejects.toThrow(errorMessage);
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("deleteHouseUnits", () => {
    it("should delete a house unit successfully", async () => {
      const id = 123;
      const mockData = [];

      httpClient.delete.mockResolvedValueOnce({ status: 200 });
      httpClient.get.mockResolvedValueOnce({ data: mockData });

      await houseUnitStore.deleteHouseUnits(id);

      expect(httpClient.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/house-units/${id}`)
      );
      expect(httpClient.get).toHaveBeenCalled();
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "House Unit deleted successfully"
      );
    });

    it("should handle error when deleting house unit fails", async () => {
      const id = 123;
      const errorMessage = "Delete failed";

      httpClient.delete.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(houseUnitStore.deleteHouseUnits(id)).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });
});
