import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useLocationStore = defineStore("location", () => {
  const { showSnackbar } = useSnackbarStore();
  const tenantId = ref(getTenantId);
  const Locations = ref([]);
  const selectedLocation = ref(null);
  const getSelectedLocation = computed(() => selectedLocation.value);
  const setSelectedLocation = (data) =>
    (selectedLocation.value = data || Locations.value[0]);


  const getLocations = computed(() => Locations.value);
  const setLocations = (data) => {
    Locations.value = data;
    if (!getSelectedLocation.value) setSelectedLocation(data[0]);
  };

  const fetchLocations = async () => {
    try {
      const response = await httpClient.get("inventory-locations");
      setLocations(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchLocationsByStore = async (storeId) => {
    try {
      const response = await httpClient.get(
        `inventory-locations/store-locations/${storeId}`
      );
      setLocations(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createLocation = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await httpClient.post("inventory-locations", data);
      showSnackbar("green", "Work/Storage Area Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const getLocationById = async (id) => {
    try {
      const response = await httpClient.get(`inventory-locations/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateLocation = async (data) => {
    try {
      await httpClient.put(`inventory-locations/${data.id}`, data);
      showSnackbar("green", "Work/Storage Area updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    Locations,
    getLocations,
    fetchLocations,
    fetchLocationsByStore,
    getLocationById,
    createLocation,
    updateLocation,
    // exportLocations,
    getSelectedLocation,
    setSelectedLocation
  };
});
