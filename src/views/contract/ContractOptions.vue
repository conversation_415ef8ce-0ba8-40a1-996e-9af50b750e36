<template>
  <v-menu>
    <template #activator="{ props }">
      <v-btn
        v-if="icon"
        v-bind="props"
        variant="text"
        icon="mdi-dots-vertical-circle-outline"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
      <v-btn
        v-else
        v-bind="props"
        variant="outlined"
        prepend-icon="mdi-dots-vertical"
        text="Options"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
    </template>

    <v-list class="pa-0">
      <div v-for="(action, index) in availableOptions" :key="action.id">
        <v-list-item
          :prepend-icon="action.icon"
          :title="action.label"
          :disabled="action.disabled"
          @click="handleAction(action.id)"
        />
        <v-divider v-if="index < availableOptions.length - 1" />
      </div>
    </v-list>
  </v-menu>

  <!-- Upload Dialog -->
  <attachment-upload-dialog
    v-model="showUploadDialog"
    type="contract"
    :item-id="item.id"
    :update-db-url="`/contracts/${item.id}/attachments`"
    @uploaded="onUploaded"
    add
  />
</template>

<script setup>
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { useContractStore } from "@/stores/contract";

import AttachmentUploadDialog from "@/components/attach/AttachmentUploadDialog.vue";

const contractStore = useContractStore();
const router = useRouter();
const showUploadDialog = ref(false);

const emit = defineEmits(["update"]);

const props = defineProps({
  icon: {
    type: Boolean,
    default: false
  }, // "icon" or "text"

  item: {
    type: Object,
    required: true
  }
});

const options = [
  {
    id: "edit",
    label: "Edit",
    icon: "mdi-square-edit-outline"
  },
  {
    id: "clone",
    label: "Clone",
    icon: "mdi-content-copy",
  },
  {
    id: "upload",
    label: "Attachment",
    icon: "mdi-paperclip-plus"
  },
  {
    id: "close",
    label: "Close",
    icon: "mdi-lock-outline"
  }
];

// Filter actions based on status & privilege
const availableOptions = computed(() => {
  if (props.item.status === "expired" || !props.item.activeStatus) {
    return options.filter(option => option.id === "clone");
  }
  return options;
});

const handleAction = async action => {
  if (action.disabled) return;

  switch (action) {
    case "close":
      await closeContract();
      break;

    case "edit":
      router.push({ name: "edit-contract", params: { id: props.item.id } });
      break;

    case "clone":
      router.push({ name: "create-contract", query: { cloneId: props.item.id } });
      break;

    case "upload":
      showUploadDialog.value = true;
      break;
  }
};

const closeContract = async () => {
  try {
    await contractStore.closeContract(props.item.id);
    await contractStore.fetchContracts();
  } catch (err) {
    console.error(err);
  }
};

const onUploaded = async () => {
  showUploadDialog.value = false;
  await contractStore.fetchContracts();
  emit("update");
};

const disabled = computed(() => availableOptions.value.length === 0);
</script>
