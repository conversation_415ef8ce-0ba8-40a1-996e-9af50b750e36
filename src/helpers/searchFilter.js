export const filterData = (list, query) => {
  return list.filter((item) => {
    return Object.values(item).some((val) => matchValue(val, query));
  });
};

export const matchValue = (val, query) => {
  if (val == null || val === "") return false;

  const type = typeof val;

  // Boolean / Number
  if (type === "boolean" || type === "number") {
    return val.toString().toLowerCase().includes(query);
  }

  // String
  if (type === "string") {
    return val.toLowerCase().includes(query);
  }

  // Array
  if (Array.isArray(val)) {
    return val.some((item) => matchValue(item, query));
  }

  // Object
  if (type === "object") {
    return Object.values(val).some((nestedVal) => matchValue(nestedVal, query));
  }

  return false;
};
