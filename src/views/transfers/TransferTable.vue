<template>
  <v-container fluid class="px-0 py-2">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="transferList"
          :headers="createTransferHeaders"
          items-per-page="-1"
          fixed-header
          hide-default-footer
        >
          <template #item.index="{ index }">{{ index + 1 }}</template>
          <template #item.actions="{ index }">
            <v-icon color="error" @click="$emit('removeItem', index)"
             @keydown.enter="$emit('removeItem', index)"
              >mdi-close</v-icon
            >
          </template>

          <template #item.itemName="{ item }">
            <formatted-item-name :item="item"></formatted-item-name>
          </template>

          <template #item.pkgName="{ item }">
            {{ item?.pkg.id === 'default' ? item.countingUOM : item?.pkg.name }}
          </template>

          <template #item.total="{ item }">
            {{ truncateNumber(item.requestedQuantity * item.unitCost) }}
          </template>

          <!-- 👇 Aligned Footer Totals -->
          <template #tfoot>
            <tr class="sticky-bottom-row">
              <!-- index -->
              <td></td>
              <!-- Item Name -->
              <td class="font-weight-bold">Total</td>
              <!-- stock -->
              <td></td>
              <!-- rate -->
              <td></td>
              <!-- requestedQuantity -->
              <td></td>
              <!-- Cost --> 
              <td></td>
              <!-- Total -->
              <td class="text-end font-weight-bold pr-4">{{ totalPrice }}</td>
              <!-- actions -->
              <td></td>
            </tr>
          </template>

          <template #no-data>
            <v-btn
              :disabled="!formValid"
              prepend-icon="mdi-plus"
              text="Add Item"
              variant="text"
              border
              rounded="xl"
              @click="$emit('addItem')"
            ></v-btn>
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { computed } from "vue";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import { createTransferHeaders } from "@/helpers/tableHeaders";
import { truncateNumber } from "@/helpers/money";

const props = defineProps({
  transferList: {
    type: Array,
    default: () => [],
  },
  formValid: {
    type: Boolean,
    default: false,
  }
});

const totalPrice = computed(() => {
  const result = props.transferList.reduce(
    (sum, item) =>
      sum + (Number(item.requestedQuantity * item.unitCost) || 0),
    0
  );
  return result >= 0 ? truncateNumber(result) : 0;
});

const emit = defineEmits(["removeItem", "addItem"]);
</script>
