<template>
  <v-card border rounded="lg" v-if="show">
    <v-expansion-panels variant="accordion" multiple flat focusable border>
      <!-- Contract Details -->
      <v-expansion-panel v-if="data.id">
        <v-expansion-panel-title>
          #{{ data.contractNumber }}
          <template #actions>
            <v-btn
              :color="getContractStatusColor(status)"
              variant="tonal"
              :text="status"
              size="small"
            />
            <v-icon class="ml-2" />
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="contractDetails" />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- Attachments -->
       <v-expansion-panel>
        <v-divider />
        <v-expansion-panel-title>
          Attachments
          <template #actions>
            <attachment-upload-dialog
              type="contract"
              :item-id="data.id"
              :update-db-url="`/contracts/${data.id}/attachments`"
              @uploaded="$emit('update')"
            />
            <v-icon class="ml-2" />
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <Attachments
            type="contract"
            :item-id="data.id"
            :attachments="data.attachments"
          />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<script setup>
import { computed } from "vue";
import { getContractStatusColor } from "@/helpers/status";

import LabelValueView from "@/components/utils/LabelValueView.vue";
import AttachmentUploadDialog from "@/components/attach/AttachmentUploadDialog.vue";
import Attachments from "@/components/attach/Attachments.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  status: {
    type: String,
    default: "",
  }
});

const emit = defineEmits(["update"]);

const contractDetails = [
  { label: "Contract Name", value: props.data.name || "-" },
  { label: "Contract Ref", value: props.data.reference || "-" },
  { label: "Vendor", value: props.data.vendor?.name || "-" },
  { label: "Start Date", value: props.data.startDate || "-" },
  { label: "Expiry By", value: props.data.endDate || "-" },
  { label: "Created By", value: props.data.requestedBy?.name || "-" },
];

const show = computed(
  () => props.data.id
);
</script>

<style scoped></style>
