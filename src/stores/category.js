import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useCategoryStore = defineStore("category", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const categories = ref([]);

  const getCategories = computed(() => categories.value);
  const setCategories = (data) => {
    categories.value = data;
  };

  const fetchCategories = async () => {
    try {
      const response = await httpClient.get("categories");
      setCategories(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchCategoryById = async (id) => {
    try {
      const response = await httpClient.get(`categories/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createCategory = async (data) => {
    data.tenantId = tenantId.value;

    try {
      await httpClient.post("categories", data);
      showSnackbar("green", "Category Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateCategory = async (data) => {
    try {
      await httpClient.put(`categories/${data.id}`, data);
      showSnackbar("green", "Category Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateSubCategory = async (data) => {
    try {
      await httpClient.post(`categories/${data.id}/subcategories`, data);
      showSnackbar("green", "Subcategory Added Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const deleteCategory = async (id) => {
    try {
      await httpClient.delete(`categories/${id}`);
      await fetchCategories();
      showSnackbar("green", "Category deleted successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getCategories,
    setCategories,
    fetchCategories,
    fetchCategoryById,
    createCategory,
    updateCategory,
    updateSubCategory,
    deleteCategory,
    // exportCategories,
  };
});
