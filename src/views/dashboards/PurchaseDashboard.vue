<template>
  <div>
    <!-- Report Header -->
    <ReportHeader
      v-model="payload"
      enable-date
      enable-vendors
      enable-items
      :loading="loading"
      @search="onSearch"
    />

    <!-- Purchase Dashboard -->
    <PurchaseDashboard :loading="loading" :dashboardData="dashboardData" />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

import httpClient from "@/plugin/Axios";
import { NewReportPayload } from "@/helpers/useReportPayload";

import ReportHeader from "@/components/utils/ReportHeader.vue";
import PurchaseDashboard from "@/components/dashboard/PurchaseDashboard.vue";

import { useSnackbarStore } from "@/stores/snackBar";
const snackBarStore = useSnackbarStore();

const loading = ref(false);
const payload = ref(NewReportPayload("purchase-dashboard"));
const dashboardData = ref({
  // metrics
  total: 0,
  baseAmount: 0,
  discount: 0,
  charge: 0,
  tax: 0,
  grn: 0,
  foc: 0,

  // unique metrics
  vendors: 0,
  uniqueItems: 0,
  locations: 0,
  hsnCompliant: 0,
  returnPercent: 0,

  // graph data
  dailyPurchaseTrends: [],
  topVendors: [],
  locationPurchaseAnalysis: [],
  categoryPurchaseDistribution: [],
  subcategoryPurchaseDistribution: [],
  highValueItems: [],
});

const onSearch = async ({ reqPayload }) => {
  loading.value = true;
  dashboardData.value = {};
  try {
    const res = await httpClient.post("/dashboards/purchases", reqPayload);
    dashboardData.value = res.data;
  } catch (err) {
    const { response } = err;
    const msg = response.data?.message || "Something went wrong";
    snackBarStore.showSnackbar("error", msg);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  onSearch({ reqPayload: payload.value });
});
</script>
