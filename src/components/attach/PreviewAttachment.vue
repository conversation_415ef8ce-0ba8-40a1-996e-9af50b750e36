<template>
  <v-sheet class="overflow-hidden" border rounded="lg">
    <v-carousel
      v-model="currentIndex"
      direction="vertical"
      vertical-arrows="right"
      vertical-delimiters="left"
      hide-delimiter-background
    >
      <v-carousel-item v-for="(file, i) in files" :key="i">
        <!-- PREVIEW AREA -->
        <component :is="getPreviewComponent(file)" :type="type" :file="file" />

        <!-- FILE INFO OVERLAY -->
        <v-overlay
          :scrim="false"
          content-class="w-100 h-100 d-flex flex-column align-center justify-space-between pointer-pass-through pa-3"
          contained
          model-value
          no-click-animation
          persistent
        >
          <v-scroll-x-transition mode="out-in" appear>
            <DownloadButton :type="type" :filePath="file.filePath">
              <template #default="{ loading, download }">
                <v-card :key="currentIndex" border rounded="lg" :loading="loading">
                  <v-list-item
                    :title="currentFile.fileName"
                    :prepend-icon="getFileIcon(currentFile)"
                    append-icon="mdi-download"
                    :disabled="loading"
                    @click="download"
                  />
                </v-card>
              </template>
            </DownloadButton>
          </v-scroll-x-transition>

          <!-- INDEX CHIP -->
          <v-chip
            class="mt-auto"
            :text="`${ currentIndex + 1 } / ${files.length}`"
            size="small"
            variant="flat"
          />
        </v-overlay>
      </v-carousel-item>
    </v-carousel>
  </v-sheet>
</template>

<script setup>
import { shallowRef, computed } from "vue";
import ImagePreview from "./ImagePreview.vue";
import NoPreview from "./NoPreview.vue";
import DownloadButton from "./DownloadButton.vue";

const currentIndex = shallowRef(0);

const props = defineProps({
  type: {
    type: String,
    required: true
  },
  files: {
    type: Array,
    default: () => []
  }
});

const currentFile = computed(() => props.files[currentIndex.value] || {});

function detectFileType(file) {
  if (!file) return "other";

  // Priority 1: contentType (MIME TYPE)
  const ct = file.contentType?.toLowerCase();
  if (ct) {
    if (ct.includes("image")) return "image";
    if (ct.includes("pdf")) return "pdf";
    if (ct.includes("spreadsheet") || ct.includes("excel")) return "excel";
    if (ct.includes("word") || ct.includes("document")) return "word";
  }

  // Priority 2: filename extension
  const name = file.fileName?.toLowerCase() || "";
  if (
    name.endsWith(".png") ||
    name.endsWith(".jpg") ||
    name.endsWith(".jpeg") ||
    name.endsWith(".webp")
  ) {
    return "image";
  }
  if (name.endsWith(".pdf")) return "pdf";
  if (name.endsWith(".xls") || name.endsWith(".xlsx")) return "excel";
  if (name.endsWith(".doc") || name.endsWith(".docx")) return "word";

  return "other";
}

/* COMPONENT MAPPER */
function getPreviewComponent(file) {
  return detectFileType(file) === "image" ? ImagePreview : NoPreview;
}

/* FILE ICONS */
function getFileIcon(file) {
  const type = detectFileType(file);

  switch (type) {
    case "pdf":
      return "mdi-file-pdf-box";
    case "excel":
      return "mdi-file-excel-box";
    case "word":
      return "mdi-file-word-box";
    case "image":
      return "mdi-file-image";
    default:
      return "mdi-file";
  }
}
</script>
