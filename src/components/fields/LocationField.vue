<template>
  <auto-complete
    v-model="model"
    :items="locations"
    v-bind="$attrs"
    :label="label"
    item-title="name"
    item-value="id"
    :loading="loading"
  />
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Location"
  },
  /** Component-specific attributes */
  noAuth: {
    // avoid restrict user specific locations
    type: Boolean,
    default: false
  },
  only: {
    // global location selected
    type: [Array, String],
    default: () => null
  },
  exclude: {
    type: [Array, String],
    default: () => null
  }
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const locations = computed(() => {
  const filters = {
    id: {
      $in: props.only,
      $not: props.exclude
    }
  };
  return masterStore.getLocations(filters, props.noAuth);
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});
</script>
