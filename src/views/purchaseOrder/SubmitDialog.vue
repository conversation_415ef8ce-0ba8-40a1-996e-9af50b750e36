<template>
  <v-dialog v-model="model" width="420" persistent>
    <v-card>
      <v-card-title class="d-flex justify-end">
        <v-btn
          variant="tonal"
          icon="mdi-close"
          color="error"
          size="small"
          @click="model = null"
        >
        </v-btn
      ></v-card-title>
      <v-card-title class="py-2 text-center text-wrap">
        Choose the option to GRN
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text class="py-4">
        <v-row>
          <template v-for="option in poOptions" :key="option.id">
            <v-col cols="12" class="pa-2">
              <v-card
                @click="selectOption(option)"
                class="cursor-pointer"
                :style="{ backgroundColor: option.selected ? 'primary' : '' }"
                :class="{ 'bg-primary': option.selected }"
              >
                <v-card-text>
                  <div class="d-flex justify-space-between align-center">
                    <div>
                      <div class="font-weight-medium">{{ option.message }}</div>
                      <div class="text-caption">
                        {{ option.description }}
                      </div>
                    </div>

                    <v-icon v-if="option.selected" small>
                      mdi-check-circle
                    </v-icon>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </template>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions class="d-flex justify-center">
        <div class="text-center">
          <v-btn
            color="error"
            variant="text"
            flat
            border
            :disabled="!selectedOption"
            @click="submit"
            >Submit</v-btn
          >
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref } from "vue";

const model = defineModel();
const emit = defineEmits(["selectOption"]);

const selectedOption = ref(null);
const poOptions = ref([
  {
    id: 1,
    message: "Keep existing PO",
    description: "Retain the current purchase order",
    selected: false
  },
  // {
  //   id: 2,
  //   message: "Create new PO for pending items",
  //   description: "Generate a new purchase order for the remaining items",
  //   selected: false
  // },
  {
    id: 3,
    message: "Close PO",
    description: "Close this purchase order and take no further action",
    selected: false
  }
]);

const selectOption = (option) => {
  if (selectedOption.value == option) {
    selectedOption.value = null;
    poOptions.value.forEach((item) => (item.selected = false));
  } else {
    selectedOption.value = option;
    poOptions.value.forEach((item) => (item.selected = item.id == option.id));
  }
};

const submit = () => {
  if (!selectedOption.value) return;
  emit("selectOption", selectedOption.value.id);
  model.value = null;
};
</script>
