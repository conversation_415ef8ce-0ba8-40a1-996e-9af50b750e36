<template>
  <div class="dashboard-header">
    <!-- Top App Bar for the report header -->
    <v-app-bar scroll-behavior="elevate" v-if="!smAndDown">
      <v-container fluid>
        <v-row justify="center" align="center">
          <!-- Store/Outlet Selector -->
          <v-col cols="auto">
            <location-field
              v-model="modelValue.filters.locations"
              multiple
              @update:model-value="onChangeLocation"
              min-width="250"
            />
          </v-col>

          <!-- Date Picker -->
          <v-col cols="3" v-if="enableDate">
            <v-date-input
              v-model="date"
              label="Date"
              variant="outlined"
              density="compact"
              prepend-icon
              prepend-inner-icon="$calendar"
              multiple="range"
              color="primary"
              :max="new Date()"
              hide-details
            />
          </v-col>

          <!-- Filter Button -->
          <!-- Opens filter drawer -->
          <v-col cols="auto">
            <v-btn variant="tonal" color="primary" @click="toggleFilterDrawer">
              <v-icon class="mr-2" icon="mdi-filter-variant"></v-icon>
              <span>Filters</span>
            </v-btn>
          </v-col>

          <ExportOptions
            v-if="enableExport"
            :pdf="exportOptions.pdf"
            :xlsx="exportOptions.xlsx"
            :loading="loading"
            @export="(resultType) => search({resultType})"
          />

          <!-- Search Button -->
          <!-- Emits search event with loading state -->
          <v-col cols="auto">
            <v-btn color="primary" variant="flat" :loading="loading" @click="search({})">
              <span>Search</span>
              <v-icon icon="mdi-magnify" class="ml-2"></v-icon>
            </v-btn>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>

    <!-- Divider below the app bar -->
    <v-divider v-if="!smAndDown"></v-divider>

    <v-fab
      v-if="smAndDown"
      color="primary"
      app
      extended
      prepend-icon="mdi-filter-variant"
      text="Filters"
      location="bottom center"
      @click="toggleFilterDrawer"
    ></v-fab>

    <!-- Filter Drawer -->
    <filter-navigation-drawer
      ref="advancedFilter"
      hide-default-activator
      :model-value="modelValue"
      :enable-export="enableExport"
      @apply="applyFilters"
      :option-groups="optionGroups"
    >
      <template #filters>
        <v-container fluid>
          <!-- Date Picker -->
          <v-row>
            <v-col cols="12" v-if="enableDate">
              <v-date-input
                v-model="date"
                label="Date"
                variant="outlined"
                density="compact"
                prepend-icon
                prepend-inner-icon="$calendar"
                multiple="range"
                color="primary"
                :max="new Date()"
                hide-details
              />
            </v-col>
          </v-row>

          <!-- Locations & related filters -->
          <v-row>
            <!-- Store/Outlet Selector -->
            <v-col cols="12">
              <location-field
                v-model="modelValue.filters.locations"
                multiple
                @update:model-value="onChangeLocation"
              />
            </v-col>

            <!-- work/storage areas -->
            <v-col cols="12">
              <work-area-field
                v-model="modelValue.filters.inventoryLocations"
                :locations="modelValue.filters.locations"
                multiple
              />
            </v-col>
          </v-row>

          <v-row v-if="enableVendors">
            <!-- vendors -->
            <v-col cols="12">
              <vendor-field
                v-model="modelValue.filters.vendors"
                multiple
                @update:model-value="onChangeVendor"
              />
            </v-col>
          </v-row>

          <!-- Inventory Items & related filters -->
          <v-row v-if="enableItems">
            <!-- categories -->
            <v-col cols="12">
              <category-field
                v-model="modelValue.filters.categories"
                multiple
                @update:model-value="onChangeCategory"
              />
            </v-col>

            <!-- subCategories -->
            <v-col cols="12">
              <sub-category-field
                v-model="modelValue.filters.subCategories"
                :categories="modelValue.filters.categories"
                multiple
                @update:model-value="onChangeSubCategory"
              />
            </v-col>

            <!-- inventory items -->
            <v-col cols="12">
              <inventory-item-field
                v-model="modelValue.filters.inventoryItems"
                :categories="modelValue.filters.categories"
                :sub-categories="modelValue.filters.subCategories"
                :vendors="modelValue.filters.vendors"
                multiple
              />
            </v-col>
          </v-row>
        </v-container>
      </template>

      <!-- options slot: only render if provided by parent -->
      <template v-if="$slots.options">
        <slot name="options"></slot>
      </template>
    </filter-navigation-drawer>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from "vue";
import { useDisplay } from "vuetify";
import { NewReportPayload, GetReportOptionGroups } from "@/helpers/useReportPayload";

// Components
import FilterNavigationDrawer from "@/components/filterSortPanel/FilterSortPanel.vue";
import LocationField from "@/components/fields/LocationField.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import CategoryField from "@/components/fields/CategoryField.vue";
import SubCategoryField from "@/components/fields/SubCategoryField.vue";
import VendorField from "@/components/fields/VendorField.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";
import Search from "@/components/navigation/Search.vue";
import ExportOptions from "./ExportOptions.vue";

// ----------------- Props -----------------
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => NewReportPayload()
  },
  loading: {
    type: Boolean,
    default: false
  },
  enableDate: {
    type: Boolean,
    default: false
  },
  enableVendors: {
    type: Boolean,
    default: false
  },
  enableItems: {
    type: Boolean,
    default: false
  },
  enableExport: {
    type: [Boolean, Array],
    default: false
  }
});

const { smAndDown } = useDisplay();
const emit = defineEmits(["search", "update:modelValue"]);

const optionGroups = GetReportOptionGroups(props.modelValue.id);
const date = ref([new Date(), new Date()]);
const submit = val => emit("search", val);

const search = ({ resultType = "json" }) => {
  submit({
    reqPayload: props.modelValue,
    resultType
  });
};

const applyFilters = ({ resultType, reqPayload }) => {
  emit("update:modelValue", reqPayload);
  submit({
    resultType,
    reqPayload
  });
};

const advancedFilter = ref(false);
function toggleFilterDrawer() {
  advancedFilter.value.toggleFilter();
}

const onChangeLocation = () => {
  props.modelValue.filters.workAreas = [];
};

const onChangeCategory = () => {
  props.modelValue.filters.subCategories = [];
  props.modelValue.filters.inventoryItems = [];
};

const onChangeSubCategory = () => {
  props.modelValue.filters.inventoryItems = [];
};

const onChangeVendor = () => {
  props.modelValue.filters.inventoryItems = [];
};

function formatLocalDate(date) {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

const exportOptions = ref({
  xlsx: true,
  pdf: true
});
if (props.enableExport && Array.isArray(props.enableExport)) {
  exportOptions.value = {
    xlsx: props.enableExport.includes("xlsx"),
    pdf: props.enableExport.includes("pdf")
  };
}

watch(
  () => date.value,
  val => {
    if (!Array.isArray(val) || val.length < 2) return;

    props.modelValue.filters.fromDate = formatLocalDate(val[0]);
    props.modelValue.filters.toDate = formatLocalDate(val[val.length - 1]);
  },
  { deep: true }
);
</script>
