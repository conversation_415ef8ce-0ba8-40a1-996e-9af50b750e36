<template>
  <div class="graph-container">
    <component
      :is="component"
      :title="title"
      :chart-data="chartData"
      :chart-options="chartOptions"
    />
  </div>
</template>

<script setup>
import { computed } from "vue";
import { getBgColors } from "../helpers/colors";

import WidgetPiChart from "./WidgetPiChart.vue";
import LineChart from "../charts/LineChart.vue";
import BarChart from "../charts/BarChart.vue";
import Pie<PERSON>hart from "../charts/PieChart.vue";
import { Line } from "vue-chartjs";

// 🔧 Props Definition
const props = defineProps({
  /**
   * Chart title (used for identification)
   */
  title: {
    type: String,
    default: ""
  },

  graphType: {
    type: String,
    default: "pie"
  },

  /**
   * Graph data in key-value format:
   * {
   *   google: { label: "Google", Amount: 75, total_count: 20 },
   *   bing: { label: "Bing", Amount: 15, total_count: 10 }
   * }
   */
  graphData: {
    type: Object,
    required: true
  },

  /**
   * View mode determines displayed metric:
   *  1 → Amount
   *  2 → Count
   */
  viewMode: {
    type: Number,
    default: 2
  },

  /**
   * Limit of top items shown
   */
  topN: {
    type: Number,
    default: 10
  }
});

const component = computed(() => {
  switch (props.graphType) {
    case "line":
      return LineChart;
    case "bar":
      return BarChart;
    default:
      return WidgetPiChart;
  }
});

// Utility: Extract & Sort Top N
const getTopN = (graphData, n) => {
  const values = Object.values(graphData || []);
  if (!values.length) return [];

  // Sort descending by Amount or total_count
  values.sort((a, b) => {
    return props.viewMode === 2
      ? b.count - a.count
      : b.amount - a.amount;
  });

  return !n || values.length <= n ? values : values.slice(0, n);
};

// Computed: Chart Data
const chartData = computed(() => {
  if (props.graphType === "pie") {
    return getTopN(props.graphData, props.topN);
  }
  if (!props.graphData) return {};

  let labels = [];
  let totalActivity = [];
  props.graphData.forEach(e => {
    labels.push(e.title);
    totalActivity.push(props.viewMode !== 2 ? e.count : e.amount);
  });

  return {
    labels,
    datasets: [
      {
        label: "Total Activity",
        data: totalActivity,
        borderWidth: 2,
        tension: 0.4,
        backgroundColor: getBgColors(labels.length),
        borderColor: "#E95420"
      }
    ]
  };
});

// 🧠 Custom tooltip function
const tooltipCallbacks = {
  // Custom title line (first line in tooltip)
  title: tooltipItems => {
    const item = tooltipItems[0]; // first hovered point
    const index = item.dataIndex;
    const original = props.graphData[index];
    if (!original) return "";
    const title = original.label || original.title;
    return `${title}`;
  }

  // Custom label line (colored dot + value)
  /*label: (tooltipItem) => {
    console.log("tooltipItem", tooltipItem);
    const ds = tooltipItem.dataset;
    const label = ds.label || "";
    const value = ds.data[tooltipItem.dataIndex];
    return `${label}: ${value}`;
  },*/
};

// Chart.js options
const chartOptions = computed(() => ({
  maintainAspectRatio: true,
  responsive: true,
  plugins: {
    legend: { display: false },
    tooltip: {
      enabled: true,
      callbacks: tooltipCallbacks
    }
  }
}));
</script>

<style scoped>
</style>
