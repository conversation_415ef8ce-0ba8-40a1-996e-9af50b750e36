<template>
  <div>
    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      :back-to="{ name: 'closing' }"
      hide-submit
      :loading="loading"
    >
      <template #custom>#{{ record.closingNumber }}</template>

      <!-- <template #actions>
        <GRNOptions status="completed" :itemId="grnId" />
      </template> -->
    </form-actions-bar>

    <!-- loader -->
    <page-loader v-if="loading" />

    <!-- Overview/Content -->
    <v-container v-else fluid>
      <!-- Overview -->
      <ClosingOverview :data="record" />

      <!-- Item Table -->
      <ClosingTableItems :items="tableItems" :record="record" />
    </v-container>
  </div>
</template>
<script setup>
import { onBeforeMount, ref } from "vue";
import { useRoute } from "vue-router";

import { useClosingStore } from "@/stores/closing";

// Components
import PageLoader from "@/components/utils/PageLoader.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import ClosingOverview from "@/views/closing/ClosingOverview.vue";
import ClosingTableItems from "@/views/closing/ClosingTableItems.vue";

const closingStore = useClosingStore();
const route = useRoute();

const closingId = route.params.id;
const loading = ref(false);
const record = ref({});
const tableItems = ref([]);

const get = async () => {
  loading.value = true;
  try {
    const { items, ...result } = await closingStore.fetchClosingById(closingId);
    record.value = result;
    tableItems.value = items;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await get();
});
</script>
