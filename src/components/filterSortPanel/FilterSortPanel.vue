<template>
  <div class="filter-drawer">
    <!-- Activator slot outside the drawer -->
    <div v-if="!hideDefaultActivator">
      <slot name="activator" :toggleFilter="toggleFilter">
        <v-btn variant="tonal" color="primary" @click="toggleFilter">
          <v-icon class="mr-2" icon="mdi-filter-variant"></v-icon>
          <span>Filters</span>
        </v-btn>
      </slot>
    </div>

    <!-- filter Drawer -->
    <v-navigation-drawer
      v-model="openFilter"
      location="right"
      :width="width"
      order="-1"
      persistent
      temporary
    >
      <v-card flat tile>
        <v-card-text class="pa-0">
          <!-- Tabs Content -->
          <v-tabs-window v-model="activeTab">
            <!-- Filters Tab -->
            <v-tabs-window-item :value="1">
              <slot name="filters">
                <Filters v-model="internalState.filters" :filters="filterComponents" />
              </slot>
            </v-tabs-window-item>

            <!-- Columns Tab -->
            <v-tabs-window-item :value="2">
              <slot name="columns">
                <column-selection ref="columnRef" v-model="internalState.columns" />
              </slot>
            </v-tabs-window-item>

            <!-- Options Tab -->
            <v-tabs-window-item :value="3">
              <slot name="options">
                <options v-model="internalState.options" :groups="optionGroups" />
              </slot>
            </v-tabs-window-item>
          </v-tabs-window>
        </v-card-text>
      </v-card>

      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar>
          <v-tabs v-model="activeTab" slider-color="primary" v-if="visibleTabs.length > 0">
            <v-tab v-for="tab in visibleTabs" :key="tab.key" :value="tab.value">{{ tab.label }}</v-tab>
          </v-tabs>
          <v-spacer></v-spacer>
          <v-btn variant="text" icon="mdi-close" color="error" @click="closeDrawer" />
        </v-toolbar>
      </template>

      <!-- Footer: Apply Button -->
      <template v-slot:append>
        <v-divider></v-divider>
        <v-row class="pa-2" no-gutters>
          <v-col cols="auto" class="pr-2" v-if="enableExport">
            <ExportOptions
              v-model="resultType"
              select-mode
              :pdf="exportOptions.pdf"
              :xlsx="exportOptions.xlsx"
              list
            />
          </v-col>
          <v-col class="w-100">
            <v-btn color="primary" variant="flat" block @click="apply">{{ applyButtonText }}</v-btn>
          </v-col>
        </v-row>
      </template>
    </v-navigation-drawer>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive, useSlots } from "vue";

import { ResultType } from "@/plugin/Axios";
import { NewReportPayload } from "@/helpers/useReportPayload";

import Filters from "./Filters.vue";
import ColumnSelection from "./ColumnSelection.vue";
import Options from "./Options.vue";
import ExportOptions from "@/components/utils/ExportOptions.vue";

// Props
const props = defineProps({
  width: {
    type: Number,
    default: 420
  },
  hideDefaultActivator: {
    type: Boolean,
    default: false
  },
  filterComponents: {
    type: Array,
    default: () => []
  },
  applyButtonText: {
    type: String,
    default: "Apply"
  },
  enableExport: {
    type: [Boolean, Array],
    default: false
  },
  modelValue: {
    // reactive state
    type: Object,
    default: () => NewReportPayload()
  },
  optionGroups: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(["apply"]);

// Drawer & Tabs State
const openFilter = ref(false);
const activeTab = ref(1);
const resultType = ref(ResultType.JSON);
const columnRef = ref(null);
const slots = useSlots();

// Internal reactive state
const internalState = reactive({
  filters: {},
  columns: [],
  options: {}
});

const resetFilter = () => {
  const val = props.modelValue;
  internalState.filters = JSON.parse(JSON.stringify(val.filters || {}));
  internalState.columns = JSON.parse(JSON.stringify(val.columns || []));
  internalState.options = JSON.parse(JSON.stringify(val.options || {}));
};

// Sync internal state when parent modelValue changes
watch(() => props.modelValue, resetFilter, {
  deep: true,
  immediate: true
});

// Predefined tabs
const allTabs = [
  {
    label: "Filters",
    key: "filters",
    value: 1,
    enabled: slots.filters || props.filterComponents.length > 0
  },
  {
    label: "Columns",
    key: "columns",
    value: 2,
    enabled: props.modelValue.columns.length > 0
  },
  {
    label: "Options",
    key: "options",
    value: 3,
    enabled: slots.options || props.optionGroups.length > 0
  }
];

// Only show enabled tabs
const visibleTabs = computed(() => allTabs.filter(tab => tab.enabled));

// Methods
const apply = () => {
  columnRef.value?.commitChanges();
  const val = JSON.parse(JSON.stringify(internalState));
  emit("apply", {
    reqPayload: val,
    resultType: resultType.value
  });
  closeDrawer();
};

const closeDrawer = () => {
  openFilter.value = false;
};

// Expose toggle function for external control
const toggleFilter = () => {
  openFilter.value = !openFilter.value;
};

const exportOptions = ref({
  xlsx: true,
  pdf: true
});
if (props.enableExport && Array.isArray(props.enableExport)) {
  exportOptions.value = {
    xlsx: props.enableExport.includes("xlsx"),
    pdf: props.enableExport.includes("pdf")
  };
}

defineExpose({ toggleFilter });
</script>

<style scoped></style>
