<template>
  <v-menu v-model="menu" transition="scale-transition" location="left">
    <!-- Activator Button -->
    <template #activator="{ props }">
      <v-list-item v-bind="props">
        <template #prepend>
          <v-icon icon="mdi-theme-light-dark" />
        </template>
        <v-list-item-title>Theme</v-list-item-title>
      </v-list-item>
    </template>

    <!-- Menu Card -->
    <v-card title="Theme" subtitle="Choose your preference" prepend-icon="mdi-theme-light-dark" >
      <v-divider />
      <v-list class="pa-0">
        <!-- Theme Options -->
        <div v-for="option in themeOptions" :key="option.id">
          <v-list-item  @click="change(option.id)" :title="option.name" :subtitle="option.description">
            <!-- Icon on the left -->
            <template #prepend>
              <v-icon :color="selectedMode === option.id ? 'primary' : undefined">{{ option.icon }}</v-icon>
            </template>
            <!-- Checkmark if selected -->
            <template #append>
              <v-icon v-if="selectedMode === option.id" color="primary">mdi-check</v-icon>
            </template>
          </v-list-item>
          <v-divider />
        </div>
      </v-list>
    </v-card>
  </v-menu>
</template>

<script setup>
import { ref } from "vue";
import { modes, setTheme, getThemeMode } from "@/plugin/vuetifyTheme";

// Menu state
const menu = ref(false);

// Currently selected theme mode
const selectedMode = ref(getThemeMode());

// Theme options for display
const themeOptions = [
  { id: modes.light, icon: "mdi-white-balance-sunny", name: "Light" },
  { id: modes.dark, icon: "mdi-weather-night", name: "Dark" },
  {
    id: modes.system,
    icon: "mdi-theme-light-dark",
    name: "System Default",
    description: "Follows your system theme"
  }
];

// Handle theme change
function change(mode) {
  setTheme(mode); // Update Vuetify + localStorage
  selectedMode.value = mode; // Update UI state
  menu.value = false; // Close menu
}
</script>

<style scoped>
/* Optional: add spacing or custom styles here */
</style>
