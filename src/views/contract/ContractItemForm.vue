<template>
  <div>
    <v-fab
      v-if="!openNav"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel v-model="filter" :no-vendor="true" />

        <v-card-text>
          <v-form ref="form" v-smart-tab="smartTab">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <inventory-item-field
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    persistent-hint
                    :hint="`HSN Code: ${formData.hsnCode || '-'}`"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                    :vendors="selectedVendorId ? [selectedVendorId] : []"
                    :exclude="excludedItems"
                    autofocus
                  />
                </v-col>

                <v-col cols="12">
                  <v-autocomplete
                    v-model="formData.pkg"
                    :items="filteredPackages"
                    item-title="name"
                    item-value="id"
                    label="Package/UOM"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                  />
                </v-col>
                <v-col cols="12">
                  <v-autocomplete
                    v-model="formData.contractType"
                    :items="types"
                    item-title="name"
                    item-value="id"
                    density="compact"
                    variant="outlined"
                    single-line
                    hide-details
                    color="primary"
                    clearable
                    @update:model-value="handleContractType"
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12">
                  <v-switch
                    v-model="formData.inclTax"
                    inset
                    density="compact"
                    color="success"
                    base-color="error"
                    label="Incl Tax"
                    :disabled="taxDisable"
                    hide-details
                  ></v-switch>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.contractPrice"
                    label="Price"
                    type="number"
                    :rules="[rules.price, rules.positive]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData, 'contractPrice')"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <!-- @todo: item summary -->
          <template #actions>
            <v-btn @click="onSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { ref, computed, watch, reactive } from "vue";
import rules from "@/helpers/rules";
import { contractItemRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useInventoryItemStore } from "@/stores/inventoryItem";

// components
import InventoryItemField from "@/components/fields/InventoryItemField.vue";
import InventoryFiltersPanel from "@/components/purchase/InventoryFiltersPanel.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  selectedVendorId: {
    type: String,
    required: true,
  },
  existingItems: {
    type: Array,
    default: () => [],
  },
});

const inventoryStore = useInventoryItemStore();

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const packageList = ref([]);
const selectedItem = ref([]);

const types = ref([
  { id: "fixed", name: "Fixed contract price" },
  { id: "discount", name: "% discount on MRP" },
  { id: "absolute", name: "Absolute price reduction on MRP" },
]);

const taxDisable = ref(false);

const handleContractType = (selected) => {
  const type = selected;
  // reset state if nothing selected
  if (!type) {
    formData.value.inclTax = false;
    taxDisable.value = false;
    return;
  }
  // fixed → editable; others → disabled
  formData.value.inclTax = true;
  taxDisable.value = type !== "fixed";
};

const filteredPackages = computed(() => {
  const item = selectedItem.value;
  if (!item || !item.id) return [];

  const chosenPkgIds = props.existingItems
    .filter((i) => i.itemId === item.id)
    .map((i) => i.pkg?.id);

  return packageList.value.filter((pkg) => !chosenPkgIds.includes(pkg.id));
});

const excludedItems = computed(() => {
  const map = {};

  props.existingItems.forEach((i) => {
    if (!i.itemId || !i.pkg?.id) return;

    if (!map[i.itemId]) {
      map[i.itemId] = new Set();
    }

    map[i.itemId].add(i.pkg.id);
  });

  return Object.entries(map).map(([itemId, pkgs]) => ({
    itemId,
    pkgs: Array.from(pkgs),
  }));
});

watch(filteredPackages, (list) => {
  if (list.length > 0) {
    formData.value.pkg = list[0];
  } else {
    formData.value.pkg = null;
  }
});

const onSelectInventoryItem = async (selected) => {
  if (!selected.id) return;
  selectedItem.value = selected;

  // build package list
  packageList.value = [...selected.packages];

  if (selected.defaultPackage) {
    packageList.value.unshift({
      name: `${selected.purchaseUnit}`,
      id: "default",
    });
  }

  if (!packageList.value.length) return;

  const item = await inventoryStore.fetchInventoryItemById(selected.id);

  formData.value.itemName = item.itemName;
  formData.value.itemCode = item.itemCode;
  formData.value.itemId = item.id;
  formData.value.hsnCode = item.hsnCode;
  formData.value.countingUOM = item.countingUnit.symbol;
};

const onSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...formData.value };
  emit("add", payload);
  packageList.value = [];

  // ✅ Reset form data
  formData.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
  selectedItem.value = null;
  form.value.resetValidation();
  // ✅ Focus back to the first field
  smartTab.focusFirst();
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }
};

const smartTab = reactive({
  onSubmit,
});

watch(
  openNav,
  (val) => {
    if (val) smartTab.focusFirst();
  },
  {
    immediate: true,
  }
);
</script>
