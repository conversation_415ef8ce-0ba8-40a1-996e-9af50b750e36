<template>
  <auto-complete
    v-model="model"
    :items="vendors"
    v-bind="$attrs"
    :label="label"
    item-title="name"
    item-value="id"
    :loading="loading"
  />
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Vendor"
  },
  only: {
    // global location selected
    type: [Array, String],
    default: () => null
  },
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const vendors = computed(() => {
  return masterStore.getVendors({id: props.only});
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});
</script>
