<template>
  <v-dialog v-model="model" width="420" persistent>
    <v-card>
      <v-card-title class="d-flex justify-end">
        <v-btn
          variant="tonal"
          icon="mdi-close"
          color="error"
          size="small"
          @click="model = null"
        />
      </v-card-title>

      <v-card-title class="py-2 text-center text-wrap">
        Choose the option to Closing
      </v-card-title>

      <v-divider />

      <v-card-text class="py-4">
        <v-row>
          <v-col
            v-for="option in closingOptions"
            :key="option.id"
            cols="12"
            class="pa-2"
          >
            <v-card
              @click="selectOption(option)"
              class="cursor-pointer"
              :class="{ 'bg-primary': option.selected }"
            >
              <v-card-text>
                <div class="d-flex justify-space-between align-center">
                  <div>
                    <div class="font-weight-medium">{{ option.message }}</div>
                    <div class="text-caption">{{ option.description }}</div>
                  </div>
                  <v-icon v-if="option.selected" small>mdi-check-circle</v-icon>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>

      <v-divider />

      <v-card-actions class="d-flex justify-center">
        <v-btn
          color="error"
          variant="text"
          flat
          border
          :disabled="!selectedOption"
          @click="submit"
        >
          Submit
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref } from "vue";

const model = defineModel();
const emit = defineEmits(["selectOption"]);

const closingOptions = ref([
  {
    id: 1,
    message: "OverWrite InStock & Upload Closing",
    description: "Upload closing data and adjust stocks accordingly.",
    selected: true
  },
  {
    id: 2,
    message: "Upload Closing",
    description: "Upload closing data only, stocks remain unchanged.",
    selected: false
  }
]);

const selectedOption = ref(closingOptions.value.find(opt => opt.selected));

const selectOption = (option) => {
  closingOptions.value.forEach(item => (item.selected = item.id === option.id));
  selectedOption.value = option;
};

const submit = () => {
  emit("selectOption", selectedOption.value.id);
  model.value = null;
};
</script>
