<template>
  <auto-complete
    v-model="model"
    :items="categories"
    v-bind="$attrs"
    :label="label"
    item-title="name"
    item-value="id"
    :loading="loading"
  />
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Categories"
  }
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const categories = computed(() => {
  return masterStore.getCategories({});
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});
</script>
