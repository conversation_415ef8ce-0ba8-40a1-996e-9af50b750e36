import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useStoreStore = defineStore("store", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const stores = ref([]);

  const getStores = computed(() => stores.value);

  const setStores = (data) => {
    stores.value = data;
  };

  const fetchStores = async () => {
    try {
      const response = await httpClient.get(`stores`);
      setStores(response.data);
    } catch ({ response }) {
      showSnackbar("error", response?.data?.message || "Something went wrong");
      throw Error(response?.data?.message || "Request failed");
    }
  };

  const fetchStoreById = async (id) => {
    try {
      const response = await httpClient.get(`stores/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createStore = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await httpClient.post("stores", data);
      showSnackbar("green", "Location Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateStore = async (data) => {
    try {
      await httpClient.put(`stores/${data.id}`, data);
      showSnackbar("green", "Location Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateStoreActiveStatus = async (id) => {
    try {
      await httpClient.put(`stores/${id}/activateStatus`);
      fetchStores();
      showSnackbar("green", "Location status updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getStores,
    fetchStores,
    fetchStoreById,
    createStore,
    updateStore,
    updateStoreActiveStatus,
  };
});
