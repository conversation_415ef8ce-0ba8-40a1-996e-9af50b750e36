<template>
  <v-card border rounded="lg" width="100%">
    <v-data-table
      class="table-bordered"
      :headers="headers"
      :items="items"
      items-per-page="-1"
      hide-default-footer
      hide-no-data
    >
      <template v-slot:item="{ item, index }">
        <tr>
          <td class="py-2 text-center">{{ index + 1 }}</td>
          <td class="py-2">
            <span>{{ item.name }}</span>
          </td>
          <td class="py-2 text-center">
            <span>{{ item.quantity }}{{ item?.toUnit || "" }}</span>
          </td>
          <td class="py-2 text-center">
            <span>{{ item.unitCost }}</span>
          </td>
          <td class="py-2 text-center" v-if="showWeight">
            <v-text-field
              v-model.number="item.fullWeight"
              density="compact"
              type="number"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.positive]"
              suffix="kg"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="item.fullWeight = item.fullWeight || 0"
            >
            </v-text-field>
          </td>
          <td class="py-2 text-center" v-if="showWeight">
            <v-text-field
              v-model.number="item.emptyWeight"
              density="compact"
              type="number"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.positive]"
              suffix="kg"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="item.emptyWeight = item.emptyWeight || 0"
            >
            </v-text-field>
          </td>

          <td>
            <div class="d-flex justify-center align-center">
              <v-icon color="error" @click="removeRow(index)">
                mdi-close
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
      <template v-slot:body.append="{ columns }">
        <tr>
          <td class="py-2"></td>
          <td class="py-2">
            <v-autocomplete
              ref="pkgName"
              v-model="newRow.name"
              :items="filteredPackageList"
              item-title="name"
              item-value="symbol"
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              return-object
              clearable
              @update:model-value="handlePkg"
              autofocus
            ></v-autocomplete>
          </td>
          <td class="py-2 text-center">
            <span>{{ newRow.quantity }}{{ newRow?.toUnit || "" }}</span>
          </td>
          <td class="py-2">
            <v-text-field
              v-model.number="newRow.unitCost"
              type="number"
              :rules="[rules.positive]"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="newRow.unitCost = newRow.unitCost || 0"
              @keypress.enter="addRow(newRow)"
            ></v-text-field>
          </td>
          <td class="py-2" v-if="showWeight">
            <v-text-field
              v-model.number="newRow.fullWeight"
              density="compact"
              type="number"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.positive]"
              suffix="kg"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="newRow.fullWeight = newRow.fullWeight || 0"
            >
            </v-text-field>
          </td>
          <td class="py-2" v-if="showWeight">
            <v-text-field
              v-model.number="newRow.emptyWeight"
              density="compact"
              type="number"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.positive]"
              suffix="kg"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="newRow.emptyWeight = newRow.emptyWeight || 0"
              @keypress.enter="addRow(newRow)"
            >
            </v-text-field>
          </td>

          <td class="py-2">
            <div class="d-flex justify-center align-center">
              <v-icon
                color="green"
                :disabled="!isRowValid(newRow)"
                @click="commitNewRow"
              >
                mdi-plus
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
    </v-data-table></v-card
  >
</template>
<script setup>
import { ref, computed } from "vue";
import { packageHeaders } from "@/helpers/tableHeaders";
import { packageRecord as DEFAULT_PACKAGE } from "@/helpers/defaultRecords";
import rules from "@/helpers/rules";

const props = defineProps({
  unit: {
    type: Object,
    default: () => {},
  },
  list: {
    type: Array,
    default: () => [],
  },
  showWeight: {
    type: Boolean,
    default: false,
  },
});

const items = defineModel();
// const inputRef = ref(null);
const pkgName = ref(null);

const newRow = ref({ ...DEFAULT_PACKAGE });

const headers = computed(() => {
  return packageHeaders.filter((header) => {
    if (props.showWeight) {
      return true;
    }
    return !header.showWeight;
  });
});

const isRowValid = (row) =>
  row.quantity > 0 &&
  row.name &&
  row.unitCost >= 0 &&
  row.packageCode !== undefined;

const commitNewRow = () => {
  if (!isRowValid(newRow.value)) return;
  items.value.push({ ...newRow.value });

  newRow.value = { ...DEFAULT_PACKAGE };
  pkgName.value.focus();
};

const filteredPackageList = computed(() => {
  return props.list.filter(
    (pkg) => items.value.findIndex((item) => item.name === pkg.name) == -1
  );
});

const handlePkg = (val) => {
  newRow.value.name = val.name;
  newRow.value.quantity = val.quantity;
  newRow.value.toUnit = val.toUnit;
};

const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow();
};
const removeRow = (index) => {
  items.value.splice(index, 1);
};
</script>

<style scoped></style>
