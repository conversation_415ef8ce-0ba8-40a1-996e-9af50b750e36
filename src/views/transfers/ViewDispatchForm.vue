<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #custom>
        <span class="text-subtitle-1">#{{ dispatchId }}</span>
      </template>
      <template #actions>   
        <dispatch-options :item="record" :dispatchNo="dispatchId" :showExportOnly="matchedTimeline.status === 'completed' ? true : false"/>   
      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid>
      <dispatch-overview :data="record" :dispatchDetails="matchedTimeline" />

      <v-container fluid class="pa-0">
        <v-row no-gutters>
          <v-card border rounded="lg" class="my-2" width="100%">
            <v-data-table
              class="table-bordered"
              :headers="viewDispatchHeaders"
              :items="tableItems"
              items-per-page="-1"
              hide-default-footer
            >
              <template #item.pkgName="{ item }">
                {{ item?.pkg.id === 'default' ? item.countingUOM : item?.pkg.name }}
              </template>

              <template #item.dispatchedQuantity="{ item }">
                {{ item.dispatchedQuantity ? item.dispatchedQuantity : 0 }}
              </template>

              <template #item.receivedQuantity="{ item }">
                {{ item.receivedQuantity ? item.receivedQuantity : 0 }}
              </template>

              <template #item.shortageQuantity="{ item }">
                {{ item.shortageQuantity ? item.shortageQuantity : 0 }}
              </template>
            </v-data-table>
          </v-card>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTransferStore } from "@/stores/transfer";
import { viewDispatchHeaders } from "@/helpers/tableHeaders";
import DispatchOptions from "@/views/transfers/DispatchOptions.vue";
import DispatchOverview from "@/views/transfers/DispatchOverview.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";

const transferStore = useTransferStore();
const router = useRouter();
const route = useRoute();

const transferId = route.params.id;
const dispatchId = route.query?.dispatchId;

const record = ref({});
const loading = ref(false);

const matchedTimeline = computed(() => {    
  return record.value.timeLine?.find((t) => t.dispatchNo === dispatchId);
});

const tableItems = computed(() => {
  if (matchedTimeline.value) {
    return matchedTimeline.value.items.map((item) => {
      const baseItem = record.value.items?.find(
        (i) => i.itemId === item.itemId
      );      
      return {
        ...item,
        itemName: baseItem?.itemName,
        itemCode: baseItem?.itemCode,
        countingUOM: baseItem?.countingUOM
      };
    });
  } 
  else {
    return record.value.items;
  }
});

const navigatePrevious = () => {
  router.push({ name: "transfers" });
};

onBeforeMount(async () => {
  loading.value = true;
  try {
    const result = await transferStore.fetchTransferById(transferId, "view");    
    record.value = result;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.scrollable-content {
  overflow-y: auto;
  max-height: calc(100vh - 150px);
  padding-right: 16px;
}
</style>
