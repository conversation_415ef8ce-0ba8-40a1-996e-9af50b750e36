<template>
  <v-app>
    <!-- Loading State -->
    <page-loader v-if="isLoading" text="Unlocking your account… 🗝️" />

    <!-- Error / Fallback State -->
    <v-empty-state
      v-else
      height="100vh"
      color="primary"
      :text="errorMessage"
      headline="Oops! Access Denied"
      action-text="Go to login"
      to="/login"
    >
      <template #media>
        <v-icon color="error" icon="mdi-account-lock-outline"></v-icon>
      </template>
    </v-empty-state>
  </v-app>
</template>

<script setup>
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";

// helpers
import { login } from "@/helpers/auth";
import httpClient from "@/plugin/Axios";

// Loader & logo
import PageLoader from "@/components/utils/PageLoader.vue";

// Router and query
const route = useRoute();
const router = useRouter();
const oAuthCode = route.query.oAuthCode;

// State
const isLoading = ref(true);
const errorMessage = ref("Unable to get your super powers… 🦸");

// Exchange OAuth code for token and set session
const exchangeToken = async () => {
  if (!oAuthCode) {
    console.error("No OAuth token in URL!");
    router.replace("/login");
    return;
  }

  try {
    // Call backend to exchange OAuth code
    const { data } = await httpClient.post(
      "/silent-login",
      { oAuthCode },
      { skipTenant: true }
    );

    // Save token and tenant info
    login(data);
    // Handle multiple tenants
    if (data.tenants.length > 1) {
      router.replace("/select-tenant");
      return;
    }
    router.replace("/");
  } catch (err) {
    console.error("Token exchange failed", err);
    errorMessage.value =
      "Something went wrong with authentication. Please login again.";
    isLoading.value = false;
  }
};

// Immediately call exchangeToken
exchangeToken();
</script>

