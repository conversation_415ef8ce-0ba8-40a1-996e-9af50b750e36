import axios from "axios";
import router from "@/router";
import { download } from "@/helpers/downloadHelper";
import { getAuthorizationToken, getTenantId, logout } from "@/helpers/auth";
import globalData from "@/composables/global";
import { useSnackbarStore } from "@/stores/snackBar";
const snackBarStore = useSnackbarStore();

/** -----------------------------------
 * Result Types Definition
 * ----------------------------------- */
export const ResultType = {
    JSON: 'json',
    EXCEL: 'xlsx',
    CSV: 'csv',
    PDF: 'pdf',
};

/**
 * Replace placeholders in URL with actual params
 * Example: "inventory/:id/activate", { id: 42 } => "inventory/42/activate"
 */
export function resolveUrl(template, params = {}) {
  if (!template) return "";
  return template.replace(/:([a-zA-Z0-9_]+)/g, (_, key) => params[key] ?? `:${key}`);
}

/**
 * Singleton Axios instance
 * - Tenant-aware URLs
 * - Authorization token
 * - Global 401/403 handling
 */
const httpClient = axios.create({
  baseURL: globalData.$serverUrl, // default base URL
  headers: {
    "Content-Type": "application/json"
  },
});

// Request interceptor
httpClient.interceptors.request.use((config) => {
  // Start with request-specific baseURL or global default
  let base = config.baseURL || globalData.$serverUrl;

  // Append tenant ID if skipTenant not true
  if (!config.skipTenant) {
    const tenantId = getTenantId?.value;
    if (tenantId) {
      // ✅ Internal change for this request only; singleton remains safe
      base = `${base}/tenants/${tenantId}`;
    }
  }

  config.baseURL = base;

  // Add Authorization token if available
  const token = getAuthorizationToken();
  if (token) config.headers["Authorization"] = token;

  return config;
});

/**
 * Checks if the error response is a blob of type application/json and parses it as JSON.
 * If the blob is not a JSON response, sets the error response data to an object with a message indicating an invalid JSON response.
 * @param {Error} error The error object to check and parse.
 * @returns {Promise<Error>} A promise resolving to the error object with the parsed blob data, if applicable.
 */
const _checkAndParseBlobJson = async (error) => {
  const blob = error.response?.data;
  if (!(blob instanceof Blob) || !blob.type.includes("application/json")) {
    return error;
  }

  try {
    const text = await blob.text();
    error.response.data = JSON.parse(text);
  } catch {
    error.response.data = { message: "Invalid JSON response from server." };
  }

  return error;
};


// Response interceptor
httpClient.interceptors.response.use(
  (response) => response.config.responseType === "blob" ? download(response) : response,
  async (error) => {
    const status = error.response?.status;

    if (status === 401) {
      logout();
    } else if (status === 403) {
      router.push("/not-permitted");
    } else {
      // check while blob is requested and any error throw from server will be json
      await _checkAndParseBlobJson(error);
    }

    return Promise.reject(error);
  }
);

/**
 * Unified API helper
 * @param {Object} options
 * @param {string} options.method - HTTP method ("get", "post", "put", etc.)
 * @param {string} options.url - URL template (can have placeholders like :id)
 * @param {Object} [options.data] - Request body
 * @param {Object} [options.params] - Query params or placeholder values
 * @param {boolean|string} [options.successMessage] - Show success snackbar (true = server msg, string = custom fallback)
 * @param {boolean|string} [options.errorMessage] - Show error snackbar (true = server msg, string = custom fallback)
 */
export async function invokeApi({
  method = "get",
  url = "",
  data = {},
  params = {},
  successMessage = true,
  errorMessage = true,
}) {
  try {
    // Resolve URL placeholders from params
    const resolvedUrl = resolveUrl(url, params);

    // Make API call
    const response = await httpClient.request({
      method,
      url: resolvedUrl,
      data,
      params,
    });
    console.log(response)

    // Show success message
    if (successMessage) {
      const serverMsg = response?.data?.message;
      let msg = serverMsg || (typeof successMessage === "string" ? successMessage : "Operation successful");
      snackBarStore.showSnackbar("success", msg);
    }

    return response.data;
  } catch (err) {
    // Show error message
    if (errorMessage) {
      const serverMsg = err?.response?.data?.message;
      let msg = serverMsg || (typeof errorMessage === "string" ? errorMessage : "Something went wrong");
      snackBarStore.showSnackbar("error", msg);
    }
    throw err; // propagate error
  }
}

// Default export for singleton usage
export default httpClient;