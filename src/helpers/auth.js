import { computed } from "vue";
import globalData from "@/composables/global";

// Define constants for the keys
const AUTH_TOKEN = "_token";
const TENANT_ID = "_tenantId";
const TENANTS = "tenants";
const PRIVILEGES = "privileges";
const USER_ID = "user_id";
const USER_NAME = "user_name";
const USER_DETAILS = "user_details";

// Core Methods
const login = (data) => {
  // a. Codes for set in localStorage
  localStorage.setItem(AUTH_TOKEN, data.token);
  localStorage.setItem(TENANTS, JSON.stringify(data.tenants));

  // b. Codes for set in sessionStorage
  sessionStorage.setItem(AUTH_TOKEN, data.token);
  sessionStorage.setItem(TENANTS, JSON.stringify(data.tenants));

  // Set token, tenants, and user details from tenant[0]
  const firstTenant = data.tenants?.[0];
  if (firstTenant) {
    // If only one tenant, choose/switch automatically
    switchTenant(firstTenant);
  }
};

const switchTenant = (tenant) => {
  // Tenant ID
  sessionStorage.setItem(TENANT_ID, tenant.id);
  localStorage.setItem(TENANT_ID, tenant.id);

  // Privileges
  sessionStorage.setItem(PRIVILEGES, JSON.stringify(tenant.privileges || []));
  localStorage.setItem(PRIVILEGES, JSON.stringify(tenant.privileges || []));

  // User Info
  localStorage.setItem(USER_ID, tenant.userId);
  localStorage.setItem(USER_NAME, tenant.userName);
  sessionStorage.setItem(USER_ID, tenant.userId);
  sessionStorage.setItem(USER_NAME, tenant.userName);

  // User Details
  const userDetails = tenant.userDetails || {};
  localStorage.setItem(USER_DETAILS, JSON.stringify(userDetails));
  sessionStorage.setItem(USER_DETAILS, JSON.stringify(userDetails));
};

const logout = () => {
  // clear sessionStorage (needed use sessionStorage.clear();)
  sessionStorage.removeItem(AUTH_TOKEN);
  sessionStorage.removeItem(TENANT_ID);
  sessionStorage.removeItem(TENANTS);
  sessionStorage.removeItem(PRIVILEGES);
  sessionStorage.removeItem(USER_ID);
  sessionStorage.removeItem(USER_NAME);
  sessionStorage.removeItem(USER_DETAILS);

  // clear localStorage (needed use localStorage.clear();)
  localStorage.removeItem(AUTH_TOKEN);
  localStorage.removeItem(TENANT_ID);
  localStorage.removeItem(TENANTS);
  localStorage.removeItem(PRIVILEGES);
  localStorage.removeItem(USER_ID);
  localStorage.removeItem(USER_NAME);
  localStorage.removeItem(USER_DETAILS);
  window.location.replace(`${globalData.$authUrl}/logout?client_id=${globalData.$clientId}`);
};

const getAuthorizationToken = () => {
  const token = sessionStorage.getItem(AUTH_TOKEN);
  if (token) return token; // early return if session already has it

  // Try to restore from localStorage
  const storedToken = localStorage.getItem(AUTH_TOKEN);
  if (!storedToken) return null; // no token anywhere — user not logged in

  // Restore to sessionStorage
  sessionStorage.setItem(AUTH_TOKEN, storedToken);

  // Restore tenants if present
  const tenantsStr = localStorage.getItem(TENANTS);
  if (tenantsStr) sessionStorage.setItem(TENANTS, tenantsStr);

  // Restore last selected tenant
  const lastTenantId = localStorage.getItem(TENANT_ID);
  if (!lastTenantId) return storedToken;

  sessionStorage.setItem(TENANT_ID, lastTenantId);

  // Parse tenants and switch to the correct one
  const tenants = tenantsStr ? JSON.parse(tenantsStr) : [];
  const selectedTenant = tenants.find(t => t.id === lastTenantId);
  if (selectedTenant) switchTenant(selectedTenant);

  return storedToken;
};

const getUserDetails = () => {
  const data = sessionStorage.getItem(USER_DETAILS);
  return data ? JSON.parse(data) : null;
};
const getUser = () => {
  return {
    userId: localStorage.getItem(USER_ID),
    userName: localStorage.getItem(USER_NAME)
  };
};

const getTenantId = computed(() => {
  return sessionStorage.getItem(TENANT_ID);
});

const getTenants = () => {
  return JSON.parse(sessionStorage.getItem(TENANTS));
};

const getTenant = () => {
  const tenants = getTenants();
  return tenants?.find(t => t.id === getTenantId.value);
};

export {
  // setters
  login,
  logout,
  switchTenant,

  // getters
  getAuthorizationToken,
  getTenants,
  getTenantId,
  getUser,
  getUserDetails,
  getTenant
};
