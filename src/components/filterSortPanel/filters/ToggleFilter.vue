<template>
  <v-row no-gutters>
    <!-- Title -->
    <v-col cols="12">
      <p class="pb-1">{{ title }}</p>
    </v-col>

    <!-- Toggle Buttons -->
    <v-col cols="12">
      <v-btn-toggle
        v-model="internalValue"
        color="primary"
        rounded="xl"
        divided
        variant="outlined"
        class="w-100"
        mandatory
        @update:model-value="emitUpdate"
      >
        <v-btn
          v-for="item in items"
          :key="item.name"
          :value="item.value"
          class="text-capitalize flex-grow-1"
        >{{ item.name }}</v-btn>
      </v-btn-toggle>
    </v-col>
  </v-row>
</template>

<script setup>
import { ref, watch } from "vue";

const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, null],
    default: null
  },
  title: {
    type: String,
    required: true
  },
  items: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(["update:modelValue"]);

// internal value to manage v-model
const internalValue = ref(props.modelValue);

// sync internal value when parent updates
watch(
  () => props.modelValue,
  val => {
    internalValue.value = val === null ? 0 : val;
  },
  {
    immediate: true
  }
);

// emit changes to parent, treat 0 as null
const emitUpdate = val => {
  const updated = val === 0 ? null : val;
  emit("update:modelValue", updated);
};
</script>
