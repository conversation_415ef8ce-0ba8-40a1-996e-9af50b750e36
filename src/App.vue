<template>
  <main>
    <!-- message -->
    <v-snackbar
      v-model="store.snackbar.show"
      :color="store.snackbar.color"
      :timeout="store.snackbar.timeout"
      location="top right"
      transition="scroll-y-transition"
    >
      {{ store.snackbar.message }}
      <template v-slot:actions>
        <v-btn flat @click="store.hideSnackbar" icon="mdi-close" />
      </template>
    </v-snackbar>

    <!-- Global Loader -->
    <LoaderDialog ref="loaderRef" />

    <!-- Global Confirm Dialog -->
    <ConfirmDialog ref="confirmRef" />

    <router-view></router-view>
  </main>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, onMounted } from "vue";
import { initTheme } from "@/plugin/vuetifyTheme";
import ConfirmDialog from "@/components/utils/ConfirmDialog.vue";
import LoaderDialog from "@/components/utils/LoaderDialog.vue";

import { useSnackbarStore } from "./stores/snackBar";
const store = useSnackbarStore();

const confirmRef = ref(null);
const loaderRef = ref(null);

const app = getCurrentInstance().appContext.app;

nextTick(() => {
  if (confirmRef.value) {
    // Expose globally
    app.config.globalProperties.$confirm = confirmRef.value.open;
    app.provide("confirm", confirmRef.value.open);
  } else {
    console.error("ConfirmDialog ref is null after nextTick");
  }

  // global loader
  if (loaderRef.value) {
    // Expose globally
    app.config.globalProperties.$loader = loaderRef.value;
    app.provide("loader", loaderRef.value);
  } else {
    console.error("Loader ref is null after nextTick");
  }
});

onMounted(() => {
  initTheme();
});
</script>

<style>
/* For Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

* {
  font-family: ui-sans-serif, -apple-system, system-ui, Segoe UI, Helvetica,
    Apple Color Emoji, Arial, sans-serif, Segoe UI Emoji, Segoe UI Symbol !important;
}
</style>
