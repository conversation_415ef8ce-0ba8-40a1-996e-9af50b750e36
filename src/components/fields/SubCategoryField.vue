<template>
  <auto-complete
    v-model="model"
    :items="categories"
    v-bind="$attrs"
    :label="label"
    item-title="name"
    item-value="id"
    :filter-keys="['title', 'raw.categoryName']"
    :loading="loading"
  />
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { groupListForVuetify } from "@/helpers/vuetifyGroupHelpers.js";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Sub Categories"
  },
  /** Component-specific attributes */
  categories: {
    // selected categories (ids)
    type: Array,
    default: () => []
  }
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const categories = computed(() => {
  const data = masterStore.getSubCategories({
    categoryId: props.categories
  });
  return groupListForVuetify(data, "categoryName");
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});
</script>
