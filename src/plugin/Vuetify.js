import "@mdi/font/css/materialdesignicons.css";
import "vuetify/styles";
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import { VDateInput } from "vuetify/labs/VDateInput";
import { VPie } from "vuetify/labs/VPie";
import { VFileUpload, VFileUploadItem } from 'vuetify/labs/VFileUpload'

const stateColors = {
  primary: "#E95420",
  secondary: "#555753",
  warning: "#F99B11",             // warning_color
  error: "#C7162B",               // error_color
  success: "#0E8420",             // success_color
  info: "#2196F3",                // default info 
};

// themes/chatGPTThemes.js
export const lightTheme = {
  dark: false,
  colors: {
    ...stateColors,
    nav: "#223345",
    /*background: "#FFFFFF",
    surface: "#F7F7F8",
    "on-background": "#111827",
    "on-surface": "#111827",
    */
  },
};

export const darkTheme = {
  dark: true,
  colors: {
    ...stateColors,
    nav: '#212121',
  },
};


// -------------------- APP CONFIG --------------------
// Create Vuetify instance
const vuetify = createVuetify({
  components: {
    ...components,
    VDateInput,
    VPie,
    VFileUpload,
    VFileUploadItem
  },
  directives,
  theme: {
    themes: {
      lightTheme,
      darkTheme
    },
  },
});

export default vuetify;
