<template>
  <v-container>
    <v-row>
      <v-col cols="12" class="pt-3 pb-0">
        <v-autocomplete
          :label="filter.title"
          v-model="selectedValue"
          :items="items"
          item-title="name"
          item-value="id"
          multiple
          clearable
          density="compact"
          color="primary"
          variant="outlined"
          hide-details="auto"
        >
          <template v-slot:selection="{ item, index }">
            <selection-view
              :item="item"
              :index="index"
              :data="selectedValue"
            ></selection-view>
          </template>
          <template v-slot:prepend-item>
            <v-list-item ripple @click="toggle">
              <div class="d-flex align-center px-2">
                <v-icon :icon="icon"></v-icon>
                <v-list-item-title class="mx-2"> Select All </v-list-item-title>
              </div>
              <v-divider class="mt-2"></v-divider>
            </v-list-item>
          </template>
        </v-autocomplete>
      </v-col>
    </v-row>
  </v-container>
</template>
<script setup>
import { ref, computed, inject, watch } from "vue";
import SelectionView from "@/components/base/SelectionView.vue";

const props = defineProps({
  filter: {
    type: Object,
    default: () => {},
  },
});

const filters = inject("filters");

const items = computed(() => {
  if (props.filter.key == "inventoryLocations") {
    return props.filter.items.filter((item) =>
      filters.value.locations?.includes(item.locationId)
    );
  }
  return props.filter.items;
});

const selectedValue = ref([]);

const selectAllData = computed(
  () => selectedValue.value.length === items.value.length
);

const selectSomeData = computed(
  () => selectedValue.value.length > 0 && !selectAllData.value
);

const icon = computed(() => {
  if (selectAllData.value) return "mdi-close";
  if (selectSomeData.value) return "mdi-minus-box-outline";
  return "mdi-checkbox-blank-outline";
});

const toggle = () => {
  if (selectAllData.value) {
    selectedValue.value = [];
  } else {
    selectedValue.value = items.value.map((item) => item.id);
  }
};

watch(selectedValue, (val) => {
  filters.value[props.filter.key] = val;
});

watch(items, (newItems) => {
  if (!newItems.length) return;
  if (!props.filter.default) {
    selectedValue.value = [];
    filters.value[props.filter.key] = [];
    return;
  }
  if (!selectedValue.value.length) {
    selectedValue.value = [newItems[0].id];
    return;
  }
  selectedValue.value = newItems
    .filter((item) => selectedValue.value.includes(item.id))
    .map((item) => item.id);
});
</script>
