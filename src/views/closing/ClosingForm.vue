<template>
    <div style="height: 100%">
      <page-loader v-if="loading" />
  
      <!-- Form Actions Bar -->
      <form-actions-bar
        v-if="!loading"
        @close="navigatePrevious"
        :hide-submit="true"
        :loading="loading"
      >
        <template #actions>
            <!-- <v-btn
            text="Import Data"
            @click="loadData"
            variant="flat"
            color="primary"
            class="ml-2"
            :loading="loading"
            :disabled="loading"
          /> -->
          <v-btn
            text="Submit"
            @click="create"
            variant="flat"
            color="primary"
            class="ml-2"
            :loading="loading"
            :disabled="loading"
          />

        </template>
      </form-actions-bar>
  
      <v-container v-if="!loading" fluid>
        <v-form ref="form" v-model="formValid">
          <v-row>
            <!-- From Location -->
            <v-col cols="12" sm="6" md="3">
              <location-field
                v-model="closingLocation"
                label="Closing Location"
                hint="Closing Location"
                persistent-hint
                mandatory
                return-object
                @update:model-value="locationChange"
              />
            </v-col>

            <!-- From WorkArea -->
            <v-col cols="12" sm="6" md="3">
              <work-area-field
                ref="fromRef"
                v-model="closingWorkArea"
                label="Closing WorkArea"
                hint="Closing WorkArea"
                persistent-hint
                mandatory
                return-object
                :locations="closingLocation ? [closingLocation.id] : null"
                :disabled="!closingLocation"
                @update:model-value="workAreaChange"
              />
            </v-col>

            <!-- Closing Date -->
            <v-col cols="12" sm="6" md="3">
              <v-date-input
                v-model="closingDate"
                label="Closing Date*"
                color="primary"
                :max="todayDate"
                variant="outlined"
                density="compact"
                hide-details="auto"
                prepend-icon
                prepend-inner-icon="$calendar"
                :rules="[rules.require]"
                hide-actions
              ></v-date-input>
            </v-col>

            <!-- Overwrite Live Stock Toggle -->
            <v-col cols="12" sm="6" md="3" >
              <v-switch
                v-model="stockCorrection"
                label="Overwrite Live Stock"
                color="success"
                base-color="error"
                hide-details
                inset
                density="compact"
                @click.prevent="handleStockCorrectionToggle"
              />
            </v-col>
          </v-row>
        </v-form>
  
        <!-- Table -->
        <closing-table
          :closingList="tableItems"
          :formValid="formValid"
          @addItem="openForm = true"
          @removeItem="remove"
        />
  
        <closing-item-form
          v-if="closingLocation"
          v-model="openForm"
          :formValid="formValid"
          :locationId="closingLocation ? closingLocation.id : null"
          @add="add"
          :existingItems="tableItems"
        />
      </v-container>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, nextTick, inject } from "vue";
  import { useRouter } from "vue-router";
  import { useClosingStore } from "@/stores/closing";
  import { useSnackbarStore } from "@/stores/snackBar";
  import rules from "@/helpers/rules";
  import ClosingTable from "@/views/closing/ClosingTable.vue";
  import FormActionsBar from "@/components/utils/FormActionsBar.vue";
  import PageLoader from "@/components/utils/PageLoader.vue";
  import WorkAreaField from "@/components/fields/WorkAreaField.vue";
  import LocationField from "@/components/fields/LocationField.vue";
  import ClosingItemForm from "@/views/closing/ClosingItemForm.vue";
  
  const router = useRouter();
  const { showSnackbar } = useSnackbarStore();
  const closingStore = useClosingStore();
  const $confirm = inject("confirm");

  
  const openForm = ref(false);
  const form = ref(null);
  const formValid = ref(false);
  const loading = ref(false);

  const closingLocation = ref(null);
  const closingWorkArea = ref(null);
  const closingDate = ref(null);
  const stockCorrection = ref(false);

  const fromRef = ref(null);

  const tableItems = ref([]);
  const todayDate = new Date().toISOString().split("T")[0];
  
  const add = (pr) => {
    tableItems.value.unshift(pr);
  };
  
  const remove = (index) => {
    tableItems.value.splice(index, 1);
  };

  const handleStockCorrectionToggle = async () => {
    if (!$confirm) {
      console.error("Global confirm dialog not available");
      return;
    }

    // If trying to turn ON the toggle, show confirmation
    if (!stockCorrection.value) {
      const confirmed = await $confirm(
        "Are you sure you want to overwrite the live stock?",
        { title: "Overwrite Live Stock?", confirmText: "Overwrite" }
      );
      if (confirmed) {
        stockCorrection.value = true;
      }
    } else {
      // If turning OFF, no confirmation needed
      stockCorrection.value = false;
    }
  };

  const create = async () => {
    const { valid } = await form.value.validate();
    if (!valid) return;

    if (!tableItems.value.length) {
      showSnackbar("primary", "At least one item is required");
      return;
    }
    loading.value = true;
    try {
      const payload = {
        closingDate: closingDate.value,
        locationId: closingLocation.value?.id,
        locationName: closingLocation.value?.name,
        workAreaId: closingWorkArea.value?.id,
        workAreaName: closingWorkArea.value?.name,
        stockCorrection: stockCorrection.value,
        items: tableItems.value.map((i) => ({
          itemId: i.itemId,
          itemName: i.itemName,
          itemCode: i.itemCode,
          categoryId: i.categoryId,
          subcategoryId: i.subCategoryId,
          categoryName: i.categoryName,
          subcategoryName: i.subCategoryName,
          closingQuantity: i.requestedQuantity,
          countingUOM: i.countingUOM,
          // unitCost: i.unitCost ? i.unitCost : 0,
          pkg: {
            ...i.pkg,
            id: i.pkg?.id ? i.pkg.id : "default",
            name: i.pkg?.name ? i.pkg.name : "Default",
          },
        })),
      };

      // await closingStore.createClosingData(payload);

      // Clear form data after successful creation
      tableItems.value = [];
      closingLocation.value = null;
      closingWorkArea.value = null;
      closingDate.value = null;
      stockCorrection.value = false;

      navigatePrevious();
    } catch (err) {
      console.error(err);
    } finally {
      loading.value = false;
    }
  };

  const loadData = async () => {}
  
  const navigatePrevious = () => router.push({ name: "closing" });

  
  const locationChange = async (loc) => {
    // Check if table has items and show confirmation dialog
    if (tableItems.value.length > 0) {
      const confirmed = await $confirm(
        "Changing the location will reset the table data. Do you want to proceed?",
        { title: "Reset Table Data", confirmText: "Yes" }
      );
      if (!confirmed) {
        return; // Do not proceed if user cancels
      }
      // Clear table data if user confirms
      tableItems.value = [];
    }

    closingLocation.value = loc;
    closingWorkArea.value = null;
    if (loc && fromRef.value) {
      fromRef.value.setDefault(loc.inventoryLocationId);
    }
  };

  const workAreaChange = async (workArea) => {
    // Check if table has items and show confirmation dialog
    if (tableItems.value.length > 0) {
      const confirmed = await $confirm(
        "Changing the location will reset the table data. Do you want to proceed?",
        { title: "Reset Table Data", confirmText: "Yes" }
      );
      if (!confirmed) {
        return; // Do not proceed if user cancels
      }
      // Clear table data if user confirms
      tableItems.value = [];
    }

    closingWorkArea.value = workArea;
  }
  
  const focusFirstField = () => {
    // Focus on the first available field (From Location)
    const firstInput = document.querySelector('.v-input input');
    if (firstInput) firstInput.focus();
  };

  onMounted(async () => {
    await nextTick();
    focusFirstField();

    window.addEventListener('keydown', (e) => {
      if (e.key === 'Tab' && document.activeElement === document.body) {
        e.preventDefault();
        focusFirstField();
      }
    });
  });
  
  </script>
  