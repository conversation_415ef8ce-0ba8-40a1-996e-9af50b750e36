<template>
  <v-dialog v-model="visible" width="auto" persistent>
    <v-card border min-width="300">
      <v-card-text>
        <v-row justify="center" align="center">
          <v-col class="text-center">
            <p class="mb-4">{{ text }}</p>
            <v-progress-linear indeterminate color="primary" />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref } from "vue";

const DEFAULT_MESSAGE = "Please wait...";
const visible = ref(false);
const text = ref(DEFAULT_MESSAGE);

function show(msg) {
  text.value = msg || DEFAULT_MESSAGE;
  visible.value = true;
}

function hide() {
  visible.value = false;
  // Reset message after hiding (to restore default next time)
  text.value = DEFAULT_MESSAGE;
}

defineExpose({ show, hide });
</script>
