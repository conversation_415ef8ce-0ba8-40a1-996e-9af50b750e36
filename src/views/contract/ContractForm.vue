<template>
  <div style="height: 100%" v-smart-focus>
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="close"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          :text="isEdit ? 'Update' : 'Create'"
          @click="save"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loader"
          :disabled="loader"
        />
      </template>
    </form-actions-bar>

    <v-container fluid v-show="!loading">
      <v-form ref="form" v-model="formValid">
        <v-row>
          <v-col cols="12" md="4">
            <v-text-field
              v-model.trim="record.name"
              label="Contract Name*"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              :rules="[rules.require]"
              autofocus
            />
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              v-model.trim="record.reference"
              label="Contract Reference*"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              :rules="[rules.require]"
            />
          </v-col>
          <v-col cols="12" md="4">
            <vendor-field v-model="record.vendor" mandatory return-object />
          </v-col>
          <v-col cols="12" md="4">
            <location-field
              v-model="record.location"
              multiple
              mandatory
              return-object
            ></location-field>
          </v-col>
          <v-col cols="12" md="4">
            <v-date-input
              v-model="record.startDate"
              label="Start Date*"
              color="primary"
              :min="todayDate"
              :max="record?.endDate"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              hide-actions
              :readonly="isEdit"
            ></v-date-input>
          </v-col>
          <v-col cols="12" md="4">
            <v-date-input
              v-model="record.endDate"
              label="Expiry Date*"
              color="primary"
              :min="record.startDate"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
              :readonly="isEdit"
            ></v-date-input>
          </v-col>
          <v-col
            cols="12"
            v-if="
              record.attachments &&
              record.attachments.length > 0 &&
              !route.query.cloneId
            "
          >
            <Attachments
              type="contract"
              :item-id="record.id"
              :attachments="record.attachments"
            />
          </v-col>
          <v-col>
            <upload-attachment
              ref="uploadRef"
              @update="updateAttachments"
            ></upload-attachment>
          </v-col>
          <v-col cols="12">
            <contract-table
              :contractList="tableItems"
              :formValid="formValid"
              @addItem="openForm = true"
              @removeItem="remove"
            />
          </v-col>

          <contract-item-form
            v-model="openForm"
            :formValid="formValid"
            @add="add"
            :existingItems="tableItems"
            :selectedVendorId="record.vendor ? record.vendor.id : ''"
          />
        </v-row>
      </v-form>
    </v-container>
  </div>
</template>

<script setup>
import { ref, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { contractRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import LocationField from "@/components/fields/LocationField.vue";
import VendorField from "@/components/fields/VendorField.vue";
import ContractTable from "./ContractTable.vue";

import { useContractStore } from "@/stores/contract";
import Attachments from "@/components/attach/Attachments.vue";
import UploadAttachment from "@/components/attach/UploadAttachment.vue";
import ContractItemForm from "./ContractItemForm.vue";
import { useSnackbarStore } from "@/stores/snackBar";

const { showSnackbar } = useSnackbarStore();
const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

const loading = ref(false);
const loader = ref(false);
const route = useRoute();
const router = useRouter();
const contractId = route.params.id;
const isEdit = contractId !== undefined;
const todayDate = new Date().toISOString().split("T")[0];

const contractStore = useContractStore();

const form = ref(null);
const openForm = ref(false);
const formValid = ref(false);
const uploadRef = ref(null);

const tableItems = ref([]);

const add = (pr) => {
  tableItems.value.unshift(pr);
};

const remove = (index) => {
  tableItems.value.splice(index, 1);
};

const updateAttachments = (uploadedFiles) => {
  if (!record.value.attachments) record.value.attachments = [];
  record.value.attachments.push(...uploadedFiles);
};

const save = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  if (!tableItems.value.length) {
    showSnackbar("primary", "At least one item is required");
    return;
  }

  loader.value = true;
  try {
    const locations = record.value.location.map(
      ({ inventoryLocationId, ...rest }) => rest
    );
    const payload = {
      name: record.value.name,
      reference: record.value.reference,
      startDate: record.value.startDate,
      endDate: record.value.endDate,
      location: locations,
      vendor: record.value.vendor,

      items: tableItems.value.map((i) => ({
        itemId: i.itemId,
        itemName: i.itemName,
        itemCode: i.itemCode,
        contractType: i.contractType,
        contractPrice: i.contractPrice,
        countingUOM: i.countingUOM,
        inclTax: i.inclTax,
        pkg: {
          id: i.pkg?.id ? i.pkg.id : "default",
          name: i.pkg?.name ? i.pkg.name : "Default",
        },
      })),
    };

    if (isEdit) {
      payload.id = record.value.id;
      await contractStore.updateContract(payload);
      if (uploadRef.value)
        await uploadRef.value.upload(
          "contract",
          payload.id,
          `/contracts/${payload.id}/attachments`
        );
    } else {
      const newContract = await contractStore.createContract(payload);
      if (uploadRef.value)
        await uploadRef.value.upload(
          "contract",
          newContract.id,
          `/contracts/${newContract.id}/attachments`
        );
    }

    close();
  } catch (error) {
    console.error("Error saving contract:", error);
  } finally {
    loader.value = false;
  }
};

const loadContractData = async () => {
  switch (true) {
    case isEdit: {
      const result = await contractStore.fetchContractById(contractId);
      record.value = result;
      tableItems.value = result.items;
      break;
    }

    case !!route.query.cloneId: {
      const result = await contractStore.fetchContractById(route.query.cloneId);
      record.value = {
        ...result,
        id: null,
      };
      record.value.startDate = new Date();
      record.value.endDate = null;
      tableItems.value = JSON.parse(JSON.stringify(result.items));
      break;
    }

    default: {
      record.value.startDate = record.value.startDate
        ? record.value.startDate
        : new Date();
      break;
    }
  }
};

const close = () => {
  router.push({ name: "contract" });
};

onBeforeMount(async () => {
  await loadContractData();
});
</script>

<style scoped></style>
