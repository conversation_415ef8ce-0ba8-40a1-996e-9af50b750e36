<template>
  <v-dialog v-model="model" width="450" persistent>
    <v-card>
      <v-card-title
        class="d-flex justify-space-between align-center"
      >
        <p>
          {{ isEdit ? "Edit Work/Storage Area" : "Create Work/Storage Area" }}
        </p>
        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider class="mb-4"/>

      <v-card-text class="py-4">
        <v-form ref="form">
          <!-- Location Selection -->

          <location-field
            v-model="storeId"
            :items="props.stores"
            item-value="id"
            item-title="name"
            label="Location *"
            hide-details
            variant="outlined"
            density="compact"
            color="primary"
            class="mb-6"
            :clearable="!(isEdit || props.fromStore)"
            :rules="[rules.require]"
            :disabled="isEdit || props.fromStore"
          />

          <!-- Work/Storage Area Name -->
          <v-text-field
            v-model.trim="location.name"
            label="Work/Storage Area Name*"
            color="primary"
            variant="outlined"
            density="compact"
            hide-details="auto"
            class="mb-6"
            @blur="cleanName"
            :rules="[rules.require, rules.maxLength(100)]"
          />
          <!-- Tag Selection (added below location) -->
          <v-autocomplete
            v-model="selectedTag"
            :items="props.tags"
            item-value="id"
            item-title="name"
            label="Tag"
            hide-details="auto"
            variant="outlined"
            density="compact"
            color="primary"
            clearable
            class="mb-6"
            persistent-hint
            messages="*if no tag is selected, the work area will have access to all menu items by default."
          ></v-autocomplete>

        </v-form>
      </v-card-text>

      <v-divider class="mt-4"/>

      <v-card-actions class="d-flex flex-column mx-2 mb-2">
        <p class="text-primary text-caption text-center">
          * Indicates a Required Field.
        </p>
        <div class="d-flex justify-end ga-2 w-100">
          <v-btn
            color="primary"
            variant="flat"
            @click="submit(true)"
            :loading="loadBtn === 'save'"
            :disabled="loadBtn !== null"
          >
            Save
          </v-btn>
          <v-btn
            v-if="!isEdit"
            color="primary"
            variant="flat"
            @click="submit(false)"
            :loading="loadBtn === 'continue'"
            :disabled="loadBtn !== null"
          >
            Save & Continue
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import { formatName } from "@/helpers/formatter";
import rules from "@/helpers/rules";
import LocationField from "@/components/fields/LocationField.vue";

const props = defineProps({
  isEdit: Boolean,
  fromStore: Boolean,
  locationData: Object,
  storeId: String,
  stores: Array,
  tags: Array, // <-- added for tag selection
});

const model = defineModel();
const emit = defineEmits(["createLocation", "updateLocation"]);

const form = ref(null);

const location = ref({
  name: "",
});

const storeId = ref("");
const selectedTag = ref(""); // tag selection

const resetForm = () => {
  location.value = { name: "" };
  selectedTag.value = "";
};

const cleanName = () => {
  location.value.name = formatName(location.value.name);
};

watch(
  () => props.storeId,
  (newVal) => {
    if (newVal) storeId.value = newVal;
  },
  { immediate: true } 
);

watch(model, (open) => {
  if (!open) return;

  if (props.isEdit && props.locationData) {
    location.value = { ...props.locationData };
    storeId.value = props.locationData.locationId || props.storeId;
    selectedTag.value = props.locationData.tagId || "";
  } else {
    resetForm();
  }
});

watch(selectedTag, (val) => {
  if (val === null) selectedTag.value = "";
});

const loadBtn = ref(null);

const submit = async (closeAfter = true) => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const selectedStore = props.stores.find((store) => store.id === storeId.value);

  loadBtn.value = closeAfter ? "save" : "continue";

  const payload = props.isEdit
  ? { ...location.value, tagId: selectedTag.value || "" }
  : { ...location.value, locationId: storeId.value, locationName: selectedStore?.name, tagId: selectedTag.value || "" };

  const done = (success) => {
    loadBtn.value = null;
    if (!success) return;
    if (closeAfter) {
      model.value = false;
    } else {
      resetForm();
    }
  };

  if (props.isEdit) {
    emit("updateLocation", payload, done);
  } else {
    emit("createLocation", payload, done);
  }
};

const closeDialog = () => {
  model.value = false;
};
</script>
