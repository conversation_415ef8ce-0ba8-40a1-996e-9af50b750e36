<template>
  <div>
    <!-- Report Header -->
    <ReportHeader
      v-model="payload"
      enable-items
      :loading="loading"
      @search="onSearch"
    />

    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="stockHeaders"
            :items="stocks"
            :loading="loading"
            :hide-default-footer="stocks.length < 11"
            no-data-text="No stocks found"
          ></v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

import httpClient from "@/plugin/Axios";
import { NewReportPayload } from "@/helpers/useReportPayload";
import { stockHeaders } from "@/helpers/tableHeaders";

// components
import ReportHeader from "@/components/utils/ReportHeader.vue";

const loading = ref(false);
const payload = ref(NewReportPayload("stock-view"));
const stocks = ref([]);

const onSearch = async (reqPayload) => {
  loading.value = true;
  stocks.value = [];
  try {
    const res = await httpClient.post("/reports/stocks", reqPayload);
    stocks.value = res.data;
  } catch ({ response }) {
    // @todo:
    throw Error(response.data.message);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  onSearch(payload.value);
});
</script>
