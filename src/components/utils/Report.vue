<template>
  <div>
    <!-- Report Header -->
    <ReportHeader
      v-model="payload"
      enable-date
      enable-vendors
      :enable-items="!disableItemFilter"
      :loading="loading"
      @search="onSearch"
      :enable-export="['xlsx']"
    />

    <!-- REPORT -->
    <v-container fluid v-if="rt === ResultType.JSON && !error">
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="response.headers"
            :items="response.data"
            :loading="loading"
            :items-per-page="itemPerPage"
            :hide-default-footer="showPagination"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>

            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <template
              v-slot:item="{ columns, item, internalItem, isExpanded, toggleExpand }"
              v-if="response.expand"
            >
              <tr>
                <td>
                  <v-btn
                    :icon="isExpanded(internalItem) ? '$expand' : '$next'"
                    color="medium-emphasis"
                    density="compact"
                    variant="outlined"
                    @click="toggleExpand(internalItem)"
                  ></v-btn>
                </td>
                <td
                  v-for="col in columns.slice(1)"
                  :key="item[col.key]"
                  :class="[
                  'v-data-table__td', col.align ? `v-data-table-column--align-${col.align}` : ''
                  ]"
                  class="font-weight-bold"
                >{{ item[col.key] }}</td>
              </tr>
            </template>

            <template v-slot:expanded-row="{ columns, item }">
              <tr v-for="row in item.subItems" :key="row.id">
                <td
                  v-for="col in columns"
                  :key="row[col.key]"
                  :class="['v-data-table__td', col.align ? `v-data-table-column--align-${col.align}` : '']"
                >{{ row[col.key] }}</td>
              </tr>
            </template>

            <template #tfoot="{columns}" v-if="Object.keys(response.totalRow).length">
              <tr class="sticky-bottom-row">
                <td>Total</td>
                <td
                  v-for="col in columns.slice(1)"
                  :key="response.totalRow[col.key]"
                  class="text-end"
                >{{ response.totalRow[col.key] }}</td>
              </tr>
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
    <!-- Message/Loader -->
    <ResultMessage v-else :loading="loading" :rt="rt" :error="error" />
  </div>
</template>

<script setup>
import { computed, ref } from "vue";

import httpClient from "@/plugin/Axios";
import { ResultType } from "@/plugin/Axios";
import { NewReportPayload } from "@/helpers/useReportPayload";

import ReportHeader from "@/components/utils/ReportHeader.vue";
import ResultMessage from "./ResultMessage.vue";

const props = defineProps({
  id: {
    // report reference id
    type: String,
    required: true
  },
  url: {
    // fetch url
    type: String,
    required: true
  },
  disableItemFilter: {
    type: Boolean,
    default: false
  }
});

const loading = ref(false);
const payload = ref(NewReportPayload(props.id));
const rt = ref(null);
const error = ref(null);
const response = ref({
  data: [],
  totalRow: {},
  headers: []
});

const itemPerPage = ref(20); // default 20;
const showPagination = computed(
  () => response.value.data?.length > itemPerPage.value
);

const onReset = () => {
  error.value = null;
  response.value = {
    data: [],
    totalRow: {},
    headers: []
  };
};

const onSearch = async ({ reqPayload, resultType = ResultType.JSON }) => {
  loading.value = true;
  rt.value = resultType; // record last result type
  onReset();

  try {
    const config = { resultType };
    if (resultType !== ResultType.JSON) {
      config.responseType = "blob";
    }
    const res = await httpClient.post(
      `${props.url}?resultType=${resultType}`,
      reqPayload,
      config
    );
    response.value = res.data;
  } catch ({ response }) {
    error.value = response.data?.error || "Something went wrong.";
  } finally {
    loading.value = false;
  }
};
</script>
