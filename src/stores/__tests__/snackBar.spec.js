import { setActivePinia, createP<PERSON> } from "pinia";
import { describe, it, beforeEach, expect } from "vitest";
import { useSnackbarStore } from "@/stores/snackBar";

describe("Snackbar Store", () => {
  let snackbarStore;

  beforeEach(() => {
    setActivePinia(createPinia());
    snackbarStore = useSnackbarStore();
  });

  it("should show snackbar with custom values", () => {
    snackbarStore.showSnackbar("error", "Test error message");

    expect(snackbarStore.snackbar.show).toBe(true);
    expect(snackbarStore.snackbar.color).toBe("error");
    expect(snackbarStore.snackbar.message).toBe("Test error message");
    expect(snackbarStore.snackbar.timeout).toBe(3000);
  });

  it("should show snackbar with default values", () => {
    snackbarStore.showSnackbar();

    expect(snackbarStore.snackbar.show).toBe(true);
    expect(snackbarStore.snackbar.color).toBe("success");
    expect(snackbarStore.snackbar.message).toBe("");
    expect(snackbarStore.snackbar.timeout).toBe(3000);
  });

  it("should hide snackbar", () => {
    snackbarStore.hideSnackbar();

    expect(snackbarStore.snackbar.show).toBe(false);
    expect(snackbarStore.snackbar.message).toBe("");
  });
});
