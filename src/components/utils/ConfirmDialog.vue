<template>
  <!-- Dialog wrapper -->
  <v-dialog v-model="dialog" :max-width="options.width" persistent @keydown.esc="cancel">
    <v-card :title="options.title">
      <v-divider />

      <!-- Message -->
      <v-card-text class="text-center" :style="options.style" v-html="message" />

      <v-divider />

      <!-- Info mode (single OK button) -->
      <v-card-actions v-if="options.info">
        <v-btn block variant="outlined" color="error" id="confirm_info" @click="cancel">
          {{ options.infoBtn }}
        </v-btn>
      </v-card-actions>

      <!-- Confirm mode (Yes/No buttons) -->
      <v-card-actions v-else>
        <v-row dense>
          <v-col cols="6">
            <v-btn block variant="tonal" color="error" id="confirm_no" @click="cancel">
              {{ options.cancelBtn }}
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn block variant="flat" color="success" id="confirm_yes" @click="agree">
              {{ options.okBtn }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, reactive } from "vue";

/**
 * Props allow customizing default labels, title, and message
 */
const props = defineProps({
  defaultTitle: { type: String, default: "Confirmation" },
  defaultMessage: { type: String, default: "Are you sure?" },
  defaultOkBtn: { type: String, default: "Yes" },
  defaultCancelBtn: { type: String, default: "No" },
  defaultInfoBtn: { type: String, default: "OK" },
  defaultWidth: { type: Number, default: 350 },
});

/**
 * State
 */
const dialog = ref(false);
const resolveFn = ref(null);
const message = ref(null);

const options = reactive({
  infoBtn: props.defaultInfoBtn,
  okBtn: props.defaultOkBtn,
  cancelBtn: props.defaultCancelBtn,
  title: props.defaultTitle,
  width: props.defaultWidth,
  info: false,
  style: {},
});

/**
 * Open the dialog
 */
function open(msg, opts = {}) {
  dialog.value = true;
  message.value = msg || props.defaultMessage;
  Object.assign(options, init(), opts);
  return new Promise((resolve) => {
    resolveFn.value = resolve;
  });
}

/**
 * Info-only mode
 */
function info(msg, opts = {}) {
  opts.info = true;
  return open(msg, opts);
}

/**
 * User agrees
 */
function agree() {
  if (resolveFn.value) resolveFn.value(true);
  dialog.value = false;
}

/**
 * User cancels
 */
function cancel() {
  if (resolveFn.value) resolveFn.value(false);
  dialog.value = false;
}

/**
 * Reset options to defaults
 */
function init() {
  return {
    infoBtn: props.defaultInfoBtn,
    okBtn: props.defaultOkBtn,
    cancelBtn: props.defaultCancelBtn,
    title: props.defaultTitle,
    width: props.defaultWidth,
    info: false,
    style: {},
  };
}

/**
 * Expose functions for parent
 */
defineExpose({
  open,
  info,
});
</script>
