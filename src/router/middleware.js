// src/router/middleware.js
import { getAuthorizationToken, getTenant } from "@/helpers/auth";

/**
 * Auth guard middleware for routes.
 * Checks if user is logged in and has the required privilege
 * for the route. If not, redirects to login or access-not-permitted page.
 * @param {Object} to - The route object that is being navigated to.
 * @returns {Object|boolean} - Either an object with the name of the route to redirect to,
 * or true if the navigation is allowed.
 */
export function authGuard(to) {
  const token = getAuthorizationToken();

  // Redirect logged-in users away from login
  if (to.name === "login") {
    if (token) {
      return {
        name: "home",
      };
    }
    return true; // allow login if not logged in
  }

  // Skip routes that do not require auth
  if (to.meta?.noAuth) {
    return true;
  }

  // Check token for authenticated routes
  if (!token) {
    return {
      name: "login",
    };
  }

  if (to.name !== "select-tenant") {
    if (!getTenant()) {
      return {
        name: "select-tenant",
      };
    }
  }

  // Check privilege if route requires it
  if (!checkUserPrivilege(to.meta?.privilege_code)) {
    return {
      name: "access-not-permitted",
    };
  }

  // All checks passed, allow navigation
  return true;
}

/**
 * Checks if a user has a specific privilege.
 *
 * @param {string} privilegeCode - The privilege code to check.
 * @returns {boolean} True if the user has the privilege, false otherwise.
 *
 * @example
 * checkUserPrivilege('PUR_PR') // true if user has PUR_PR privilege, false otherwise
 */
export function checkUserPrivilege(privilegeCode) {
  /**
   * If no privilege is provided, we consider it as true (no privilege required).
   */
  if (!privilegeCode) {
    return true;
  }

  const tenant = getTenant();
  if (!tenant) {
    return false;
  }

  /**
   * If the user is an admin, we consider it as true (all privileges are granted).
   */
  const { isAdmin, privileges: userPrivileges } = tenant;

  if (isAdmin) {
    return true;
  }

  /**
   * Checks if the user has the privilege in their list of privileges.
   */
  return userPrivileges.includes(privilegeCode);
}
