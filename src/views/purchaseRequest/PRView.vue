<template>
  <div>
    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      :back-to="{ name: 'purchase-requests' }"
      hide-submit
      :loading="loading"
    >
      <template #custom>#{{ cart.prNumber }}</template>

      <template #actions>
        <PROptions
          :status="cart.status"
          @refresh="get"
          :itemId="purchaseRequestId"
        />
      </template>
    </form-actions-bar>

    <!-- loader -->
    <page-loader v-if="loading" />

    <!-- Overview/Content -->
    <v-container v-if="!loading" fluid>
      <PrOverview :data="cart" />

      <!-- Item Table -->
      <PRTableItems
        :items="tableItems"
        :cart="cart"
        :showVendor="cart.vendorType !== 2"
      />
    </v-container>
  </div>
</template>

<script setup>
import { ref, onBeforeMount } from "vue";
import { useRoute } from "vue-router";

import { usePurchaseRequestStore } from "@/stores/purchaseRequest";

// components
import PageLoader from "@/components/utils/PageLoader.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PROptions from "@/components/purchase/PROptions.vue";
import PrOverview from "@/components/purchase/PROverview.vue";
import PRTableItems from "@/components/purchase/viewTable/PRTableItems.vue";

const purchaseRequestStore = usePurchaseRequestStore();
const route = useRoute();

const purchaseRequestId = route.params.id;
const loading = ref(false);
const cart = ref({});
const tableItems = ref([]);

const get = async () => {
  try {
    loading.value = true;
    const { items, ...result } =
      await purchaseRequestStore.fetchPurchaseRequestById(purchaseRequestId);
    cart.value = result;
    tableItems.value = items;
    cart.value.vendor = items[0].vendor;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await get();
});
</script>
