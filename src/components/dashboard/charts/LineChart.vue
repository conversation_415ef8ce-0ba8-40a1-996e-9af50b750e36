<template>
  <v-container fluid class="d-flex justify-center">
    <Line :data="chartData" :options="chartOptions" />
  </v-container>
</template>

<script setup>
import { onMounted } from "vue";
import { Line } from "vue-chartjs";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const props = defineProps({
  chartData: { type: Object, required: true },
  chartOptions: {
    type: Object,
    default: () => {},
  },
});
</script>

<style></style>
