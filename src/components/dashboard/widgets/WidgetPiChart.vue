<template>
  <!--  Pie Chart  -->
  <v-pie
    :key="`graph-${title}`"
    :items="graphData"
    :legend="{
        position: $vuetify.display.mdAndUp ? 'right' : 'bottom'
      }"
    :tooltip="{ subtitleFormat: '[value]%' }"
    class="pa-3 mt-3 justify-center"
    gap="2"
    inner-cut="10"
    item-key="id"
    rounded="2"
    animation
    hide-slice
    reveal
  >
    <!-- Custom Legend Text -->
    <template #legend-text="{ item }">
      <div class="d-flex ga-6 align-center">
        <div>{{ item.title }}</div>
        <div class="ml-auto font-weight-bold">
          {{ item.value }}
          <!-- ({{ item.raw.percentage }}%) -->
        </div>
      </div>
    </template>
  </v-pie>
</template>

<script setup>
import { computed } from "vue";
import { getBgColors } from "../helpers/colors";

// 🔧 Props Definition
const props = defineProps({
  /**
   * Chart title (used for identification)
   */
  title: {
    type: String,
    default: ""
  },

  chartData: {
    type: Array,
    required: true,
    default: () => []
  },

  /**
   * View mode determines displayed metric:
   *  1 → Amount
   *  2 → Count
   */
  viewMode: {
    type: Number,
    default: 2
  }
});

// 🎨 Computed: Chart Data
const graphData = computed(() => {
  // Generate colors dynamically
  const colors = getBgColors(props.chartData.length);

  // Map data into v-pie compatible format
  return props.chartData.map((gd, id) => ({
    ...gd,
    value: props.viewMode !== 2 ? gd.count : gd.amount,
    percentage: props.viewMode !== 2 ? gd.count : gd.amount,
    color: gd.color || colors[id]
  }));
});
</script>