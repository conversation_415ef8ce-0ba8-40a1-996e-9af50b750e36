<template>
  <v-menu v-model="open" :close-on-content-click="false">
    <template #activator="{ props }">
      <v-btn
        density="compact"
        variant="text"
        icon="mdi-history"
        v-bind="props"
        color="primary"
        :disabled="disabled"
      />
    </template>

    <v-card border rounded="lg" width="400px">
      <!-- Loader -->
      <template #loader v-if="loading">
        <v-progress-linear indeterminate color="primary"></v-progress-linear>
      </template>

      <!-- List -->
      <v-list lines="three" density="compact">
        <!-- Loading Skeleton -->
        <v-skeleton-loader type="list-item-three-line" v-if="loading" />

        <!-- No recent prices -->
        <v-list-item
          v-else-if="!prices.length"
          title="— No recent prices —"
          class="text-medium-emphasis"
        />

        <!-- Prices -->
        <v-list-item
          v-for="(item, index) in prices"
          :key="index"
          :class="{'border-b' : index < prices.length - 1}"
          :title="item.grnNumber"
        >
          <v-list-item-subtitle class="mt-1 text-caption">{{ item.vendorName }}</v-list-item-subtitle>
          <v-list-item-subtitle class="mt-1 text-caption">{{ item.date }}</v-list-item-subtitle>

          <template #append>
            <v-list-item-action class="flex-column align-end">
              <span class="font-weight-medium">
                {{ item.unitCost }}
                <span class="text-caption text-medium-emphasis">/ {{ item.UOM }}</span>
              </span>

              <v-spacer></v-spacer>
              <span class="text-caption">{{ item.quantity }} {{ item.UOM }}</span>
            </v-list-item-action>
          </template>
        </v-list-item>
      </v-list>
    </v-card>
  </v-menu>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "@/stores/snackBar";

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  locationId: {
    type: String,
    required: true
  }
});

const { showSnackbar } = useSnackbarStore();
const open = defineModel();

const prices = ref([]);
const loading = ref(false);

// track last fetch identity
const lastFetchedKey = ref(null);

/**
 * Fetch the last purchase prices for the given item and pkg.
 * Skips if the same item/pkg already fetched.
 * @returns {Promise<void>}
 */
const fetchPrices = async () => {
  if (disabled.value) return;
  
  const payload = {
    locationId: props.locationId,
    itemId: props.item.itemId,
    pkgId: props.item.pkg?.id || null
  };

  const key = `${payload.locationId}_${payload.itemId}_${payload.pkgId}`;

  // skip if same item/pkg already fetched
  if (lastFetchedKey.value === key) return;

  loading.value = true;
  try {
    const { data } = await httpClient.post(
      "purchases/grns/last-purchase-prices?limit=5",
      payload
    );

    prices.value = data || [];
    lastFetchedKey.value = key;
  } catch ({ response }) {
    showSnackbar("error", response.data?.message || "Failed to fetch prices");
  } finally {
    loading.value = false;
  }
};

// menu open handler
watch(
  () => open.value,
  isOpen => {
    if (isOpen) fetchPrices();
  }
);

// watch item/pkg changes
watch(
  () => [props.item?.itemId, props.item?.pkg?.id],
  () => {
    prices.value = [];
    lastFetchedKey.value = null;

    // if menu open while changed, fetch immediately
    if (open.value) fetchPrices();
  }
);

const disabled = computed(() => !props.item?.itemId);
</script>

