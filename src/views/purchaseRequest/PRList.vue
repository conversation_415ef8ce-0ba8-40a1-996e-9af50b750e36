<template>
  <div>
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search PR"
      :hideImportExport="true"
      :hide-filter="true"
      addLabel="PR"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      @export="(type) => console.log(type)"
    >
      <template #prepend-actions>
        <v-btn
          variant="tonal"
          color="primary"
          prepend-icon="mdi-filter-variant"
          class="ml-2"
          @click="toggleFilter"
        >
          <span class="d-none d-md-flex">Filters</span>
        </v-btn>
      </template>
    </list-actions-bar>

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="purchaseRequestHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No purchase request found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear
                :height="2"
                indeterminate
                color="primary"
              ></v-progress-linear>
            </template>
            <template #item.prNumber="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="handleItemClick(item)"
              >
                {{ item.prNumber }}</span
              >
            </template>
            <template #item.totalAmount="{ item }">
              <span>{{ item.totalAmount.toFixed(2) }}</span>
            </template>
            <template #item.status="{ item }">
              <v-chip
                class="text-uppercase"
                label
                :color="getStatusColor(item.status)"
              >
                {{
                  item.status == purchaseStatus.SUBMITTED
                    ? purchaseStatus.APPROVAL_PENDING
                    : item.status
                }}
              </v-chip>
            </template>
            <template #item.action="{ item }">
              <PROptions
                icon
                :status="item.status"
                :itemId="item.id"
                @refresh="refresh"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
    <filter-nav-drawer
      ref="editFilter"
      :tabs="tabs"
      :filtersData="filters"
      @apply-filter="applyFilters"
    ></filter-nav-drawer>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import FilterNavDrawer from "@/components/filters/NavDrawer.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";
import ToggleFilter from "@/components/filters/ToggleFilter.vue";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import { purchaseRequestHeaders } from "@/helpers/tableHeaders";
import PROptions from "@/components/purchase/PROptions.vue";

import { useRouter } from "vue-router";
import { useLocationStore } from "@/stores/location";
import { useStoreStore } from "@/stores/store";
import { usePurchaseRequestStore } from "@/stores/purchaseRequest";
import { getStatusColor } from "@/helpers/status";
import { purchaseStatus } from "@/constants/status";

const router = useRouter();
const purchaseRequestStore = usePurchaseRequestStore();
const locationStore = useStoreStore();
const workAreaStore = useLocationStore();

const locations = computed(() => locationStore.getStores || []);

const search = ref(null);
const loading = ref(false);
const sortBy = ref([{ key: "prNumber", order: "desc" }]);

const items = computed(() => purchaseRequestStore.getPurchaseRequest || []);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase();
  let result = items.value;
  if (query) {
    result = result.filter((item) =>
      item.poNumber?.toLowerCase().includes(query)
    );
  }
  return result;
});

const dataFilters = ref({});
const tabs = [{ value: 1, label: "filters" }];
const filters = computed(() => [
  {
    component: ToggleFilter,
    title: "Status",
    key: "status",
    items: [
      { name: "In progress", value: "pending" },
      { name: "Completed", value: "completed" },
    ],
  },
  {
    component: AutoComplete,
    title: "Location",
    key: "locations",
    items: locations.value,
  },
]);

const applyFilters = (filters) => {
  dataFilters.value = filters;
  refresh();
};

const handleSearch = (v) => {
  search.value = v;
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await purchaseRequestStore.fetchPurchaseRequest(dataFilters.value);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const add = () => {
  router.push({ name: "create-purchase-request" });
};

const handleItemClick = (item) => {
  router.push({ name: "view-purchase-request", params: { id: item.id } });
};

const editFilter = ref(null);
const toggleFilter = () => {
  editFilter.value.toggle();
};

onBeforeMount(async () => {
  try {
    loading.value = true;
    await Promise.all([
      locationStore.fetchStores(),
      workAreaStore.fetchLocations(),
    ]);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
});
</script>
