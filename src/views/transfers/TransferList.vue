<template>
  <div>
    <list-actions-bar
      search-label="Search Transfers"
      add-label="Transfer"
      :hideImportExport="true"
      :hide-filter="true"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
    >
      <template #prepend-actions>
        <v-btn
          variant="tonal"
          color="primary"
          prepend-icon="mdi-filter-variant"
          class="ml-2"
          @click="toggleFilter"
        >
          <span class="d-none d-md-flex">Filters</span>
        </v-btn>
      </template>
    </list-actions-bar>

    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No transfers found"
            show-expand
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear
                :height="2"
                indeterminate
                color="primary"
              ></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.transferNumber="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="view(item.id)"
                >{{ item.transferNumber }}</span
              >
            </template>

            <template #item.dispatchStatus="{ item }">
              <v-chip
                class="text-uppercase"
                label
                :color="getStatusColor(item.dispatchStatus)"
              >
                {{ item.dispatchStatus }}
              </v-chip>
            </template>

            <!-- Custom expand button in dispatch status column -->
            <template
              v-slot:item.data-table-expand="{
                internalItem,
                isExpanded,
                toggleExpand,
              }"
            >
              <v-btn
                :append-icon="
                  isExpanded(internalItem)
                    ? 'mdi-chevron-up'
                    : 'mdi-chevron-down'
                "
                text="Dispatch History"
                color="medium-emphasis"
                size="small"
                variant="tonal"
                @click="toggleExpand(internalItem)"
                :disabled="!internalItem.raw?.timeLine?.length"
              ></v-btn>
            </template>

            <template #item.receiveStatus="{ item }">
              <v-chip
                class="text-uppercase"
                label
                :color="getStatusColor(item.receiveStatus)"
              >
                {{ item.receiveStatus }}
              </v-chip>
            </template>

            <template #item.requestedDate="{ item }">
              {{ ConvertIOStoDate(item.requestedDate) }}
            </template>

            <template #item.requestedTime="{ item }">
              {{ ConvertIOStoTime(item.requestedDate) }}
            </template>

            <template #item.action="{ item }">
              <transfer-options
                icon
                :item="item"
                :disableReceive="!item.timeLine?.length"
                :filters="dataFilters"
              />
            </template>

            <!-- Expanded row template for timeline -->
            <template v-slot:expanded-row="{ item }">
              <tr>
                <td :colspan="filteredHeaders.length" class="px-0">
                  <v-card class="px-4 py-4 bg-surface-light" flat>
                    <v-data-table
                      :headers="timelineHeaders"
                      :items="item.timeLine"
                      class="border"
                      hide-default-footer
                      density="compact"
                    >
                      <template #item.dispatchNo="{ item: timelineItem }">
                        <span
                          class="text-decoration-underline cursor-pointer"
                          @click="viewByDispatchId(item, timelineItem)"
                          >{{ timelineItem.dispatchNo }}</span
                        >
                      </template>

                      <template #item.dispatchedBy="{ item: timelineItem }">
                        <span>{{ timelineItem.dispatchedBy.name }}</span>
                      </template>

                      <template #item.dispatchedDate="{ item: timelineItem }">
                        <span>{{
                          dateFormat(timelineItem.dispatchedBy.time)
                        }}</span>
                      </template>

                      <template #item.dispatchedTime="{ item: timelineItem }">
                        <span>{{
                          timeFormat(timelineItem.dispatchedBy.time)
                        }}</span>
                      </template>

                      <template #item.receivedDate="{ item: timelineItem }">
                        <span>{{
                          timelineItem?.receivedBy?.time ? dateFormat(timelineItem.receivedBy.time) : '-'
                        }}</span>
                      </template>

                      <template #item.receivedTime="{ item: timelineItem }">
                        <span>{{
                          timelineItem?.receivedBy?.time ? timeFormat(timelineItem.receivedBy.time) : '-'
                        }}</span>
                      </template>

                      <template #item.status="{ item: timelineItem }">
                        <span>{{ timelineItem.status.toUpperCase() }}</span>
                      </template>

                      <template #item.actions="{ item: timelineItem }">
                        <div class="text-end">
                          <v-tooltip text="Receive" v-if="timelineItem.status.toLowerCase() !== 'completed'">
                            <template v-slot:activator="{ props }">
                              <v-btn
                                v-bind="props"
                                icon="mdi-package-variant-plus"
                                size="small"
                                variant="text"
                                color="success"
                                @click="receiveDispatch(item, timelineItem)"
                              ></v-btn>
                            </template>
                          </v-tooltip>
                          <v-tooltip text="Print">
                            <template v-slot:activator="{ props }">
                              <v-btn
                                v-bind="props"
                                icon="mdi-file-pdf-box"
                                size="small"
                                variant="text"
                                color="primary"
                                @click="exportPDF(item.id, timelineItem.dispatchNo)"
                              ></v-btn>
                            </template>
                          </v-tooltip>
                        </div>
                      </template>
                    </v-data-table>
                  </v-card>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <filter-nav-drawer
      ref="editFilter"
      :tabs="tabs"
      :filtersData="filters"
      @apply-filter="applyFilters"
    ></filter-nav-drawer>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed, inject } from "vue";
import { useRouter } from "vue-router";
import { transferHeaders, timelineHeaders } from "@/helpers/tableHeaders";
import { getStatusColor } from "@/helpers/status";
import { useLocationStore } from "@/stores/location";
import { useTransferStore } from "@/stores/transfer";
import { filterData } from "@/helpers/searchFilter";
import TransferOptions from "@/views/transfers/TransferOptions.vue";
import {
  ConvertIOStoDate,
  ConvertIOStoTime,
  dateFormat,
  timeFormat,
} from "@/helpers/date";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";

import FilterNavDrawer from "@/components/filters/NavDrawer.vue";
import ToggleFilter from "@/components/filters/ToggleFilter.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";
import httpClient from "@/plugin/Axios"; 

const router = useRouter();
const transferStore = useTransferStore();
const workAreaStore = useLocationStore();

const workAreas = computed(() => workAreaStore.getLocations || []);

const search = ref(null);
const loading = ref(false);
const editFilter = ref(null);

const headers = ref(transferHeaders);

const items = computed(() => transferStore.getTransfers || []);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  return result;
});

const filteredHeaders = computed(() => headers.value.filter((h) => h.enable));

const dataFilters = ref({
  status: ["pending"],
});
const tabs = [{ value: 1, label: "filters" }];
const filters = computed(() => [
  {
    component: ToggleFilter,
    title: "Status",
    key: "status",
    items: [
      { name: "In progress", value: "pending" },
      { name: "Completed", value: "completed" },
    ],
  },
  {
    component: AutoComplete,
    title: "Requester",
    key: "requester",
    items: workAreas.value,
  },
  {
    component: AutoComplete,
    title: "Issuer",
    key: "issuer",
    items: workAreas.value,
  },
]);

const loadData = ref(true);

const applyFilters = (filters) => {
  dataFilters.value = filters;
  if (!loadData.value) {
    refresh();
  }
};

const handleSearch = (v) => {
  search.value = v;
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await transferStore.fetchTransfers(dataFilters.value);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const add = () => {
  router.push({
    name: "create-transfer",
  });
};

const view = (id) => {
  router.push({ name: "view-transfer", params: { id } });
};

const viewByDispatchId = (item, timelineItem) => {
  router.push({
    name: "view-dispatch",
    params: { id: item.id },
    query: { dispatchId: timelineItem.dispatchNo },
  });
};

const toggleFilter = () => {
  editFilter.value.toggle();
};

const $loader = inject("loader");

// Timeline action methods
const exportPDF = async (id,dispatchNo) => {
  $loader.show("Please wait...");
  try {
    await httpClient.get(`transfers/${id}/pdf`, { params: { dispatchNo: dispatchNo }, responseType: "blob" });
  } catch ({ response }) {
    console.error(response.data.message);
  } finally {
    $loader.hide();
  }
};

const receiveDispatch = (item, timelineItem) => {
  router.push({
    name: "receive-transfer",
    params: { id: item.id },
    query: { type: "receive", dispatchId: timelineItem.dispatchNo },
  });
};

onBeforeMount(async () => {
  await workAreaStore.fetchLocations();
  await refresh();
  loadData.value = false;
});
</script>
