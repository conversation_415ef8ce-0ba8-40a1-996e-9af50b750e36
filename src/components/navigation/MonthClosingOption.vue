<template>
  <div class="d-flex align-center">
    <!-- Current Month Button -->
    <v-btn
      v-if="tenants.settings?.monthEndClosing"
      prepend-icon="mdi-calendar-month"
      variant="flat"
      color="primary"
      @click="freezeMonth"
      class="mr-4"
      :style="{ cursor: currentMonthName !==  getCurrentMonth() ? 'pointer' : 'default' }"
    >
      {{ currentMonthName }}
    </v-btn>
  </div>
</template>

<script setup>
import { computed, inject } from "vue";
import { useTenantStore } from "@/stores/tenant";
import { getCurrentMonth } from "@/helpers/date";

const tenantStore = useTenantStore();
const tenants = computed(() => tenantStore.getTenants);

const currentMonthName = computed(() => {
  const data = tenants.value;
  const monthName = data.settings?.currentMonth ? data.settings.currentMonth : getCurrentMonth();
  return monthName;
});

const $confirm = inject("confirm");

const freezeMonth = async () => {
  if (currentMonthName.value === getCurrentMonth()) return;
  try {
    const confirmed = await $confirm(
      "Are you sure you want to close this month?",
      { title: "Month Closing Request" }
    );
    if (!confirmed) return;
    await tenantStore.freezeMonth();
    await tenantStore.fetchTenants();
  } catch (err) {
    console.error(err);
  }
};
</script>

<style scoped></style>
