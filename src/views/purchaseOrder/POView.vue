<template>
  <div>
    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      :back-to="{ name: 'purchase-orders' }"
      hide-submit
      :loading="loading"
    >
      <template #custom>#{{ cart.poNumber }}</template>

      <template #actions>
        <POOptions :status="cart.status" :itemId="purchaseOrderId" />
      </template>
    </form-actions-bar>

    <!-- loader -->
    <page-loader v-if="loading" />

    <!-- Overview/Content -->
    <v-container v-if="!loading" fluid>
      <!-- Overview -->
      <po-overview :data="cart"></po-overview>

      <!-- Item Table -->
      <POTableItems v-if="showPOTable" :items="tableItems" :cart="cart" />
      <PRTableItems
        v-else
        :items="tableItems"
        :cart="cart"
        :showVendor="false"
      ></PRTableItems>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRoute } from "vue-router";

import { purchaseStatus } from "@/constants/status";
import { usePurchaseOrderStore } from "@/stores/purchaseOrder";
import cartHelper from "@/helpers/cartHelper";

// Components
import PageLoader from "@/components/utils/PageLoader.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import POOptions from "@/components/purchase/POOptions.vue";
import PoOverview from "@/components/purchase/PoOverview.vue";
import POTableItems from "@/components/purchase/viewTable/POTableItems.vue";
import PRTableItems from "@/components/purchase/viewTable/PRTableItems.vue";

const purchaseOrderStore = usePurchaseOrderStore();
const route = useRoute();

const purchaseOrderId = route.params.id;
const loading = ref(false);
const tableItems = ref([]);
const cart = ref({});

const modifyItem = (item, index) => {
  cart.value = cartHelper.ModifyItem(cart.value, item, index);
  tableItems.value = cart.value.items;
};

const showPOTable = computed(() => {
  const completedStatus = [purchaseStatus.COMPLETED, purchaseStatus.PARTIAL];
  return completedStatus.includes(cart.value.status);
});

const get = async () => {
  loading.value = true;
  try {
    const result = await purchaseOrderStore.fetchPurchaseOrderById(
      purchaseOrderId
    );
    cart.value = result;
    tableItems.value = result.items;

    if (showPOTable.value) {
      tableItems.value.forEach((item, index) => {
        const newQuantity = item.quantity - (item.receivedQty || 0);
        modifyItem(
          {
            ...item,
            receivedQty: item.receivedQty,
            pendingQty: newQuantity,
            orderedQty: item.quantity,
          },
          index
        );
      });
    }

    loading.value = false;
  } catch (err) {
    console.error(err);
  } finally {
  }
};

onBeforeMount(async () => {
  await get();
});
</script>
