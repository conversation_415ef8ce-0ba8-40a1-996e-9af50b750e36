<template>
  <!-- Container for all widgets -->
  <v-container fluid class="pa-0">
    <v-row>
      <!-- Loop through widgets and render each -->
      <v-col
        v-for="widget in widgets"
        :key="widget.id"
        cols="12"
        sm="6"
        md="6"
        xl="4"
      >
        <!-- Individual Widget Component -->
        <Widget :widget="widget" />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import Widget from "./Widget.vue";

// Props
/**
 * Array of widget objects to display
 * Each widget should have at least an `id` property
 */
defineProps({
  widgets: {
    type: Array,
    required: true,
  },
});
</script>

<style scoped>
/* Optional: spacing or custom styles for the widget grid */
</style>
