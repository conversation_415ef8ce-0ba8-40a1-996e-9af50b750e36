<template>
  <auto-complete
    v-model="model"
    :items="filteredInventoryItems"
    v-bind="$attrs"
    :label="label"
    :multiple="multiple"
    item-title="name"
    item-value="id"
    :filter-keys="['title', 'raw.code', 'raw.hsnCode']"
    :loading="loading"
  >
    <template v-slot:item="{ item, props }">
      <v-list-item class="mb-1" v-bind="props" :subtitle="item.raw.subtitle">
        <template #prepend v-if="multiple">
          <v-checkbox
            :model-value="model.includes(item.value)"
            density="compact"
            hide-details
            color="primary"
            class="mr-2"
          ></v-checkbox>
        </template>
      </v-list-item>
    </template>
  </auto-complete>
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Inventory Items",
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  /** Component-specific attributes */
  categories: {
    // selected locations (ids)
    type: Array,
    default: () => [],
  },
  subCategories: {
    // selected locations (ids)
    type: Array,
    default: () => [],
  },
  vendors: {
    // array of vendor ids
    type: Array,
    default: () => [],
  },
  exclude: {
    type: Array,
    default: () => [],
  },
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const inventoryItems = computed(() => {
  return masterStore.getInventoryItems({
    categoryId: props.categories,
    subCategoryId: props.subCategories,
    vendors: props.vendors,
  });
});

const filteredInventoryItems = computed(() => {
  if (props.exclude.length === 0) return inventoryItems.value;
  return inventoryItems.value.filter((item) => {
    const excludeItem = props.exclude.find((e) => e.itemId === item.id) || null;
    if (!excludeItem) return true;

    // Build list of all possible package IDs for this item
    let itemPkgIds = [...item.packages.map((p) => p.id)];
    if (!item.packages.length) {
      // if no packages, only the default package exists
      itemPkgIds = ["default"];
    }

    // Check if ALL these pkg IDs exist in props.exclude
    const allPkgsUsed = itemPkgIds.every((pkgId) =>
      excludeItem.pkgs.includes(pkgId)
    );

    // If all packages are used → exclude item
    return !allPkgsUsed;
  });
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureInventoryItemData();
  loading.value = false;
});
</script>
