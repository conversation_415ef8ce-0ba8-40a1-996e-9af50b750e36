import { createApp } from "vue";
import { createPinia } from "pinia";

import App from "./App.vue";
import router from "./router";
import Vuetify from "@/plugin/Vuetify";
import SmartTab from '@/directives/v-smart-tab.js';
import SmartFocus from "@/directives/v-smart-focus.js";

// assets
import "@/assets/scss/custom-table.scss";
import "@/assets/scss/draggable-list.scss";

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(Vuetify);
app.directive('smart-tab', SmartTab);
app.directive('smart-focus', SmartFocus);

app.mount("#app");
