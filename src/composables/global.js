// src/composables/globalData.js
import { reactive } from "vue";
import packageJson from "@/../package.json";

const env = import.meta.env;

const globalData = reactive({
  $appVersion: packageJson.version,
  $appName: packageJson.name,
  $serverUrl: env.VITE_APP_URL,
  $authUrl: env.VITE_DIGIAPP_URL,
  $authServerUrl: env.VITE_AUTH_API_URL,
  $clientId: env.VITE_CLIENT_ID,
});

export default globalData;
