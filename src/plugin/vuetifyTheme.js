import vuetify from "./Vuetify";

/**
 * Theme modes
 * - light: Light theme
 * - dark: Dark theme
 * - system: Follow system preference
 */
export const modes = {
  light: "light",
  dark: "dark",
  system: "system",
};

// LocalStorage key
const THEME_KEY = "theme_mode";

// Vuetify theme names
const THEME_LIGHT = "lightTheme";
const THEME_DARK = "darkTheme";

// System theme listener references
let mediaQuery = null;
let systemListener = null;

/**
 * Save the selected mode to localStorage
 * @param {'light'|'dark'|'system'} mode
 */
function setModeHelper(mode) {
  localStorage.setItem(THEME_KEY, mode);
}

/**
 * Get the saved theme mode from localStorage
 * @returns {'light'|'dark'|'system'}
 */
export function getThemeMode() {
  return localStorage.getItem(THEME_KEY) || modes.system;
}

/**
 * Get the **effective theme** considering system preference
 * @returns 'lightTheme'|'darkTheme'
 */
export function getEffectiveTheme() {
  const mode = getThemeMode();

  if (mode === modes.system) {
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
    return prefersDark ? THEME_DARK : THEME_LIGHT;
  }

  return mode === modes.dark ? THEME_DARK : THEME_LIGHT;
}

/**
 * Listen to system theme changes and call a callback with the new effective theme
 * @param {Function} callback - function(theme: 'lightTheme'|'darkTheme')
 */
export function onSystemThemeChange(callback) {
  // Remove previous listener if any
  if (mediaQuery && systemListener) {
    mediaQuery.removeEventListener("change", systemListener);
  }

  mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
  systemListener = () => callback(getEffectiveTheme());

  mediaQuery.addEventListener("change", systemListener);
}

/**
 * Remove system theme listener
 */
export function removeSystemThemeListener() {
  if (mediaQuery && systemListener) {
    mediaQuery.removeEventListener("change", systemListener);
    mediaQuery = null;
    systemListener = null;
  }
}

/**
 * Set Vuetify theme based on mode
 * @param {'light'|'dark'|'system'} mode
 */
export function setTheme(mode) {
  // Save mode in localStorage
  setModeHelper(mode);

  // Apply theme immediately
  vuetify.theme.change(getEffectiveTheme());

  // Remove previous system listener
  removeSystemThemeListener();

  // If system mode, listen for OS theme changes
  if (mode === modes.system) {
    onSystemThemeChange((theme) => {
      vuetify.theme.change(theme)
    });
  }
}

/**
 * Initialize the Vuetify theme using the saved mode.
 * Should be called at app startup.
 */
export function initTheme() {
  setTheme(getThemeMode());
}

/**
 * Get the currently applied Vuetify theme string
 * @returns 'lightTheme'|'darkTheme'
 */
export function getVuetifyTheme() {
  return vuetify.theme.global.name.value;
}
