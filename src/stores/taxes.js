import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useTaxStore = defineStore("tax", () => {
    const tenantId = ref(getTenantId);

    const { showSnackbar } = useSnackbarStore();
    const taxes = ref([]);

    const getTaxes = computed(() => taxes.value || []);
    const setTaxes = (data) => {
        taxes.value = data;
    };

    const fetchTaxes = async () => {
        try {
            const response = await httpClient.get(`taxes`);
            setTaxes(response.data?.data);
        } catch (error) {
            const message =
                error.response?.data?.message || error.message || "Failed to fetch taxes";
            showSnackbar("error", message);
            throw Error(message);
        }
    };

    const fetchTaxById = async (id) => {
      try {
        const response = await httpClient.get(`taxes/${id}`);
        return response.data?.data;
      } catch ({ response }) {
        showSnackbar("error", response.data.message);
        throw Error(response.data.message);
      }
    };

    const createTax = async (data) => {
        data.tenantId = tenantId.value;
        try {
          await httpClient.post("taxes", data);
          showSnackbar("green", "Tax Created Successfully");
        } catch ({ response }) {
          showSnackbar("error", response.data.message);
          throw Error(response.data.message);
        }
      };
    
      const updateTax = async (id, data) => {
        data.tenantId = tenantId.value;
      
        try {
          await httpClient.put(`taxes/${id}`, data);
          showSnackbar("green", "Tax Updated Successfully");
        } catch ({ response }) {
          showSnackbar("error", response.data.message);
          throw Error(response.data.message);
        }
      };
      



    return {
        getTaxes,
        setTaxes,
        fetchTaxes,
        fetchTaxById,
        createTax,
        updateTax
    };
});
