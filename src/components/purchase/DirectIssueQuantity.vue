<template>
  <v-text-field
    :value="totalQuantity"
    variant="outlined"
    density="compact"
    color="primary"
    readonly
    hide-details
  >
    <template v-slot:append-inner>
      <v-icon size="extra-small">mdi-pencil</v-icon>
    </template>
    <v-menu
      v-model="showMenu"
      :close-on-content-click="false"
      activator="parent"
    >
      <v-card>
        <v-card-text class="py-4">
          <v-row v-for="(wa, ind) in quantityList" :key="wa.id">
            <v-col cols="6">
              <div>
                <div class="d-flex align-center">
                  <span>{{ wa.name }}</span>
                  <v-chip
                    v-if="wa.isDefault"
                    label
                    size="small"
                    density="compact"
                    color="primary"
                    class="ml-2"
                  >
                    DEFAULT
                  </v-chip>
                </div>

                <span class="text-caption text-medium-emphasis">{{
                  wa.locationName
                }}</span>
              </div>
            </v-col>
            <v-col cols="6">
              <v-text-field
                v-model.number="wa.quantity"
                type="number"
                variant="outlined"
                density="compact"
                color="primary"
                hide-details="auto"
                :rules="[rules.positive]"
                @keydown.up.prevent
                @keydown.down.prevent
                @blur="wa.quantity = wa.quantity || 0"
                @update:model-value="handleQuantity"
              >
              </v-text-field
            ></v-col>
            <v-divider v-if="ind != quantityList.length - 1"></v-divider>
          </v-row>
        </v-card-text>
        <!-- <v-divider />
        <v-card-actions class="px-4">
          <v-row no-gutters>
            <v-col cols="6">
              <p class="text-error text-caption" v-if="!quantityMatch">
                Expected quantity is {{ expectedQty }} {{ quantityMatch }}
              </p>
            </v-col>
            <v-col cols="6" class="text-end">
              <v-btn color="primary" variant="flat" @click="submit">
                Submit
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions> -->
      </v-card>
    </v-menu>
  </v-text-field>
</template>
<script setup>
import { ref, watch, computed } from "vue";
import rules from "@/helpers/rules";

const showMenu = ref(false);
const props = defineProps({
  selectedWorkArea: {
    type: Array,
    default: () => [],
  },
  item: {
    type: Object,
    default: () => {},
  },
});

const totalQuantity = defineModel();
const quantityList = ref([]);

const emit = defineEmits(["submit"]);

const handleQuantity = () => {
  const result = quantityList.value.reduce(
    (acc, item) => acc + item.quantity,
    0
  );
  totalQuantity.value = result;
  emit("submit", quantityList.value);
};

const selectedWorkAreaRef = computed(() => props.selectedWorkArea);

watch(
  selectedWorkAreaRef,
  (v) => {
    quantityList.value = v.map((wa, ind) => ({
      ...wa,
      quantity: ind == 0 ? totalQuantity.value : 0,
      item: props.item,
    }));
  },
  { immediate: true, deep: true }
);
</script>
