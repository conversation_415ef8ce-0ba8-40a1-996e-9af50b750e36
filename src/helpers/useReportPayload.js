import { REPORT_INFORMATION } from "./reportDefs";

// useReportPayload.js
export function NewReportPayload(id = null, filters = {}, columns = [], options = {}) {
  const payload = {
    id,
    filters: Object.assign({
      fromDate: new Date(),
      toDate: new Date(),
      locations: [],
      inventoryLocations: [],
      categories: [],
      subCategories: [],
      vendors: [],
      inventoryItems: [],
    }, filters),
    columns,
    options
  };

  if (id) {
    const reportInfo = REPORT_INFORMATION[id];
    if (reportInfo) {
      payload.columns = reportInfo.headers
        .sort((h1, h2) => h1.ordinal - h2.ordinal);
      payload.options = reportInfo.options;
    }
  }

  return payload
}

export function GetReportOptionGroups(id){
  const reportInfo = REPORT_INFORMATION[id];
  if (reportInfo) {
    return reportInfo.optionGroups;
  }
  return [];
}