<template>
  <v-dialog v-model="dialog" max-width="500px" persistent>
    <v-card>
      <v-card-title>{{ dialogType }} Item</v-card-title>

      <v-card-text>
        <v-autocomplete
          v-model="selectedItem"
          label="Inventory Item"
          color="primary"
          hide-details="auto"
          variant="outlined"
          density="compact"
          :items="inventoryItems"
          item-title="itemName"
          item-value="id"
          return-object
          clearable
        ></v-autocomplete>
        <v-text-field
          v-model.number="entryQuantity"
          label="Enter Quantity"
          type="number"
          min="1"
          required
          density="compact"
          variant="outlined"
          hide-details
          color="primary"
          class="mt-4"
          @keyup.enter="submit"
        >
          <template v-slot:append-inner>
            {{
              dialogType == "Consume"
                ? selectedItem?.recipeUnit.symbol
                : selectedItem?.purchaseUnit.symbol
            }}
          </template>
        </v-text-field>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-spacer />
        <v-btn @click="close" variant="outlined" color="primary"
          ><v-icon>mdi-close</v-icon>Close</v-btn
        >
        <v-btn text="Save" @click="submit" variant="flat" color="primary" />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { ref, watch, onBeforeMount, computed } from "vue";
const dialog = defineModel();
const props = defineProps({
  modelValue: Boolean,
  item: Object,
  dialogType: String,
});
const emit = defineEmits(["update:modelValue", "submit"]);

const entryQuantity = ref(null);
const selectedItem = ref(null);

const inventoryItemStore = useInventoryItemStore();
const inventoryItems = computed(() => inventoryItemStore.getInventoryItems);

const close = () => {
  emit("update:modelValue", false);
  entryQuantity.value = null;
  selectedItem.value = null;
};

const submit = () => {
  if (!entryQuantity.value || entryQuantity.value <= 0) {
    alert("Please enter a valid quantity.");
    return;
  }
  emit("submit", {
    type: props.dialogType,
    quantity: entryQuantity.value,
    itemName: selectedItem.value.itemName,
    itemId: selectedItem.value.id,
    itemCode: selectedItem.value.itemCode,
    purchaseUnit: selectedItem.value.purchaseUnit,
    countingUnit: selectedItem.value.countingUnit,
    recipeUnit: selectedItem.value.recipeUnit,
  });
  close();
};

watch(
  () => props.modelValue,
  (val) => {
    if (!val) entryQuantity.value = null;
  }
);
onBeforeMount(async () => {
  const inventoryItemPromise = inventoryItemStore.fetchInventoryItems();

  await Promise.all([inventoryItemPromise]);
});
</script>
