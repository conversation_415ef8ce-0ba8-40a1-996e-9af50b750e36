<template>
  <v-container class="py-6" max-width="800">
    <!-- ========== Purchase Settings Card ========== -->
    <v-card border rounded="lg" title="Purchase Settings" prepend-icon="mdi-cog-outline">
      <v-divider></v-divider>

      <!-- Options List -->
      <v-list>
        <!-- PR Approval -->
        <v-list-item
          title="PR Approval"
          subtitle="Enable or disable approval for purchase requests"
        >
          <template #append>
            <v-switch
              :model-value="settings.prApproval"
              :loading="loadingField === 'prApproval'"
              @click.prevent="submit('prApproval')"
              inset
              color="success"
              base-color="error"
              hide-details
            />
          </template>
        </v-list-item>

        <v-divider></v-divider>

        <!-- PO Approval -->
        <v-list-item title="PO Approval" subtitle="Enable or disable approval for purchase orders">
          <template #append>
            <v-switch
              :model-value="settings.poApproval"
              :loading="loadingField === 'poApproval'"
              @click.prevent="submit('poApproval')"
              inset
              color="success"
              base-color="error"
              hide-details
            />
          </template>
        </v-list-item>
      </v-list>
    </v-card>

    <!-- ========== Transfer Settings Card ========== -->
    <v-card border rounded="lg" class="my-6" title="Transfer Settings" prepend-icon="mdi-transfer">
      <v-divider></v-divider>

      <!-- Options List -->
      <v-list>
        <v-list-item
          title="Auto Receive Internal Transfers"
          subtitle="Enable or disable automatic receiving of internal transfers within the location"
        >
          <template #append>
            <v-switch
              :model-value="settings.autoReceive"
              :loading="loadingField === 'autoReceive'"
              @click.prevent="submit('autoReceive')"
              inset
              color="success"
              base-color="error"
              hide-details
            />
          </template>
        </v-list-item>
      </v-list>
    </v-card>

    <!-- ========== Closing Settings Card ========== -->
    <v-card
      border
      rounded="lg"
      class="my-6"
      title="Month Closing"
      prepend-icon="mdi-calendar-month"
    >
      <v-divider></v-divider>

      <v-list>
        <v-list-item title="Month-End Closing" subtitle="Enable or disable month-end closing">
          <template #append>
            <v-switch
              :model-value="settings.monthEndClosing"
              :loading="loadingField === 'monthEndClosing'"
              @click.prevent="submit('monthEndClosing')"
              inset
              color="success"
              base-color="error"
              hide-details
            />
          </template>
        </v-list-item>

        <v-divider></v-divider>

        <v-list-item title="Current Month">
          <template #append>
            <v-chip
              label
              prepend-icon="mdi-calendar-month"
              variant="flat"
              color="primary"
            >{{ currentMonthName }}</v-chip>
          </template>
        </v-list-item>
      </v-list>
    </v-card>
  </v-container>
</template>

<script setup>
import { ref, onBeforeMount, computed, watch } from "vue";
import { useTenantStore } from "@/stores/tenant";
import { getCurrentMonth } from "@/helpers/date";

const tenantStore = useTenantStore();
const tenants = computed(() => tenantStore.getTenants);

const settings = ref({
  prApproval: false,
  poApproval: false,
  autoReceive: false,
  monthEndClosing: false
});

const loadingField = ref(null);

const submit = async (key) => {
  const currentValue = settings.value[key];
  const newValue = !currentValue;

  loadingField.value = key;
  try {
    await tenantStore.updatedTenantSetting({ settings: { [key]: newValue } });
    settings.value[key] = newValue; 
    await refresh();
  } catch (err) {
    console.error("Failed to update setting:", err);
  } finally {
    loadingField.value = null;
  }
};

const currentMonthName = computed(() => {
  const data = tenants.value;
  const monthName = data.settings?.currentMonth ? data.settings.currentMonth : getCurrentMonth();
  return monthName;
});

const refresh = async () => {
  try {
    await tenantStore.fetchTenants();
    if (tenants.value.settings) {
      settings.value = { ...tenants.value.settings };
    }
  } catch (err) {
    console.error(err);
  }
};

onBeforeMount(refresh);
</script>
