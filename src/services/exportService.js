import httpClient from "@/plugin/Axios";
import { getTenantId } from "@/helpers/auth";
import { ref } from "vue"

const tenantId = ref(getTenantId);

const url = `/export-product-configuration`;
const logsUrl = `/import-export-logs`;

export const handleExport = async (isTemplate = false, sheets = []) => {
  try {
    const sheetsParam = Array.isArray(sheets) ? sheets.join(",") : sheets;
    await httpClient.get(
      `${url}?sheets=${sheetsParam}&isTemplate=${isTemplate}`,
      { responseType: "blob" }
    );
  } catch (error) {
    console.error("Export failed:", error);
  }
};

export const fetchImportExportLogs = async () => {
  try {
    const response = await httpClient.get(logsUrl);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch import/export logs:", error);
    return [];
  }
};
