<template>
  <div>
    <v-fab
      v-if="!openNav"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel v-model="filter" :no-vendor="true" />

        <v-card-text>
          <v-form ref="form">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <inventory-item-field
                    ref="itemField"
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                  />
                </v-col>

                <v-col cols="12" v-if="packageList.length">
                  <v-autocomplete
                    v-model="formData.pkg"
                    :items="filteredPackages"
                    item-title="name"
                    item-value="id"
                    label="Package"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                    @update:model-value="handleInventoryPkg"
                    :disabled="packageList.length === 1"
                  />
                </v-col>

                <v-col cols="12">
                  <v-autocomplete
                    v-model="formData.adjustmentType"
                    :items="types"
                    item-title="name"
                    item-value="id"
                    label="Adjustment Type"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                  />
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.adjustmentQuantity"
                    label="Adjustment Qty"
                    type="number"
                    min="1"
                    :rules="[rules.min1]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData, 'adjustmentQuantity')"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <!-- @todo: item summary -->
          <template #actions>
            <v-btn @click="handleSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { nextTick, ref, computed, onMounted, watch } from "vue";
import rules from "@/helpers/rules";
import { transferRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useInventoryItemStore } from "@/stores/inventoryItem";

// components
import InventoryFiltersPanel from "@/components/purchase/InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  locationId: {
    type: String,
    default: null,
  },
  existingItems: { type: Array, default: () => [] },
});

const inventoryStore = useInventoryItemStore();

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const packageList = ref([]);
const selectedItem = ref([]);
const itemField = ref(null);

const types = ref([
  { id: "addition", name: "Stock Addition" },
  { id: "reduction", name: "Stock Reduction" },
]);

const getItemDetails = async (id) => {
  const item = await inventoryStore.fetchItemDetails({
    id: id,
    locationId: props.locationId,
    pkgId: "default",
  });

  formData.value.itemName = item.itemName;
  formData.value.itemCode = item.itemCode;
  formData.value.itemId = item.id;
  formData.value.categoryId = item.category?.id;
  formData.value.categoryName = item.category?.name;
  formData.value.subCategoryId = item.subCategory?.id;
  formData.value.subCategoryName = item.subCategory?.name;
  formData.value.unitCost = item.unitCost;
  formData.value.countingUOM = item.countingUnit.symbol;
  formData.value.adjustmentQuantity = item.adjustmentQuantity;

  packageList.value = [
    { name: `${item.purchaseUnit.symbol} (default)`, id: "default" },
    ...item.packages,
  ];
};

const filteredPackages = computed(() => {
  if (!selectedItem.value || !selectedItem.value.id) {
    return [];
  }
  const selectedPkgs = props.existingItems
    .filter((i) => i.itemId === selectedItem.value.id)
    .map((i) => i.pkg?.id);
  return packageList.value.filter((pkg) => !selectedPkgs.includes(pkg.id));
});

const onSelectInventoryItem = async (selected) => {
  selectedItem.value = selected;
  if (!selected) return;
  getItemDetails(selected.id);
};

const handleInventoryPkg = async (v) => {
  if (!v) return;
  const item = await inventoryStore.fetchItemDetails(
    {
      id: formData.value.itemId,
      locationId: props.locationId,
      pkgId: v.id,
    },
    false
  );

  formData.value.purchaseUOM =
    v.id === "default" ? item.purchaseUnit.symbol : v.packageCode;
  formData.value.unitCost = item.unitCost;
  formData.value.inclTax = item.inclTax || false;
};

const itemNameField = ref(null);
const handleSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...formData.value };
  emit("add", payload);
  packageList.value = [];

  // ✅ Reset form data
  formData.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
  selectedItem.value = null;
  form.value.resetValidation();
  // ✅ Focus back to the first field
  await nextTick();
  itemNameField.value?.focus();
  focusFirstField();
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Special rule: quantity must always be at least 1
  if (field === "adjustmentQuantity") {
    if (item.adjustmentQuantity < 1 || !item.adjustmentQuantity) {
      item.adjustmentQuantity = 1;
    }
    return;
  }

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }
};

let hasFocusedInitially = false;

const focusFirstField = () => {
  if (itemField.value?.$el) {
    const input = itemField.value.$el.querySelector('input, .v-input input');
    if (input) input.focus();
  }
};

onMounted(async () => {
  await nextTick();
  focusFirstField();
  hasFocusedInitially = true;

  window.addEventListener('keydown', (e) => {
    if (e.key === 'Tab' && document.activeElement === document.body) {
      e.preventDefault();
      focusFirstField();
    }
  });
});

watch(openNav, async (val) => {
  if (val) {
    await nextTick()
    focusFirstField()   // same method you already have
  }
})

watch(filteredPackages, (pkgs) => {
  formData.value.pkg = pkgs[0];
});

</script>
