import { generalStatus, contractStatus } from "@/constants/status";

const getStatusColor = (status) => {
  switch (status) {
    case generalStatus.DRAFT:
    case generalStatus.PARTIAL:
    case generalStatus.PENDING:
    case generalStatus.ACTIVE:
    case generalStatus.APPROVAL_PENDING:
      return "warning";
    case generalStatus.SUBMITTED:
    case generalStatus.APPROVED:
    case generalStatus.CREATED:
    case generalStatus.COMPLETED:
    case generalStatus.EXPIRED:
      return "success";
    default:
      return "error";
  }
};

const getContractStatusColor = (status) => {
  switch (status) {
    case contractStatus.IDLE:
      return "warning";
    case contractStatus.ACTIVE:
      return "success";
    default:
      return "error";
  }
};

export { getStatusColor, getContractStatusColor };
