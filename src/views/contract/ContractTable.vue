<template>
  <v-container fluid class="px-0 py-2">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="contractList"
          :headers="contractItemHeaders"
          items-per-page="-1"
          fixed-header
          hide-default-footer
        >
          <template #item.index="{ index }">{{ index + 1 }}</template>
          <template #item.actions="{ index }">
            <v-icon color="error" @click="$emit('removeItem', index)"
             @keydown.enter="$emit('removeItem', index)"
              >mdi-close</v-icon
            >
          </template>

          <template #item.itemName="{ item }">
            <formatted-item-name :item="item"></formatted-item-name>
          </template>

          <template #item.pkgName="{ item }">
            {{ item?.pkg.id === 'default' ? item.countingUOM : item?.pkg.name }}
          </template>

          <template #no-data>
            <v-btn
              :disabled="!formValid"
              prepend-icon="mdi-plus"
              text="Add Item"
              variant="text"
              border
              rounded="xl"
              @click="$emit('addItem')"
            ></v-btn>
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import { contractItemHeaders } from "@/helpers/tableHeaders";

const props = defineProps({
  contractList: {
    type: Array,
    default: () => [],
  },
  formValid: {
    type: Boolean,
    default: false,
  }
});

const emit = defineEmits(["removeItem", "addItem"]);
</script>
