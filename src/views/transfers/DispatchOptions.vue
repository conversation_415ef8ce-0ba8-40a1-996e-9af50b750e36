<template>
  <v-menu>
    <template #activator="{ props }">
      <v-btn
        v-if="icon"
        v-bind="props"
        variant="text"
        icon="mdi-dots-vertical-circle-outline"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
      <v-btn
        v-else
        v-bind="props"
        variant="outlined"
        prepend-icon="mdi-dots-vertical"
        text="Options"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
    </template>

    <v-list class="pa-0">
      <div v-for="(action, index) in availableOptions" :key="action.id">
        <v-list-item
          :prepend-icon="action.icon"
          :title="action.label"
          :disabled="action.disabled"
          @click="handleAction(action.id)"
        />
        <v-divider v-if="index < availableOptions.length - 1" />
      </div>
    </v-list>
  </v-menu>
</template>

<script setup>
import { computed, inject } from "vue";
import { useRouter } from "vue-router";
import httpClient from "@/plugin/Axios"; 

const router = useRouter();

const props = defineProps({
  icon: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    required: true,
  },
  dispatchNo: {
    type: String,
    required: true,
  },
  showExportOnly: {
    type: Boolean,
    default: false,
  },
});

const options = [
  {
    id: "receive",
    label: "Receive",
    icon: "mdi-package-variant-plus",
  },
  {
    id: "printPDF",
    label: "Export as PDF",
    icon: "mdi-file-pdf-box",
  },
  // {
  //   id: "printXLS",
  //   label: "Export as XLS",
  //   icon: "mdi-file-excel-box",
  // },
];

const availableOptions = computed(() => {
  if (props.showExportOnly) {    
    return options.filter(opt => opt.id === "printPDF");
  }
  return options;
});

const disabled = computed(() => availableOptions.value.length === 0);

const handleAction = async (actionId) => {
  const action = availableOptions.value.find((a) => a.id === actionId);
  if (!action || action.disabled) return;

  switch (actionId) {
    case "printPDF":
      await exportPDF();
      break;

    case "printXLS":
      await exportPDF();
      break;

    case "receive": {
      router.push({
        name: "receive-transfer",
        params: { id: props.item.id },
        query: { type: "receive", dispatchId: props.dispatchNo },
      });
      break;
    }

    default:
      break;
  }
};

const $loader = inject("loader");

const exportPDF = async () => {
  $loader.show("Please wait...");
  try {
    const dispatchNumber = props.dispatchNo;
    await httpClient.get(`transfers/${props.item.id}/pdf`, {
      params: { dispatchNo: dispatchNumber },
      responseType: "blob",
    });
  } catch ({ response }) {
    console.error(response.data.message);
  } finally {
    $loader.hide();
  }
};

</script>
