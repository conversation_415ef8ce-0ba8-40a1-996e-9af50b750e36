<template>
  <div style="height: 100%" v-smart-focus>
    <!-- Form Actions Bar -->
    <form-actions-bar
      @close="navigatePrevious"
      @submit="save"
      :loading="isLoading"
    />

    <v-container v-if="!loader" fluid>
      <v-card-text class="pa-0">
        <v-form ref="form">
          <v-row>
            <v-col cols="12" sm="4">
              <v-text-field
                v-model="record.itemName"
                label="Item Name*"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                @blur="cleanName"
                :rules="[rules.require, rules.maxLength(100)]"
                auto-focus
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="4">
              <v-text-field
                v-model="record.itemCode"
                label="Item Code"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :rules="[rules.minLength(4), rules.maxLength(20)]"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="4">
              <v-autocomplete
                v-model="record.itemType"
                label="Item Type*"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :items="['bought', 'made']"
                :readonly="isEdit"
                @update:model-value="handleItemTypeChange"
                :rules="[rules.require]"
                clearable
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="4">
              <CategoryField v-model="record.category" return-object />
            </v-col>
            <v-col cols="12" sm="4">
              <SubCategoryField
                v-model="record.subCategory"
                :categories="[record.category?.id]"
                return-object
                @update:model-value="handleSubCategoryChange"
              />
            </v-col>
            <v-col cols="12" sm="4">
              <TagField v-model="record.tags" return-object :multiple="true" />
            </v-col>
            <v-col cols="12" sm="4">
              <HouseUnitField
                v-model="record.purchaseUnit"
                label="Purchase Unit"
                mandatory
                return-object
                @update:model-value="handlePurchaseUnitChange"
              ></HouseUnitField>
            </v-col>
            <v-col cols="12" sm="4">
              <HouseUnitField
                v-model="record.countingUnit"
                label="Counting Unit"
                mandatory
                return-object
                :show-conversion="true"
                :purchase-unit="record.purchaseUnit"
                :parent-unit="record.purchaseUnit"
                @update:model-value="handleCountingUnitChange"
              ></HouseUnitField>
            </v-col>
            <v-col cols="12" sm="4">
              <HouseUnitField
                v-model="record.recipeUnit"
                label="Recipe Unit"
                mandatory
                return-object
                :show-conversion="true"
                :purchase-unit="record.purchaseUnit"
                :parent-unit="record.countingUnit"
              ></HouseUnitField>
            </v-col>

            <v-col cols="12" sm="4" v-if="record.itemType == 'made'">
              <v-text-field
                v-model.number="record.prepareQuantity"
                label="Prepare Quantity*"
                type="number"
                variant="outlined"
                density="compact"
                hide-details="auto"
                color="primary"
                min="1"
                :rules="[rules.require, rules.quantity]"
              >
                <template v-slot:append-inner>
                  {{ recipeUnit?.symbol }}
                </template></v-text-field
              >
            </v-col>

            <v-col cols="12" sm="4">
              <v-text-field
                v-model.number="record.unitCost"
                label="Unit cost*"
                type="number"
                variant="outlined"
                density="compact"
                hide-details="auto"
                color="primary"
                @keydown.up.prevent
                @keydown.down.prevent
                @blur="
                  record.unitCost = record.unitCost < 0 ? 0 : record.unitCost
                "
                :readonly="record.itemType == 'made'"
              />
            </v-col>

            <v-col cols="12" sm="4">
              <v-text-field
                v-model.number="record.parLevel"
                label="Par Level"
                type="number"
                variant="outlined"
                density="compact"
                hide-details="auto"
                color="primary"
                @keydown.up.prevent
                @keydown.down.prevent
                @blur="
                  record.parLevel = record.parLevel < 0 ? 0 : record.parLevel
                "
                :rules="[rules.positive]"
              >
                <template v-slot:append-inner>
                  {{ record.countingUnit?.symbol }}
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="4">
              <TaxField v-model="record.taxes" return-object multiple />
            </v-col>
            <v-col cols="12" sm="4">
              <VendorField v-model="record.vendors" return-object multiple />
            </v-col>
            <v-col cols="12" sm="4">
              <v-text-field
                v-model="record.ledger"
                label="Ledger"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="4">
              <v-text-field
                v-model="record.hsnCode"
                label="HSN Code"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="6" sm="4" class="py-0">
              <v-switch
                v-model="record.trackExpiry"
                label="Track Expiry"
                color="green"
                hide-details="auto"
                inset=""
              />
            </v-col>
            <v-col cols="6" sm="4" class="py-0">
              <v-switch
                v-model="record.stockable"
                label="Stockable"
                color="green"
                hide-details="auto"
                inset=""
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" md="4">
              <v-checkbox
                v-model="record.showPackage"
                color="primary"
                label="Create custom packages or brands."
                hide-details
                :disabled="!record.purchaseUnit"
                @update:model-value="handleShowPackage"
              >
              </v-checkbox>
            </v-col>
            <v-col cols="12" md="4" v-if="record.showPackage">
              <v-checkbox
                v-model="record.defaultPackage"
                color="primary"
                label="Add purchase unit as default package."
                hide-details
              >
              </v-checkbox>
            </v-col>
            <v-col cols="12" md="4" v-if="record.showPackage">
              <v-checkbox
                v-model="record.showWeight"
                color="primary"
                label="show weights"
                hide-details
              >
              </v-checkbox>
            </v-col>
          </v-row>

          <v-col cols="12" v-if="record.showPackage">
            <h3 class="mb-4">
              <v-icon icon="mdi-package-variant"></v-icon>
              <span class="mx-2">Packages</span>
            </h3>
            <package-table
              v-model="packages"
              :unit="record.purchaseUnit"
              :list="packageList"
              :show-weight="record.showWeight"
            ></package-table>
          </v-col>
          <v-col cols="12" v-if="record.itemType == 'made'">
            <h3 class="mb-4">
              <v-icon icon="mdi-food"></v-icon>
              <span class="mx-2">Ingredients</span>
            </h3>
            <ingredient-table
              v-model="ingredients"
              :inventoryList="filteredInventoryItems"
            ></ingredient-table>
          </v-col>
        </v-form>
      </v-card-text>
    </v-container>
    <v-container
      v-else
      class="d-flex justify-center align-center"
      style="height: 100%"
    >
      <div class="text-center">
        <v-progress-circular
          color="primary"
          indeterminate
        ></v-progress-circular>
      </div>
    </v-container>
  </div>
</template>
<script setup>
import { ref, onBeforeMount, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { inventoryRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useVendorStore } from "@/stores/vendor";
import { useCategoryStore } from "@/stores/category";
import { useHouseUnitStore } from "@/stores/houseUnit";
import PackageTable from "@/views/inventoryItem/PackageTable.vue";
import IngredientTable from "@/views/inventoryItem/IngredientTable.vue";
import { formatName } from "@/helpers/formatter";
import { calculateRecipeCost } from "@/helpers/cost";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import CategoryField from "@/components/fields/CategoryField.vue";
import SubCategoryField from "@/components/fields/SubCategoryField.vue";
import TagField from "@/components/fields/TagField.vue";
import TaxField from "@/components/fields/TaxField.vue";
import VendorField from "@/components/fields/VendorField.vue";
import HouseUnitField from "@/components/fields/HouseUnitField.vue";

const route = useRoute();
const router = useRouter();
const inventoryItemStore = useInventoryItemStore();
const vendorStore = useVendorStore();
const categoryStore = useCategoryStore();
const houseUnitStore = useHouseUnitStore();

const inventoryItemId = route.params.id;
const isEdit = inventoryItemId !== undefined;

const form = ref(null);
const loader = ref(false);
const isLoading = ref(false);
const subCategories = ref([]);
const inventoryItems = ref([]);

const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const packages = ref([]);
const ingredients = ref([]);

const categories = computed(() => categoryStore.getCategories || []);
const vendors = computed(() => vendorStore.getVendors || []);
const purchaseUnits = computed(() => houseUnitStore.getHouseUnits);
const packageList = ref([]);

//const inventoryItems = computed(() => inventoryItemStore.getInventoryItems);

const filteredInventoryItems = computed(() => {
  const selectedIds = ingredients.value.map((i) => i.id);
  return inventoryItems.value.filter(
    (item) => !selectedIds.includes(item.id) && item.id !== record.value.id
  );
});

const handlePurchaseUnitChange = async (val) => {
  record.value.countingUnit = null;
  record.value.recipeUnit = null;

  if (record.value.showPackage && val) {
    handleShowPackage(true);
  } else {
    packageList.value = [];
  }
};

const handleCountingUnitChange = async () => {
  record.value.recipeUnit = null;
};

// Build a unit map for quick lookup by name
const unitMap = computed(() => {
  const map = new Map();
  purchaseUnits.value.forEach((u) => map.set(u.symbol, u));
  return map;
});

//  Function to calculate conversion from A to B (BFS)
function getConvertedQuantity(fromSymbol, toSymbol) {
  const visited = new Set();
  const queue = [{ symbol: fromSymbol, quantity: 1 }];

  while (queue.length > 0) {
    const { symbol, quantity } = queue.shift();
    if (symbol === toSymbol) return quantity;

    const unit = unitMap.value.get(symbol);
    if (!unit || !unit.conversions) continue;

    for (const conv of unit.conversions) {
      if (visited.has(conv.toUnit)) continue;
      visited.add(conv.toUnit);
      queue.push({ symbol: conv.toUnit, quantity: quantity * conv.quantity });
    }
  }

  return null; // Not found
}

//  Full conversion dropdown with success/failure paths
const countingUnitOptions = computed(() => {
  const fromUnit = record.value.purchaseUnit;
  if (!fromUnit) return [];

  const toUnit = purchaseUnits.value.find((u) => u.symbol === fromUnit.toUnit);

  return purchaseUnits.value.map((toUnit) => {
    const qty = getConvertedQuantity(fromUnit.symbol, toUnit.symbol);
    return {
      name: `${toUnit.name} (${toUnit.symbol})`,
      value: toUnit.name,
      conversion: qty ?? null,
      symbol: toUnit.symbol,
    };
  });
});
const onCategoryChange = (val, subCategoryExist) => {
  record.value.category = { id: val.id, name: val.name };
  if (!subCategoryExist) record.value.subCategory = null;
  subCategories.value =
    categories.value.find((item) => item.id == val.id)?.subCategories || [];
};

const handleItemTypeChange = (val) => {
  if (val == "bought") ingredients.value = [];
};

const selectAllStores = computed(
  () => record.value.vendors.length === vendors.value.length
);
const selectSomeStore = computed(
  () => record.value.vendors.length > 0 && !selectAllStores.value
);
const icon = computed(() => {
  if (selectAllStores.value) return "mdi-close";
  if (selectSomeStore.value) return "mdi-minus-box-outline";
  return "mdi-checkbox-blank-outline";
});

onBeforeMount(async () => {
  loader.value = true;
  try {
    const houseUnitPromise = houseUnitStore.fetchAllHouseUnits();
    await Promise.all([houseUnitPromise]);

    if (isEdit) {
      const result = await inventoryItemStore.fetchInventoryItemById(
        inventoryItemId
      );
      record.value = result;
      onCategoryChange(record.value.category, true);
    }
    packages.value = record.value.packages;
    if (record.value.showPackage) {
      packageList.value = await houseUnitStore.getRelatedHouseUnits(
        record.value.purchaseUnit
      );
    }
    ingredients.value =
      record.value.ingredients?.map((v) => ({
        ...v,
        name: v.itemName,
        id: v.itemId,
        code: v.itemCode,
      })) || [];
    loader.value = false;
  } catch (error) {
    console.log(error);
  }
});

const handleShowPackage = async (val) => {
  if (!val) return;
  packages.value = [];
  packageList.value = await houseUnitStore.getRelatedHouseUnits(
    record.value.purchaseUnit
  );
};

const save = async () => {
  if (isLoading.value) return;
  isLoading.value = true;

  try {
    const { valid } = await form.value.validate();
    if (!valid) {
      isLoading.value = false;
      return;
    }

    const payload = {
      ...record.value,
      purchaseUnit: {
        id: record.value.purchaseUnit.id,
        name: record.value.purchaseUnit.name,
        symbol: record.value.purchaseUnit.symbol,
        quantity: record.value.purchaseUnit.quantity,
        toUnit: record.value.purchaseUnit.toUnit,
      },
      subCategory: {
        id: record.value.subCategory.id,
        name: record.value.subCategory.name,
      },
      tags: record.value.tags,
      taxes: (record.value.taxes ?? []).map((t) => ({
        id: t.id,
        name: t.name,
      })),
      showPackage: record.value.showPackage,
      packages: packages.value || [],
      ingredients: ingredients.value?.map((v) => ({
        itemId: v.id,
        itemName: v.name,
        itemCode: v.code,
        quantity: v.quantity,
        purchaseUnit: v.purchaseUnit,
        countingUnit: v.countingUnit,
        recipeUnit: v.recipeUnit,
        unitCost: v.unitCost,
        hsnCode: v.hsnCode,
        // symbol: v.symbol,
        // recipeConversion: v.recipeConversion,
      })),
      countingUnit: {
        id: record.value.countingUnit.id,
        name: record.value.countingUnit.name,
        symbol: record.value.countingUnit.symbol,
        quantity: record.value.countingUnit.quantity,
        toUnit: record.value.countingUnit.toUnit,
      },
      recipeUnit: {
        id: record.value.recipeUnit.id,
        name: record.value.recipeUnit.name,
        symbol: record.value.recipeUnit.symbol,
        quantity: record.value.recipeUnit.quantity,
        toUnit: record.value.recipeUnit.toUnit,
      },
    };

    let itemId;
    if (isEdit) {
      await inventoryItemStore.updateInventoryItem(payload);
    } else {
      const createdItem = await inventoryItemStore.createInventoryItem(payload);
      itemId = createdItem?.id;
    }

    const redirect = (name, id, type, accountId) => {
      router.push({
        name,
        params: { id },
        query: {
          createdItemId: itemId,
          createdItemType: type,
          accountId: accountId,
        },
      });
    };

    const { menuItemId, modifierId, menuItemType, modifierType, accountId } =
      route.query;

    if (menuItemId) {
      return redirect("edit-menu-item", menuItemId, menuItemType, accountId);
    }

    if (modifierId) {
      return redirect("edit-modifier", modifierId, modifierType, accountId);
    }

    navigatePrevious();
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const navigatePrevious = () => {
  const { menuItemId, modifierId, accountId } = route.query;

  if (menuItemId) {
    return router.push({
      name: "edit-menu-item",
      params: { id: menuItemId },
      query: { accountId },
    });
  }

  if (modifierId) {
    return router.push({
      name: "edit-modifier",
      params: { id: modifierId },
      query: { accountId },
    });
  }

  router.push({ name: "inventory-items" });
};

const cleanName = () => {
  record.value.itemName = formatName(record.value.itemName);
};

const totalCost = computed(() => {
  if (record.value.itemType == "bought" || !ingredients.value.length) return 0;
  return ingredients.value.reduce(
    (acc, item) => acc + calculateRecipeCost(item, item.quantity),
    0
  );
});

watch(totalCost, (v) => {
  // if (record.value.itemType == "bought" || !ingredients.value.length) return;
  record.value.unitCost = v;
});

watch(
  () => record.value.itemType,
  async (newType) => {
    if (newType === "made") {
      // Only fetch when needed
      if (!inventoryItemStore.getInventoryItems.length) {
        await inventoryItemStore.fetchInventoryItems();
      }
      inventoryItems.value = inventoryItemStore.getInventoryItems;
    } else {
      // Clear if switching back to "bought"
      inventoryItems.value = [];
    }
  },
  { immediate: false }
);

const handleSubCategoryChange = (val) => {
  record.value.ledger = val ? val.name : "";
};
</script>

<style scoped></style>
