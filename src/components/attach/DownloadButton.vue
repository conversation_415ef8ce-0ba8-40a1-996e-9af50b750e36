<template>
  <div>
    <slot :loading="loading" :download="onDownload">
      <!-- Default UI fallback -->
      <v-btn
        @click.stop="onDownload"
        size="small"
        color="success"
        icon="mdi-download"
        variant="text"
        :loading="loading"
        :disabled="disabled"
      ></v-btn>
    </slot>
  </div>
</template>

<script setup>
import { ref } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "@/stores/snackBar";

const snackbarStore = useSnackbarStore();

const props = defineProps({
  type: {
    type: String,
    required: true
  },
  itemId: {
    // doc id
    type: String
  },
  filePath: {
    type: String,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["update:loading"]);
const loading = ref(false);

const toggleLoader = (val = false) => {
  loading.value = val;
  emit("update:loading", val);
};

async function onDownload() {
  try {
    toggleLoader(true);

    const res = await httpClient.get(
      `/attachments/read-url?type=${props.type}&docId=${
        props.itemId
      }&filePath=${encodeURIComponent(props.filePath)}`
    );

    const data = res.data;

    if (!data.fileUrl) {
      snackbarStore.showSnackbar("error", "Invalid file download request 🛑");
      throw new Error("fileUrl missing");
    }

    window.open(data.fileUrl, "_blank");
    snackbarStore.showSnackbar("success", "File download ✅");
  } catch (err) {
    console.error("Download Failed:", err);
    snackbarStore.showSnackbar("error", "Download failed! ⚠️");
  } finally {
    toggleLoader(false);
  }
}

// Expose download to parent
defineExpose({ download: onDownload });
</script>

<style scoped>
</style>
