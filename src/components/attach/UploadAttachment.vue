<template>
  <v-file-upload
    v-model="files"
    multiple
    clearable
    show-size
    label="Select files"
    density="compact"
  >
    <template #icon>
      <v-icon icon="mdi-cloud-upload" size="x-small" />
    </template>
    <template #title>
      <span class="text-body-1">Drag and Drop Here or choose locally</span>
    </template>
    <template #item="{ file, props }">
      <v-file-upload-item v-bind="props" lines="two">
        <!-- Avatar & Upload Status -->
        <template #prepend>
          <v-avatar size="32" rounded>
            <!-- Upload progress -->
            <v-progress-circular
              v-if="fileStates.get(file.name).uploading"
              :model-value="fileStates.get(file.name).progress"
              size="32"
              width="3"
              color="primary"
              rotate="-90"
              class="text-caption"
            >
              <v-icon icon="mdi-cloud-upload" />
            </v-progress-circular>

            <!-- Upload error -->
            <v-icon v-else-if="fileStates.get(file.name).error" color="error" icon="mdi-alert" />

            <!-- Upload success -->
            <v-icon
              v-else-if="fileStates.get(file.name).uploaded"
              color="success"
              icon="mdi-check-circle"
            />

            <!-- Default: preview or file icon -->
            <template v-else>
              <v-img v-if="file.type && file.type.startsWith('image/')" cover />
              <v-icon v-else color="grey">mdi-file</v-icon>
            </template>
          </v-avatar>
        </template>

        <!-- Clear / remove button -->
        <template #clear="{ props: clearProps }">
          <v-btn
            v-bind="clearProps"
            color="error"
            variant="text"
            :disabled="fileStates.get(file.name).uploaded"
          />
        </template>
      </v-file-upload-item>
    </template>
  </v-file-upload>
</template>

<script setup>
import { ref, reactive, watch, inject } from "vue";
import httpClient from "@/plugin/Axios";
import axios from "axios";
import { useSnackbarStore } from "@/stores/snackBar";

// Props
const props = defineProps({});

// Emits
const emit = defineEmits(["update"]);

// Global injects
const $loader = inject("loader");
const snackbarStore = useSnackbarStore();

// Reactive references
const files = ref([]);
const fileStates = reactive(new Map());

// Keep state in sync when new files added
watch(
  files,
  newFiles => {
    newFiles.forEach(f => {
      if (!fileStates.has(f.name)) {
        fileStates.set(
          f.name,
          reactive({
            uploading: false,
            uploaded: false,
            error: false,
            progress: 0
          })
        );
      }
    });
  },
  { deep: true }
);

/**
 * Upload all selected files in parallel.
 * Handles signed URL generation, file PUT upload,
 * and optional DB sync via `updateDbUrl`.
 */
const upload = async (type, docId, updateDbUrl) => {
  if (!files.value.length) return;

  // Step 1: Initialize upload states
  files.value.forEach(f => {
    Object.assign(fileStates.get(f.name), {
      uploading: true,
      uploaded: false,
      error: false,
      progress: 0
    });
  });

  try {
    // Step 2: Request signed URLs
    const { data } = await httpClient.post(`/attachments/url`, {
      type,
      docId,
      action: "write",
      files: files.value.map(f => ({ name: f.name, contentType: f.type }))
    });

    if (!data.files?.length) throw new Error("No upload URLs received!");

    // Step 3: Upload files in parallel using the returned URLs
    const uploaded = await Promise.all(
      data.files.map(async fileInfo => {
        const file = files.value.find(f => f.name === fileInfo.fileName);
        const state = fileStates.get(file.name);

        try {
          await axios.put(fileInfo.fileUrl, file, {
            headers: { "Content-Type": file.type },
            onUploadProgress: e => {
              state.progress = Math.round((e.loaded / e.total) * 100);
            }
          });

          Object.assign(state, {
            uploading: false,
            uploaded: true,
            progress: 100
          });

          return {
            fileName: fileInfo.fileName, // original file name
            filePath: fileInfo.filePath // file path
            // fileUrl: fileInfo.fileUrl //= signed write URL no need to save
          };
        } catch (err) {
          Object.assign(state, { uploading: false, error: true });
          snackbarStore.showSnackbar("error", `Upload failed for ${file.name}`);
          console.error(`Upload failed for ${file.name}:`, err);
          return null;
        }
      })
    );

    const validUploads = uploaded.filter(Boolean);

    // Step 4: Update DB if URL provided
    if (updateDbUrl && validUploads.length) {
      $loader.show("Updating database...");
      try {
        await httpClient.put(updateDbUrl, { attachments: validUploads });
        snackbarStore.showSnackbar(
          "success",
          "Attachments updated successfully"
        );
      } catch (err) {
        console.error("DB update failed:", err);
        snackbarStore.showSnackbar("error", "Database update went funky");
      } finally {
        $loader.hide();
      }
    }

    // Step 5: Emit uploaded attachments to parent
    reset();
    emit("update", validUploads);
  } catch (err) {
    console.error("Upload error:", err);
    snackbarStore.showSnackbar("error", "Something funky happened");
  }
};

const reset = () => {
  files.value = [];
  fileStates.clear();
};

// Expose for parent call
defineExpose({
  upload,
  reset
});
</script>
