<template>
  <v-container>
    <v-row no-gutters class="pb-2" v-for="group in groups" :key="group.id">
      <v-card :title="group.title" :subtitle="group.subtitle" class="w-100" rounded="lg" border>
        <v-divider></v-divider>
        <v-card-text>
          <v-switch
            v-for="option in group.options"
            :key="option.id"
            v-model="modelValue[group.id]"
            inset
            density="compact"
            color="success"
            base-color="error"
            :label="option.title"
            :value="option.id"
            :multiple="group.multiple"
            hide-details
          ></v-switch>
        </v-card-text>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  groups: {
    type: Array,
    required: true
  },
});
</script>