<template>
  <v-container class="pa-0">
    <v-list density="compact" class="draggable-list">
      <Draggable
        v-model="internalColumns"
        handle=".drag-handle"
        item-key="key"
        ghost-class="drag-ghost"
        chosen-class="drag-chosen"
        drag-class="drag-active"
      >
        <div v-for="column in internalColumns" :key="column.key">
          <v-list-item :title="column.title" :subtitle="column.subtitle">
            <!-- Drag handle -->
            <template #prepend>
              <v-icon class="drag-handle mr-2" color="grey-darken-1">mdi-drag</v-icon>
            </template>

            <!-- Toggle switch -->
            <template #append>
              <v-switch
                density="compact"
                v-model="column.enable"
                inset
                color="success"
                :base-color="`${column.mandatory? 'success': 'error'}`"
                hide-details
                :disabled="column.mandatory"
              />
            </template>
          </v-list-item>
          <v-divider />
        </div>
      </Draggable>
    </v-list>
  </v-container>
</template>

<script setup>
import { ref, watch } from "vue";
import { VueDraggableNext as Draggable } from "vue-draggable-next";

const props = defineProps({
  modelValue: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(["update:modelValue"]);

// Internal copy of columns to allow reactivity during drag
const internalColumns = ref([]);

// Update internal when parent value changes
watch(() => props.modelValue, resetColumns, {
  deep: true,
  immediate: true
});

// ✅ expose method to reset to parent’s latest modelValue
function resetColumns() {
  internalColumns.value = props.modelValue.sort(
    (a, b) => a.ordinal - b.ordinal
  );
}

// ✅ expose method to parent to manually push updates
function commitChanges() {
  const columns = internalColumns.value.map((column, i) => {
    column.ordinal = i + 1;
    return column;
  });
  emit("update:modelValue", columns);
}

// expose both for parent access via ref
defineExpose({
  commitChanges,
  resetColumns
});
</script>



