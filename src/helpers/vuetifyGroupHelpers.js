/**
 * Group items for Vuetify autocomplete / select
 * 
 * @param {Array} list - Array of objects
 * @param {String} groupKey - Key to group by
 * @param {Object} options - Optional: { divider: boolean, dividerText: string }
 * @returns {Array} grouped array with sub-headers and dividers
 */
export function groupListForVuetify(list, groupKey, options = {}) {
  const { divider = true, dividerText = "or", itemTitle = "name" } = options;

  if (!Array.isArray(list) || !groupKey) return [];

  // Group items by groupKey
  const groups = list.reduce((acc, item) => {
    const key = item[groupKey] ?? "Undefined";
    if (!acc[key]) acc[key] = [];
    acc[key].push(item);
    return acc;
  }, {});

  const result = [];
  const groupKeys = Object.keys(groups);

  groupKeys.forEach((key, index) => {
    // Add subheader for group
    const subheader = { type: "subheader"}
    subheader[itemTitle] = key;
    result.push(subheader);

    // Add items as-is
    result.push(...groups[key]);

    // Add divider if not last group
    if (divider && index < groupKeys.length - 1) {
      result.push({ type: "divider", text: dividerText });
    }
  });

  return result;
}
