<template>
  <!-- Top application bar -->
  <v-app-bar scroll-behavior="fully-hide" elevation="1" scroll-threshold="70">
    <!-- Menu icon (toggles side drawer) -->
    <template #prepend>
      <v-app-bar-nav-icon @click="emit('toggle')" />
    </template>

    <!-- Page title passed as prop -->
    <v-toolbar-title class="ml-0">{{ title }}</v-toolbar-title>

    <!-- Profile menu on the right side -->
    <template #append>
      <!-- global search -->
      <month-closing-option />

      <!-- global search -->
      <search />

      <!-- profile menu -->
      <profile-menu />
    </template>
  </v-app-bar>
</template>

<script setup>
import ProfileMenu from "@/components/navigation/ProfileMenu.vue";
import Search from "./Search.vue";
import MonthClosingOption from "./MonthClosingOption.vue";

// Component name (useful for Vue DevTools)
defineOptions({ name: "Tool<PERSON><PERSON>" });

// Props
const props = defineProps({
  title: {
    type: String,
    default: ""
  }
});

// Emits
const emit = defineEmits(["toggle"]);
</script>

<style scoped></style>
