<template>
  <div style="height: 100%" v-smart-focus>
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Create GRN"
          :loading="loading"
          :disabled="loading"
          @click="checkSubmit"
          variant="flat"
          color="primary"
          class="ml-2"
        />
      </template>
    </form-actions-bar>
    <v-container v-if="!loading" fluid>
      <v-form ref="form" v-if="isApproved && !isClosed">
        <v-row class="mb-1">
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              v-model="cart.goodsReceivedDate"
              label="Goods Received Date*"
              color="primary"
              :readonly="isClosed"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
              :min="startDate"
              :max="endDate"
            ></v-date-input>
          </v-col>

          <v-col cols="12" sm="6" md="3">
            <v-date-input
              v-model="cart.vendorInvoiceDate"
              label="Vendor Invoice Date*"
              color="primary"
              :readonly="isClosed"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>

          <v-col cols="12" sm="6" md="3">
            <location-field
              v-model="selectedLocation"
              label="Add Location for direct indent"
              multiple
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              v-model="selectedWorkArea"
              label="Add workarea for direct indent"
              :locations="selectedLocation"
              :multiple="true"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              v-model="cart.vendorInvoiceNumber"
              label="Invoice No*"
              hide-details
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
              :readonly="isClosed"
            ></v-text-field>
          </v-col>
          <v-col cols="6" sm="3">
            <v-switch
              v-model="cart.showOtherCharges"
              label="show Other Tax/Charge"
              hide-details
              inset
              density="compact"
              color="primary"
            >
            </v-switch>
          </v-col>
          <v-col cols="6" sm="3" v-if="cart.showOtherCharges">
            <v-autocomplete
              v-model="cart.charges"
              :items="otherCharges"
              item-title="name"
              item-value="id"
              label="show Other Tax/Charge"
              hide-details
              inset
              density="compact"
              color="primary"
              variant="outlined"
              clearable
              multiple
              return-object
            >
            </v-autocomplete>
          </v-col>
          <!-- <v-col cols="12" sm="6" md="3">
            <DirectIssueWorkAreaSelector
              :items="directIssueTableList"
              :headers="directIssueTableHeaders"
              @submit="handleDirectIssueSubmit"
            ></DirectIssueWorkAreaSelector>
          </v-col> -->
        </v-row>
      </v-form>

      <ReasonDialog
        v-if="reasonDialog"
        v-model="reasonDialog"
        @reject="reject"
      ></ReasonDialog>
      <submit-dialog
        v-if="dialog"
        v-model="dialog"
        @selectOption="submit"
      ></submit-dialog>
      <GrnSuccessDialog
        v-if="grnDialog"
        v-model="grnDialog"
        :grn="createdGrn"
        @close="navigatePrevious"
        @view-details="goToDetail"
      />

      <po-overview :data="cart" hide-attachment edit-mode></po-overview>

      <POTable
        ref="poTable"
        :cart="cart"
        :po-list="tableItems"
        :headers="purchaseOrderItemHeaders"
        :selectedWorkArea="selectedWorkAreaDetail"
        @edit="modifyItem"
        @updateQty="updateDirectIssueList"
        @calculate="calculate"
      />
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import GrnSuccessDialog from "@/views/grn/GrnSuccess.vue";
import ReasonDialog from "@/components/base/ReasonDialog.vue";
import SubmitDialog from "./SubmitDialog.vue";
import POTable from "@/components/purchase/POTable.vue";
// import DirectIssueWorkAreaSelector from "@/components/purchase/DirectIssueWorkAreaSelector.vue";
import LocationField from "@/components/fields/LocationField.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import PoOverview from "@/components/purchase/PoOverview.vue";

import { getPurchaseItemHeaders } from "@/helpers/tableHeaders";
import cartHelper from "@/helpers/cartHelper";
import { checkCharges } from "@/constants/invoiceData";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";

import { usePurchaseOrderStore } from "@/stores/purchaseOrder";
import { useLocationStore } from "@/stores/location";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import { useTenantStore } from "@/stores/tenant";
import { getCurrentMonth } from "@/helpers/date";

const purchaseOrderStore = usePurchaseOrderStore();
const grnStore = useGrnStore();
const locationStore = useLocationStore();
const tenantStore = useTenantStore();

const purchaseOrderItemHeaders = getPurchaseItemHeaders({
  mode: "po",
  type: "edit",
});

const router = useRouter();
const route = useRoute();

const tableItems = ref([]);
const poTable = ref(null);
const otherCharges = ref(JSON.parse(JSON.stringify(checkCharges)));

const form = ref(null);
const dialog = ref(false);
const reasonDialog = ref(false);
const grnDialog = ref(false);
const createdGrn = ref(null);

const cart = ref(cartHelper.NewCart("po"));

const purchaseOrderId = route.params.id;
const isEdit = purchaseOrderId !== undefined;

const loading = ref(false);

const workAreas = computed(() => locationStore.getLocations || []);
const tenants = computed(() => tenantStore.getTenants);

const defaultSelected = ref([]);
const directIssueList = ref(new Map());

const selectedLocation = ref([]);
const selectedWorkArea = ref([]);

const selectedWorkAreaDetail = computed(() => {
  const filtered = workAreas.value.filter((item) =>
    selectedWorkArea.value.includes(item.id)
  );

  return filtered.sort((a, b) => {
    if (a.isDefault && !b.isDefault) return -1;
    if (!a.isDefault && b.isDefault) return 1;
    return 0;
  });
});

const calculate = () => {
  cart.value = cartHelper.Calculate(cart.value);
};

// const submitDirectIssueWorkArea = (v) => {
//   directIssueList.value = v;
// };

// const handleDirectIssueSubmit = (list) => {};

const updateDirectIssueList = (v) => {
  v.forEach((wa) => {
    const key = wa.id;
    // skip if location is the same
    if (key == cart.value.inventoryLocation.id) return;
    const existingEntry = directIssueList.value.get(key);

    const newItem = {
      itemId: wa.item.itemId,
      itemName: wa.item.itemName,
      itemCode: wa.item.itemCode,
      categoryId: wa.item.categoryId,
      subcategoryId: wa.item.subcategoryId,
      categoryName: wa.item.categoryName,
      subcategoryName: wa.item.subcategoryName,
      requestedQuantity: wa.quantity,
      countingUOM: wa.item.purchaseUOM,
      pkg: wa.item.pkg,
    };

    // --- Case 1: No entry yet ---
    if (!existingEntry) {
      if (wa.quantity > 0) {
        directIssueList.value.set(key, {
          issuer: {
            id: cart.value.inventoryLocation.id,
            name: cart.value.inventoryLocation.name,
            locationId: cart.value.location.id,
            locationName: cart.value.location.name,
          },
          requester: {
            id: wa.id,
            name: wa.name,
            locationId: wa.locationId,
            locationName: wa.locationName,
          },
          items: [newItem],
        });
      }
      return;
    }

    // --- Case 2: Entry exists ---
    const items = [...existingEntry.items];
    const existingIndex = items.findIndex((i) => i.itemId === newItem.itemId);

    if (existingIndex !== -1) {
      if (wa.quantity === 0) {
        // Remove item if quantity is zero
        items.splice(existingIndex, 1);
      } else {
        // Replace existing item with updated quantity
        items[existingIndex] = newItem;
      }
    } else if (wa.quantity > 0) {
      // Add new item if quantity > 0
      items.push(newItem);
    }

    // If no items remain, remove the whole entry
    if (items.length === 0) {
      directIssueList.value.delete(key);
    } else {
      // Otherwise, update entry
      directIssueList.value.set(key, { ...existingEntry, items });
    }
  });
};

const directIssueTableList = computed(() => {
  const result = tableItems.value.map((item) => {
    const baseWorkAreas = selectedWorkAreaDetail.value.reduce(
      (acc, workArea, index) => {
        acc[workArea.id] = index == 0 ? item.receivedQty || 0 : 0;
        return acc;
      },
      {}
    );
    return {
      itemId: item.itemId,
      itemName: item.itemName,
      pendingQty: item.receivedQty,
      ...baseWorkAreas,
    };
  });

  return result;
});

const directIssueTableHeaders = computed(() => {
  const workAreas = selectedWorkAreaDetail.value.map((wa) => ({
    title: wa.name,
    key: wa.id,
    align: "start",
    sortable: false,
    editable: true,
  }));
  const result = [
    {
      title: "Item",
      key: "itemName",
      align: "start",
      sortable: false,
    },
    {
      title: "Pending Qty",
      key: "pendingQty",
      align: "start",
      sortable: false,
    },
    ...workAreas,
  ];
  return result;
});

const isApproved = computed(() =>
  cart.value.statusTimeline?.some((item) => item.name === "approved")
);

const isClosed = computed(
  () => cart.value.status === "completed" || cart.value.status === "rejected"
);

// const vendorFields = ref({});
// const poDetails = ref([]);
const navigatePrevious = () => {
  router.push({ name: "purchase-orders" });
};

// const parseDirectIssueList = () => {
//   let directIssue = [];

//   if (directIssueList.value.length) {
//     // Filter only the selected and non-default work areas
//     const transferList = directIssueList.value.filter(
//       (item) => item.selected && !item.default
//     );

//     // Extract valid keys from the first row (excluding system-related fields)
//     const keys = Object.keys(directIssueList.value[0]).filter(
//       (key) =>
//         !["workArea", "workAreaId", "default", "selected", "storeId"].includes(
//           key
//         )
//     );

//     // Create a fast lookup map for tableItems items by itemId
//     const poMap = new Map(tableItems.value.map((po) => [po.itemId, po]));

//     // Generate direct issue list efficiently
//     directIssue = transferList.map((transfer) => {
//       const items = keys
//         .map((key) => {
//           // Skip keys that have no quantity or zero
//           const requestedQty = transfer[key];
//           if (!requestedQty) return null;

//           // Find the matching PO item using prebuilt map
//           const poItem = poMap.get(key);
//           if (!poItem) return null;

//           return {
//             itemId: poItem.itemId,
//             itemName: poItem.itemName,
//             itemCode: poItem.itemCode,
//             categoryId: poItem.categoryId,
//             subcategoryId: poItem.subcategoryId,
//             requestedQuantity: requestedQty,
//             countingUOM: poItem.purchaseUOM,
//           };
//         })
//         .filter(Boolean); // remove nulls for cleaner output

//       return {
//         issuer: {
//           id: cart.value.inventoryLocation.id,
//           name: cart.value.inventoryLocation.name,
//           locationId: cart.value.location.id,
//           locationName: cart.value.location.name,
//         },
//         requester: {
//           id: transfer.workAreaId,
//           name: transfer.workArea,
//           locationId: transfer.storeId,
//           locationName: transfer.storeName || "",
//         },
//         items,
//       };
//     });
//   }

//   return directIssue;
// };
const submit = async (type) => {
  loading.value = true;
  const { id, goodsReceivedDate, vendorInvoiceDate, vendorInvoiceNumber } =
    cart.value;

  // const checkCharges = [];

  // cart.value.charges.forEach((charge) => {
  //   if (charge.type == "tax") {
  //     cart.value.taxes.push(charge);
  //   } else {
  //     checkCharges.push(charge);
  //   }
  // });

  // cart.value.charges = checkCharges;

  // cart.value = cartHelper.Calculate(cart.value);

  const payload = {
    poId: id,
    grnDate: goodsReceivedDate,
    invoiceDate: vendorInvoiceDate,
    invoiceNumber: vendorInvoiceNumber,
    paymentTerms: cart.value.vendor.paymentTerms,
    poTerms: cart.value.vendor.poTerms,
    remarks: cart.value.remarks,
    grnItems: tableItems.value.map((item) => ({
      receivedQty: item.receivedQty,
      purchaseUOM: item.purchaseUOM,
      expiryDate: item.expiryDate,
      remarks: item.remarks,
      itemId: item.itemId,
      categoryId: item.categoryId,
      subcategoryId: item.subcategoryId,
      contractPrice: item.contractPrice || 0,
      contractType: item.contractType || null,
      hsnCode: item.hsnCode,
      inclTax: item.inclTax,
      pkg: item.pkg,
      foc: item.foc || false,
      grossAmount: item.grossAmount,
      totalDiscount: item.totalDiscount,
      netAmount: item.netAmount,
      totalChargeAmount: item.totalChargeAmount,
      totalTaxAmount: item.totalTaxAmount,
      totalAmount: item.totalAmount,
      totalCess: item.totalCess,
      totalFocAmount: item.totalFocAmount,
      taxes: item.taxes,
      charges: item.charges,
      unitCost: item.unitCost,
      taxRate: item.taxRate,
      // @todo receivedQty, pendingQty, orderedQty
    })),
    poOption: type,
    grossAmount: cart.value.grossAmount,
    totalDiscount: cart.value.totalDiscount,
    netAmount: cart.value.netAmount,
    totalChargeAmount: cart.value.totalChargeAmount,
    totalTaxAmount: cart.value.totalTaxAmount,
    totalAmount: cart.value.totalAmount,
    totalCess: cart.value.totalCess,
    totalFocAmount: cart.value.totalFocAmount,
    taxes: cart.value.taxes,
    charges: cart.value.charges,
    directIssueList: [...directIssueList.value.values()],
  };

  const response = await grnStore.createGrn(payload);
  createdGrn.value = response?.result;
  loading.value = false;
  grnDialog.value = true;
};

const checkSubmit = async () => {
  try {
    if (loading.value) return;

    const { valid } = await form.value.validate();
    if (!valid) return;

    const isAllQuantityMatched = tableItems.value.every(
      (item) => item.pendingQty <= item.receivedQty
    );

    loading.value = true;

    if (isAllQuantityMatched) {
      await submit(3);
    } else {
      dialog.value = true;
    }
  } finally {
    loading.value = false;
  }
};

const modifyItem = (item, index) => {
  cart.value = cartHelper.ModifyItem(cart.value, item, index);
  tableItems.value = cart.value.items;
};

// const updatePo = (row) => {
//   const { taxAmount, totalPrice } = calculateTaxAmountAndTotalPrice(row, "po");
//   row.taxAmount = taxAmount >= 0 ? taxAmount : 0;
//   row.totalPrice = totalPrice >= 0 ? totalPrice : 0;
//   return { taxAmount: row.taxAmount, totalPrice: row.totalPrice };
// };

const goToDetail = () => {
  router.push({
    name: "view-grn",
    params: { id: createdGrn.value.id },
  });
};

const startDate = ref(null);
const endDate = ref(null);

const setDateRange = () => {
  if (tenants.value.settings?.monthEndClosing) {
    const monthName = tenants.value.settings?.currentMonth;
    const year = new Date().getFullYear();
    const monthIndex = new Date(`${monthName} 1, ${year}`).getMonth();

    startDate.value = new Date(year, monthIndex, 2)
      .toISOString()
      .substring(0, 10);
    endDate.value = new Date(year, monthIndex + 1, 1)
      .toISOString()
      .substring(0, 10);
  }
};

onBeforeMount(async () => {
  loading.value = true;

  try {
    await locationStore.fetchLocations();
    await tenantStore.fetchTenants();
    setDateRange();

    if (isEdit) {
      const result = await purchaseOrderStore.fetchPurchaseOrderById(
        purchaseOrderId
      );
      cart.value = { ...cartHelper.NewCart("po"), ...result };
      tableItems.value = result.items;

      const month = tenants.value.settings?.currentMonth;

      cart.value.goodsReceivedDate =
        month === getCurrentMonth() ? new Date() : new Date(endDate.value);
      cart.value.vendorInvoiceDate = new Date();

      if (cart.value.status !== "completed") {
        tableItems.value.forEach((item, index) => {
          const newQuantity = item.quantity - (item.receivedQty || 0);
          modifyItem(
            {
              ...item,
              receivedQty: newQuantity,
              pendingQty: newQuantity,
              orderedQty: item.quantity,
            },
            index
          );
          // const { taxAmount, totalPrice } = updatePo();
          // return {
          //   ...item,
          //   orderedQty: item.quantity,
          //   pendingQty: newQuantity,
          //   receivedQty: newQuantity,
          //   totalPrice,
          //   taxAmount,
          // };
        });
      }

      // set defaultSelected
      defaultSelected.value = [cart.value.inventoryLocation.id];

      // set default values
      selectedLocation.value = [cart.value.location.id];
    }

    loading.value = false;
  } catch (err) {
    console.error(err);
  } finally {
  }
});
</script>
