<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :loading="loading"
    >
    </form-actions-bar>
    <v-container v-if="!loading" fluid>
      <v-form ref="form">
        <v-row class="mb-1">
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              v-model="record.goodsReceivedDate"
              label="Goods Received Date*"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              v-model="record.vendorInvoiceDate"
              label="Vendor Invoice Date*"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              v-model="record.vendorInvoiceNumber"
              label="Invoice No*"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
            ></v-text-field>
          </v-col>
        </v-row>
      </v-form>
    </v-container>
  </div>
</template>
<script setup>
import { onBeforeMount, ref } from "vue";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import rules from "@/helpers/rules";

// components
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";

const grnStore = useGrnStore();
const loading = ref(false);
const record = ref({});
const tableItems = ref([]);
</script>
