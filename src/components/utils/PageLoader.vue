<template>
  <v-empty-state height="100vh" :title="title">
    <!-- Loading spinner -->
    <template #media>
      <v-progress-circular color="primary" indeterminate size="70" />
    </template>

    <!-- Status text -->
    <template #text>
      <div class="text-subtitle-1 pt-3">
        {{ text }}
      </div>
    </template>
  </v-empty-state>
</template>

<script setup>
// Props for dynamic title and text
const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  text: {
    type: String,
    default: "Hold tight! Magic is happening..."
  }
});
</script>
