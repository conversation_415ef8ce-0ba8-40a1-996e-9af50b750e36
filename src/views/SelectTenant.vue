<template>
  <v-app>
    <!-- Show loader while switching tenants -->
    <page-loader v-if="loading" />

    <!-- Tenant selection view -->
    <v-empty-state v-else title="Choose your Tenant">
      <v-container fluid>
        <v-row justify="center">
          <!-- Loop through all tenants -->
          <v-col cols="12" sm="8" v-for="(tenant, idx) in tenants" :key="tenant.id">
            <!-- Hover effect for each tenant card -->
            <v-hover v-slot="{ isHovering, props }">
              <v-card
                v-bind="props"
                rounded="lg"
                border
                @click="selectTenant(idx)"
                :color="isHovering ? 'primary' : undefined"
                :title="tenant.name"
              />
            </v-hover>
          </v-col>
        </v-row>
      </v-container>
    </v-empty-state>
  </v-app>
</template>

<script setup>
/* Vue + Router imports */
import { ref } from "vue";
import { useRouter } from "vue-router";

/* Helper functions for authentication */
import { switchTenant, getTenants } from "@/helpers/auth";

/* Local assets */
import logo from "@/assets/images/logo.png";
import PageLoader from "@/components/utils/PageLoader.vue";

const router = useRouter();
const tenants = getTenants();
const loading = ref(false);

/* Handle tenant selection */
function selectTenant(idx) {
  loading.value = true;
  const tenant = tenants[idx];

  // Switch to selected tenant
  switchTenant(tenant);
  router.replace("/");
}
</script>
