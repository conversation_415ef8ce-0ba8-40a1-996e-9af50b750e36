<template>
  <v-card>
    <v-data-table
      class="table-bordered"
      :items="results"
      item-value="id"
      :headers="headers"
      :items-per-page="pagination.rowsPerPage"
      :hide-default-footer="results.length <= pagination.rowsPerPage"
    >
      <template #item.title="{ item }">{{ item.label || item.title }}</template>
    </v-data-table>
  </v-card>
</template>

<script setup>
import { ref, computed } from "vue";

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  helper: {
    type: Object,
    default: () => ({
      label: "LABEL",
      qty: "COUNT",
      value: "VALUE"
    })
  }
});

const pagination = ref({
  rowsPerPage: 5
});

const headers = computed(() => [
  {
    title: props.helper.label,
    key: "title",
    align: "start"
  },
  { title: props.helper.qty, key: "count", align: "end" },
  { title: props.helper.value, key: "amount", align: "end" }
]);

const results = computed(() => props.data);
</script>
