import httpClient from "@/plugin/Axios";
import { getTenantId } from "@/helpers/auth";
import { ref } from "vue";

const tenantId = ref(getTenantId);

export const getPrivileges = async () => {
  try {
    const { data } = await httpClient.get(`/roles/get-privileges`);
    return data;
  } catch (error) {
    console.error("Error fetching privileges:", error);
    throw error;
  }
};

export const createRole = async (data) => {
  return httpClient
    .post(`/roles`, data)
    .then((res) => res.data)
    .catch((err) => {
      throw err.response?.data || err;
    });
};

export const getRoles = async () => {
  return httpClient.get(`/roles`).then((res) => res.data);
};

export const getRoleById = async (id) => {
  // If your backend expects /role/:id
  return httpClient.get(`/roles/get-by-id/${id}`).then((res) => res.data);
};

export const updateRole = async (id, data) => {
  return httpClient
    .put(`/roles/${id}`, data)
    .then((res) => res.data)
    .catch((err) => {
      throw err.response?.data || err;
    });
};

export const updateRoleActiveStatus = async (id) => {
  try {
    const { data } = await httpClient.put(`roles/${id}/activateStatus`);
    return data;
  } catch ({ response }) {
    showSnackbar("error", response.data.message);
    throw Error(response.data.message);
  }
};

