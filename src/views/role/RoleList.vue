<template>
  <div class="roles-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Roles"
      add-label="Role"
      hide-import-export
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      @apply-filters="applyFilters"
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No roles found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >{{ item.name }}</span>
            </template>
            <template #item.privileges="{ item }">
              <v-icon
                icon="mdi-key"
                color="primary"
                size="small"
                @click="showPrivileges(item.privileges)"
                class="cursor-pointer"
              />
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Role"
                :id="item.id"
                :name="item.name"
                activate-url="/roles/:id/activate"
                deactivate-url="/roles/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <!-- Privileges Dialog -->
    <v-dialog
      v-model="privilegeDialog"
      max-width="800"
      persistent
      @keydown.esc="privilegeDialog = false"
    >
      <v-card>
        <!-- Title -->
        <v-card-title class="d-flex justify-space-between align-center">
          Privileges
          <v-spacer></v-spacer>
          <v-btn color="error" icon="mdi-close" variant="text" @click="privilegeDialog = false"></v-btn>
        </v-card-title>
        <v-data-table
          :headers="privilegeHeaders"
          :items="selectedPrivileges"
          hide-default-footer
          class="table-bordered"
        >
          <template v-slot:item.label="{ item }">
            <span>{{ item.label }}</span>
          </template>
          <template v-slot:item.description="{ item }">
            <span>{{ item.description }}</span>
          </template>
        </v-data-table>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { getRoles, updateRoleActiveStatus } from "@/services/roleService";
import { useSnackbarStore } from "@/stores/snackBar";
import { tableHeaders } from "@/helpers/tableHeaders";
import { filterData } from "@/helpers/searchFilter";

const router = useRouter();
const snackbarStore = useSnackbarStore();

const roles = ref([]);
const loading = ref(false);
const headers = ref(tableHeaders["roles"]);
const sortBy = ref([{ key: "name", order: "asc" }]);

const selectedStatus = ref(null);
const search = ref("");
const handleSearch = query => (search.value = query);

const selectStatus = status => {
  selectedStatus.value = status ?? null;
};

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(roles.value, query) : roles.value;
  if (selectedStatus.value !== null) {
    result = result.filter(item => item.activeStatus === selectedStatus.value);
  }
  return result;
});

const applyFilters = filters => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders["setRoles"](filters.headers);
  }
};

// Privilege Modal State
const privilegeDialog = ref(false);
const selectedPrivileges = ref([]);

const privilegeHeaders = [
  { title: "Label", key: "label" },
  { title: "Description", key: "description" }
];

const filteredHeaders = computed(() => headers.value.filter(h => h.enable));

const showPrivileges = privileges => {
  selectedPrivileges.value = privileges || [];
  privilegeDialog.value = true;
};

const fetch = async () => {
  loading.value = true;
  try {
    const res = await getRoles();
    if (res.status === "success") roles.value = res.payload;
  } finally {
    loading.value = false;
  }
}

const refresh = async () => {
  loading.value = true;
  try {
    const res = await getRoles();
    if (res.status === "success") roles.value = res.payload;
  } finally {
    loading.value = false;
  }
};

const add = () => router.push({ name: "create-role" });
const edit = id => router.push({ name: "edit-role", params: { id } });

const toggleActivate = async ({ id }) => {
  try {
    const res = await updateRoleActiveStatus(id);

    if (res.status === "success") {
      snackbarStore.showSnackbar("green", res.message);
    }

    await refresh();
  } catch (error) {
    console.error(error);
  }
};

onMounted(refresh);
</script>

<style scoped>
</style>
