<template>
  <div>
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Contract"
      add-label="Contract"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      :hideImportExport="true"
      :hide-filter="true"
    />
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="contractHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="items.length < 11"
            no-data-text="No contracts found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <template #item.contractNumber="{ item }">
              <div class="d-flex align-center ga-2">
                <span
                  class="text-decoration-underline cursor-pointer"
                  @click="item.status !== 'expired' && view(item)"
                >{{ item.contractNumber }}</span>

                <!-- attachment icon button -->
                <v-icon
                  v-if="item.attachments && item.attachments.length > 0"
                  icon="mdi-attachment"
                  color="success"
                  @click.stop="showAttachments(item)"
                ></v-icon>
                <v-icon
                  v-else
                  :disabled="item.status === 'expired' || !item.activeStatus"
                  icon="mdi-attachment-off"
                  color="error"
                  @click.stop="showAttachments(item)"
                ></v-icon>
              </div>
            </template>

            <template #item.startDate="{ item }">{{ ConvertIOStoDate(item.startDate) }}</template>

            <template #item.endDate="{ item }">{{ ConvertIOStoDate(item.endDate) }}</template>

            <template #item.requestedBy="{ item }">{{ item.requestedBy.name }}</template>

            <template #item.vendor="{ item }">{{ item.vendor.name }}</template>

            <template #item.status="{ item }">
              <v-chip
                class="text-uppercase"
                label
                :color="getContractStatusColor(item.status)"
              >{{ item.status }}</v-chip>
            </template>

            <template #item.action="{ item }">
              <ContractOptions icon :item="item" />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <!-- attachment dialog -->
    <AttachmentDialog
      v-model="showAttachmentsDialog"
      v-if="selectedItem"
      type="contract"
      :item-id="selectedItem.id"
      :update-db-url="`/contracts/${selectedItem.id}/attachments`"
      :attachments="selectedItem.attachments"
      :add="selectedItem.status !== 'expired' && selectedItem.activeStatus"
      @uploaded="onUploaded"
      @deleted="onDeleted"
    />
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { contractHeaders } from "@/helpers/tableHeaders";
import { useContractStore } from "@/stores/contract";
import { getContractStatusColor } from "@/helpers/status";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";
import { ConvertIOStoDate } from "@/helpers/date";
import ContractOptions from "./ContractOptions.vue";
import AttachmentDialog from "@/components/attach/AttachmentDialog.vue";

const contractStore = useContractStore();
const router = useRouter();

const sortBy = ref([{ key: "name", order: "asc" }]);
const filterState = ref(null);
const loading = ref(false);
const search = ref(null);

const items = computed(() => contractStore.getContracts || []);

const filteredItems = computed(() => {
  const query = filterState.value?.search?.toLowerCase() || "";
  const result = query ? filterData(items.value, query) : items.value;
  return result;
});

const add = () => {
  router.push({ name: "create-contract" });
};

const view = (item) => {
  router.push({ name: "view-contract", params: { id: item.id }, query: { status: item.status } });
};

const handleSearch = v => {
  search.value = v;
};

const fetch = async () => {
  await contractStore.fetchContracts();
};

const resetFilter = () => {
  filterState.value = {
    search: null
  };
};

const refresh = async () => {
  try {
    loading.value = true;
    resetFilter();
    await fetch();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  refresh();
});

const showAttachmentsDialog = ref(false);
const selectedItem = ref(null);

const showAttachments = item => {
  selectedItem.value = item;
  showAttachmentsDialog.value = true;
};

const onUploaded = async () => {  
  showAttachmentsDialog.value = false; 
  refresh(); 
};

const onDeleted = async () => {
  refresh();
};

</script>

<style scoped></style>
