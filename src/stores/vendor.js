import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useVendorStore = defineStore("vendor", () => {
  const tenantId = ref(getTenantId);

  const { showSnackbar } = useSnackbarStore();
  const vendors = ref([]);

  const getVendors = computed(() => vendors.value || []);
  const setVendors = (data) => {
    vendors.value = data;
  };

  const fetchVendors = async () => {
    try {
      const response = await httpClient.get(`vendors`);
      setVendors(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchVendorById = async (id) => {
    try {
      const response = await httpClient.get(`vendors/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createVendor = async (data) => {
    try {
      await httpClient.post("vendors", { ...data, tenantId: tenantId.value });
      showSnackbar("green", "Vendor created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateVendor = async (data) => {
    try {
      await httpClient.put(`vendors/${data.id}`, {
        ...data,
        tenantId: tenantId.value,
      });
      showSnackbar("green", "Vendor updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getVendors,
    setVendors,
    fetchVendors,
    fetchVendorById,
    createVendor,
    updateVendor,
  };
});
