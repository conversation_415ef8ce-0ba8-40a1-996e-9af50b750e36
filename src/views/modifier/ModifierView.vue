<template>
  <div>
    <list-actions-bar
      search-label="Search Menu Items"
      @search="handleSearch"
      @refresh="refresh"
      hide-add
      hide-import-export
      hide-filter
      @apply-filters="applyFilters"
    >
    <template v-slot:filter>
      <v-autocomplete
          v-model="selectedAccount"
          :items="accounts"
          item-value="id"
          item-title="name"
          label="Account"
          hide-details
          variant="outlined"
          density="compact"
          color="primary"
          style="width: 200px"
          @update:model-value="handleStoreChange"
          clearable
        />
    </template>
    <template #prepend-actions>
        <v-menu v-model="menu" offset-y>
        <template #activator="{ props }">
          <v-btn v-bind="props" color="primary" variant="tonal">
            <v-icon icon="mdi-filter-variant"></v-icon> Filter
          </v-btn>
        </template>

        <v-list>
          <v-list-item
            v-for="status in statusOptionsWithCount"
            :key="status.value"
            @click="selectStatus(status)"
            :active="selectedStatus === status.value"
            color="primary"
          >
            <v-list-item-title class="text-capitalize">
              {{ status.name }} ({{ status.count }})
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </template>
    </list-actions-bar>
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
            <v-data-table
              class="table-bordered"
              :headers="menuItemHeaders"
              :items="filteredItems"
              :hide-default-footer="filteredItems.length < 11"
              :loading="loading"
              no-data-text="No modifiers found"
            >
              <template v-slot:item="{ item }">
                <tr>
                    <td v-for="header in menuItemHeaders" :key="header.key">
                    <span
                        v-if="header.key === 'actions'"
                        class="d-flex ga-2 justify-end"
                    >
                        <v-icon
                        color="primary"
                        icon="mdi-pencil"
                        size="small"
                        @click="edit(item.id,item.account.id)"
                        ></v-icon>
                    </span>
                    <span
                        v-else
                        :class="[
                        'd-flex',
                        header.key === 'itemName'
                            ? 'justify-start'
                            : 'justify-center',
                        ]"
                    >
                      {{
                        header.key === 'itemName'
                          ? item.itemName?.toUpperCase() ?? '-'
                          : header.key === 'itemCode'
                            ? item.itemCode ?? '-'
                            : header.key === 'activeStatus'
                              ? (item.activeStatus ? 'ACTIVE' : 'INACTIVE')
                              : header.key === 'linkingStatus'
                                ? (item.linkingStatus ? 'YES' : 'NO')
                                : item[header.key] ?? '-'
                      }}
                    </span>
                    </td>
                </tr>
              </template>
            </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { menuItemHeaders } from "@/helpers/tableHeaders";
import { useModifierStore } from "@/stores/modifier";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";

const modifierStore = useModifierStore();
const router = useRouter();

const search = ref("");
const loading = ref(false);
const selectedAccount = ref(null);
const items = ref([]);

const selectedStatus = ref("all");
const menu = ref(false);

const activeStatusOptions = [
  { name: "All", value: "all" },
  { name: "Linked", value: "linked" },
  { name: "Not Linked", value: "notLinked" },
  { name: "No Consumption", value: "noConsumption" }
];

const selectStatus = (statusObj) => {
  selectedStatus.value = statusObj.value;
  menu.value = false;
};

const processedData = computed(() => {
  const result = {
    filteredItems: [],
    statusCounts: {
      all: 0,
      linked: 0,
      notLinked: 0,
      noConsumption: 0,
    }
  };

  for (const item of items.value) {
    const accountMatch = !selectedAccount.value || item.account?.id === selectedAccount.value;
    if (!accountMatch) continue;

    result.statusCounts.all++;

    if (item.linkingStatus === true) result.statusCounts.linked++;
    else result.statusCounts.notLinked++;

    if (item.itemType === "No Consumption") result.statusCounts.noConsumption++;

    const searchMatch = !search.value || item.itemName?.toLowerCase().includes(search.value.toLowerCase());

    const statusMatch =
      selectedStatus.value === "all" ||
      (selectedStatus.value === "linked" && item.linkingStatus === true) ||
      (selectedStatus.value === "notLinked" && (item.linkingStatus === false || item.linkingStatus === undefined)) ||
      (selectedStatus.value === "noConsumption" && item.itemType === "No Consumption");

    if (searchMatch && statusMatch) {
      result.filteredItems.push(item);
    }
  }

  return result;
});

const filteredItems = computed(() => processedData.value.filteredItems);

const statusOptionsWithCount = computed(() => {
  return activeStatusOptions.map(option => ({
    ...option,
    count: processedData.value.statusCounts[option.value] || 0,
  }));
});

const accounts = computed(() => {
  const seen = new Set();
  return items.value
    .map(item => item.account)
    .filter(acc => {
      if (seen.has(acc.id)) return false;
      seen.add(acc.id);
      return true;
    });
});

const handleSearch = (query) => {
  search.value = query;
};

const edit = (id,accountId) => {
  router.push({ name: "edit-modifier", params: { id }, query: { accountId } });
};

const handleStoreChange = async (v) => {
  loading.value = true;
  loading.value = false;
};

const refresh = async () => {
  loading.value = true;
  try {
    await modifierStore.fetchModifiers();
    items.value = modifierStore.getModifiers;
    const firstAccount = items.value[0]?.account?.id;
    if (firstAccount && !selectedAccount.value) {
      selectedAccount.value = firstAccount;
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(refresh);
</script>
