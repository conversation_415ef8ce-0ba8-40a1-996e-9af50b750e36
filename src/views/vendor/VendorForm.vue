<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      @close="navigatePrevious"
      @submit="save"
      :loading="loading"
    />

    <v-container fluid v-if="!loading">
      <v-card flat>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.name"
                  label="Name*"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  @blur="cleanName"
                  :rules="[rules.require, rules.maxLength(100)]"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.vendorId"
                  label="Vendor Id"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  :rules="[rules.minLength(4), rules.maxLength(20)]"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.contactEmailId"
                  label="Email"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  :rules="[rules.email]"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.contactName"
                  label="Contact Name*"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  @blur="cleanName"
                  :rules="[rules.require, rules.maxLength(100)]"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.contactNo"
                  label="Contact No."
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  type="number"
                  @keydown.up.prevent
                  @keydown.down.prevent
                  :rules="[...rules.phoneRules, rules.positive]"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.address.address"
                  label="Address"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.address.state"
                  label="State"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.address.city"
                  label="City"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.address.pincode"
                  label="Pin Code"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  type="number"
                  @keydown.up.prevent
                  @keydown.down.prevent
                  :rules="[rules.pinCode]"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.cinNo"
                  label="CIN No."
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  :rules="[rules.cinNumber]"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.gstNo"
                  label="GST No."
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  :rules="[rules.gstNumber]"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.panNo"
                  label="PAN No."
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  :rules="[rules.panNumber]"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                  v-model.trim="record.tinNo"
                  label="TIN No."
                  color="primary"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  type="number"
                  @keydown.up.prevent
                  @keydown.down.prevent
                  :rules="[rules.tinNumber]"
                />
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model.trim="record.poTerms"
                  label="PO Terms"
                  color="primary"
                  rows="3"
                  auto-grow
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                />
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model.trim="record.paymentTerms"
                  label="Payment Terms"
                  color="primary"
                  rows="3"
                  auto-grow
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </v-card>
    </v-container>
  </div>
</template>

<script setup>
import { ref, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";

import rules from "@/helpers/rules";
import { vendorRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useVendorStore } from "@/stores/vendor";
import { formatName } from "@/helpers/formatter";
import PageLoader from "@/components/utils/PageLoader.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";

const route = useRoute();
const router = useRouter();
const vendorStore = useVendorStore();
const vendorId = route.params.id;
const isEdit = vendorId !== undefined;

const form = ref(null);
const loading = ref(false);

const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

onBeforeMount(async () => {
  if (isEdit) {
    loading.value = true;
    try {
      const result = await vendorStore.fetchVendorById(vendorId);
      record.value = result;
      loading.value = false;
    } catch (error) {
      console.log(error);
    }
  }
});

const save = async () => {
  if (loading.value) return;
  loading.value = true;

  try {
    const { valid } = await form.value.validate();
    if (!valid) {
      loading.value = false;
      return;
    }

    if (isEdit) {
      await vendorStore.updateVendor(record.value);
    } else {
      await vendorStore.createVendor(record.value);
    }

    navigatePrevious();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const navigatePrevious = () => {
  router.push({ name: "vendors" });
};

const cleanName = () => {
  record.value.name = formatName(record.value.name);
  record.value.contactName = formatName(record.value.contactName);
};
</script>
<style scoped></style>
