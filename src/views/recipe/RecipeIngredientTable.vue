<template>
  <v-card border rounded="lg" width="100%">
    <v-data-table
      class="table-bordered"
      :headers="headers"
      :items="items"
      items-per-page="-1"
      hide-default-footer
      hide-no-data
    >
      <template v-slot:item="{ item, index }">
        <tr>
          <td class="py-2 text-center">{{ index + 1 }}</td>
          <td class="py-2">
            <span>{{ item.name }}</span>
            <div
              v-if="item.isSubRecipe"
              class="text-caption text-medium-emphasis"
            >
              Subrecipe
            </div>
          </td>
          <td class="py-2">
            <v-text-field
              v-model.number="item.quantity"
              type="number"
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.require, rules.positive]"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="item.quantity = item.quantity || 0"
            >
              <template v-slot:append-inner>
                {{ item.recipeUnit?.symbol || item.recipeUnit || "" }}
              </template>
            </v-text-field>
          </td>
          <td class="py-2 text-center">
            <span>{{ item.name ? formattedRecipeUnitPrice(item) : "-" }}</span>
          </td>
          <td class="py-2 text-center">
            <span>{{
              parseFloat(formattedLineItemPrice(item)).toFixed(2)
            }}</span>
          </td>
          <td class="py-2">
            <div class="d-flex justify-center align-center">
              <v-icon color="error" @click="removeRow(index)">
                mdi-close
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
      <template v-slot:body.append>
        <tr>
          <td class="py-2"></td>
          <td class="py-2">
            <v-autocomplete
              v-model="newRow.name"
              :items="inventoryList"
              item-title="itemName"
              item-value="id"
              density="compact"
              variant="outlined"
              single-line
              hide-details
              color="primary"
              return-object
              @update:model-value="(val) => setValues(val, null)"
              clearable
              tabindex="-1"
            >
              <template #item="{ item, props }">
                <v-list-item
                  v-bind="props"
                  :title="item.raw.itemName"
                  :subtitle="item.raw.recipeType ? 'Subrecipe' : ''"
                >
                </v-list-item>
              </template>
            </v-autocomplete>
          </td>
          <td class="py-2">
            <v-text-field
              v-model="newRow.quantity"
              type="number"
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="newRow.quantity = newRow.quantity || 0"
              @keypress.enter="addRow(newRow)"
              :rules="[rules.positive]"
            >
              <template v-slot:append-inner>
                {{ newRow.recipeUnit?.symbol || newRow.recipeUnit || "" }}
              </template>
            </v-text-field>
          </td>
          <td class="py-2 text-center">
            <span>{{
              newRow.name ? formattedRecipeUnitPrice(newRow) : "-"
            }}</span>
          </td>
          <td class="py-2 text-center">
            <span>{{
              parseFloat(formattedLineItemPrice(newRow)).toFixed(2)
            }}</span>
          </td>
          <td class="py-2">
            <div class="d-flex justify-center align-center">
              <v-icon
                color="green"
                :disabled="!isRowValid(newRow)"
                @click="commitNewRow"
              >
                mdi-plus
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
      <!-- 👇 Aligned Footer Totals -->
      <template #tfoot>
        <tr class="sticky-bottom-row">
          <!-- index -->
          <td></td>
          <!-- Item Name -->
          <td class="font-weight-bold">Total</td>
          <td></td>
          <td></td>
          <td class="text-center font-weight-bold pr-4">{{ totalPrice }}</td>
          <!-- actions -->
          <td></td>
        </tr>
      </template>
    </v-data-table>
  </v-card>
</template>
<script setup>
import { onMounted, ref, nextTick, computed } from "vue";
import rules from "@/helpers/rules";
import { recipeIngredientHeaders } from "@/helpers/tableHeaders";
import { ingredientRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

defineProps({
  inventoryList: {
    type: Array,
    default: () => [],
  },
});

const items = defineModel();
const inputRef = ref(null);
const headers = recipeIngredientHeaders;

const newRow = ref({ ...DEFAULT_RECORD });

const isRowValid = (row) => row.quantity > 0 && row.name;
const commitNewRow = () => {
  items.value.push({ ...newRow.value });
  newRow.value = { ...DEFAULT_RECORD };
};
const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow(row);
};
const removeRow = (index) => {
  items.value.splice(index, 1);
};

const setValues = (val, row = null) => {
  const target = row || newRow.value;
  if (!val) return;
  const isRecipe = val.hasOwnProperty("recipeType");

  if (isRecipe) {
    target.recipeUnit = val.recipeUnit;
    target.unitCost = val.cost / val.quantity;
    target.name = val.itemName;
    target.id = val.id;
    target.code = val.id;
    target.purchaseUnit = {};
    target.countingUnit = {};
    target.isSubRecipe = true;
    return;
  }
  target.name = val.itemName;
  target.id = val.id;
  target.code = val.itemCode;
  target.purchaseUnit = val.purchaseUnit;
  target.countingUnit = val.countingUnit;
  target.recipeUnit = val.recipeUnit;
  target.unitCost = val.unitCost;
};

const formattedRecipeUnitPrice = (item) => {
  if (!item.unitCost) return 0;

  if (item.isSubRecipe) {
    return `${parseFloat(getRecipeUnitPrice(item)).toFixed(2)} / ${
      item.recipeUnit
    }`;
  }
  return `${parseFloat(getRecipeUnitPrice(item)).toFixed(2)} / ${
    item.recipeUnit?.symbol
  }`;
};

const totalPrice = computed(() => {
  const result = items.value.reduce((acc, item) => {
    return acc + formattedLineItemPrice(item);
  }, 0);
  return result >= 0 ? parseFloat(result).toFixed(2) : 0;
});

const getRecipeUnitPrice = (item) => {
  if (!item.unitCost) return 0;
  if (item.isSubRecipe) {
    return item.unitCost;
  }
  return item.unitCost / item.recipeUnit?.conversion;
};

const formattedLineItemPrice = (item) => {
  if (!getRecipeUnitPrice(item)) return 0;

  return getRecipeUnitPrice(item) * item.quantity;
};

onMounted(async () => {
  await nextTick();
  document
    .querySelectorAll(".v-field__clearable i[tabindex='0']")
    .forEach((el) => el.setAttribute("tabindex", "-1"));
});
</script>
