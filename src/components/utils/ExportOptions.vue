<template>
  <!-- Single Export Option = Simple Button -->
  <template v-if="enabledOptions.length === 1">
    <v-btn
      :loading="loading"
      variant="tonal"
      color="primary"
      :prepend-icon="selectedOption.icon"
      class="ml-2"
      :class="{ 'cursor-default': selectMode }"
      @click="selectMode ? null : onChange(enabledOptions[0].type)"
    >{{ selectedOption.label }}</v-btn>
  </template>

  <!-- Multiple Export Options = Dropdown Menu -->
  <template v-else>
    <v-menu v-model="menu" offset-y>
      <template #activator="{ props }">
        <v-btn
          v-bind="props"
          color="primary"
          variant="tonal"
          :loading="loading"
          :prepend-icon="selectedOption.icon"
          :text="selectedOption.label"
          append-icon="mdi-chevron-down"
          class="ml-2"
        ></v-btn>
      </template>

      <v-list density="compact">
        <div v-for="(opt, index) in enabledOptions" :key="opt.type">
          <v-list-item
            :prepend-icon="opt.icon"
            :title="opt.label"
            :disabled="loading"
            @click="onChange(opt.type)"
          />
          <v-divider v-if="index < enabledOptions.length - 1" />
        </div>
      </v-list>
    </v-menu>
  </template>
</template>

<script setup>
import { ref, computed } from "vue";

import { ResultType } from "@/plugin/Axios";

const props = defineProps({
  modelValue: {
    type: String,
    default: null
  },
  list: {
    type: Boolean,
    default: false
  },
  pdf: {
    type: Boolean,
    default: false
  },
  xlsx: {
    type: Boolean,
    default: true
  },
  selectMode: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["update:modelValue", "export"]);
const menu = ref(false);

// Configurable export options (clean formatting)
const options = [
  {
    label: "View",
    type: ResultType.JSON,
    icon: "mdi-format-list-bulleted",
    disabled: !props.list
  },
  {
    label: "PDF",
    type: ResultType.PDF,
    icon: "mdi-file-pdf-box",
    disabled: !props.pdf
  },
  {
    label: "Xlsx",
    type: ResultType.EXCEL,
    icon: "mdi-file-excel-box",
    disabled: !props.xlsx
  }
];

const enabledOptions = computed(() => options.filter(o => !o.disabled) || []);

function onChange(type) {
  if (props.selectMode) {
    emit("update:modelValue", type);
  } else {
    emit("export", type);
  }
}

const _fallback = { icon: "mdi-download", label: "Export" };
const selectedOption = computed(() => {
  if (!props.selectMode) return _fallback;
  if (!enabledOptions.value) return _fallback;
  if (enabledOptions.value.length === 1) {
    return enabledOptions.value[0];
  }
  const option = enabledOptions.value.find(x => x.type === props.modelValue);
  if (option) {
    return option;
  }
  return _fallback;
});
</script>

<style scoped>
</style>
