<template>
  <v-menu>
    <template #activator="{ props }">
      <v-btn
        v-if="icon"
        v-bind="props"
        variant="text"
        icon="mdi-dots-vertical-circle-outline"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
      <v-btn
        v-else
        v-bind="props"
        variant="outlined"
        prepend-icon="mdi-dots-vertical"
        text="Options"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
    </template>

    <v-list class="pa-0">
      <div v-for="(action, index) in availableOptions" :key="action.id">
        <v-list-item
          :prepend-icon="action.icon"
          :title="action.label"
          :disabled="action.disabled"
          @click="handleAction(action.id)"
        />
        <v-divider v-if="index < availableOptions.length - 1" />
      </div>
    </v-list>
  </v-menu>
</template>

<script setup>
import { computed, inject } from "vue";
import { useRouter } from "vue-router";
import { useTransferStore } from "@/stores/transfer";
import httpClient from "@/plugin/Axios"; 

const transferStore = useTransferStore();
const router = useRouter();

const props = defineProps({
  icon: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    required: true,
  },
  disableReceive: {
    type: Boolean,
    default: false,
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
  showExportOnly: {
    type: Boolean,
    default: false,
  },
});

const options = [
  {
    id: "dispatch",
    label: "Dispatch",
    icon: "mdi-truck",
  },
  // {
  //   id: "receive",
  //   label: "Receive",
  //   icon: "mdi-package-variant-closed-check",
  // },
  {
    id: "close",
    label: "Close",
    icon: "mdi-close-circle-outline",
  },
  {
    id: "printPDF",
    label: "Export as PDF",
    icon: "mdi-file-pdf-box",
  },
  // {
  //   id: "printXLS",
  //   label: "Export as XLS",
  //   icon: "mdi-file-excel-box",
  // },
];

const availableOptions = computed(() => {
  if (props.showExportOnly) {    
    return options.filter(opt => opt.id === "printPDF");
  }

  const { receiveStatus, dispatchStatus, timeLine } = props.item;

  const received = receiveStatus === "completed";
  const dispatched = dispatchStatus === "completed";
  const timelineStatus = timeLine?.some(t => t.status !== "completed");

  return options
    .filter(opt => {
      if (dispatched && opt.id === "dispatch") return false;
      if (received && ["dispatch", "receive"].includes(opt.id)) return false;
      return true;
    })
    .map(opt => {
      const disabled =
        (opt.id === "receive" && props.disableReceive) ||
        (opt.id === "close" && (timelineStatus || received));

      return { ...opt, disabled };
    });
});

const disabled = computed(() => availableOptions.value.length === 0);

const handleAction = async (actionId) => {
  const action = availableOptions.value.find((a) => a.id === actionId);
  if (!action || action.disabled) return;

  switch (actionId) {
    case "close":
      await closeTransfer();
      break;

    case "printPDF":
      await exportPDF();
      break;

    case "printXLS":
      await exportPDF();
      break;

    case "dispatch":
    case "receive": {
      const routeName = actionId === "dispatch" ? "dispatch-transfer" : "receive-transfer";
      router.push({
        name: routeName,
        params: { id: props.item.id },
        query: { type: actionId },
      });
      break;
    }

    default:
      break;
  }
};

const navigatePrevious = () => {
  router.push({ name: "transfers" });
};

const $loader = inject("loader");

const exportPDF = async () => {
  $loader.show("Please wait...");
  try {
    await httpClient.get(`transfers/${props.item.id}/pdf`, { responseType: "blob" });
  } catch ({ response }) {
    console.error(response.data.message);
  } finally {
    $loader.hide();
  }
};

const closeTransfer = async () => {
  try {
    const currentRoute = router.currentRoute.value.name;

    let transferData = props.item;

    if (currentRoute === "transfers") {
      transferData = await transferStore.fetchTransferById(props.item.id, "view");
    }

    await transferStore.closeTransfer(props.item.id);

    currentRoute === "transfers" ? await transferStore.fetchTransfers(props.filters) : navigatePrevious();
  } catch (err) {
    console.error(err);
  }
};
</script>
