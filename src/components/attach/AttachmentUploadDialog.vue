<template>
  <v-dialog persistent v-model="show" max-width="600px">
    <template #activator="{ props }">
      <v-btn
        v-if="!add"
        v-bind="props"
        color="primary"
        variant="tonal"
        prepend-icon="mdi-paperclip"
        text="Attach"
        size="small"
      />
    </template>

    <v-card title="Upload Attachments">
      <template #append>
        <v-btn color="error" variant="text" icon="mdi-close" @click="close"></v-btn>
      </template>
      <v-divider />
      <v-card-text>
        <upload-attachment ref="uploadRef" @update="show = false" />
      </v-card-text>
      <v-divider />

      <v-card-actions class="justify-center">
        <v-btn
          color="primary"
          variant="flat"
          prepend-icon="mdi-cloud-upload"
          @click="startUpload"
        >Upload</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref } from "vue";

import UploadAttachment from "./UploadAttachment.vue";

const props = defineProps({
  itemId: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true
  },
  add: {
    type: Boolean,
    default: false
  },
  updateDbUrl: {
    type: String,
    default: null
  },
});

const show = defineModel({ type: Boolean, default: false });
const uploadRef = ref(null);
const emit = defineEmits(["uploaded"])

const startUpload = async () => {
  if (!uploadRef.value) return;

  try {
    await uploadRef.value.upload(props.type, props.itemId, props.updateDbUrl);
    emit("uploaded");
  } catch (err) {
    console.error("Upload failed:", err);
  }
};

const close = () => {
  show.value = false;
  if (uploadRef.value) uploadRef.value.reset();
};
</script>
