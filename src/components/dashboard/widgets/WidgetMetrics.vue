<template>
  <!-- Main container card -->
  <v-card density="compact" flat>
    <v-row dense>
      <!-- Iterate over each item to create a card -->
      <v-col v-for="item in items" :key="item.id" cols="12" sm="6" md="3" class="d-flex">
        <widget-metric-card :loading="loading" :item="item" />
      </v-col>
    </v-row>
  </v-card>
</template>

<script setup>
import { computed } from "vue";

import WidgetMetricCard from "./WidgetMetricCard.vue";

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});
</script>

<style scoped>
</style>
