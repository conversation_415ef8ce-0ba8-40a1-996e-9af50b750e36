<template>
  <v-app>
    <!-- Side navigation drawer -->
    <side-navigation-bar v-model="toggleNav" />

    <!-- Top nav toolbar -->
    <TopNavBar :title="title" @toggle="toggleNav = !toggleNav" />

    <!-- Main content area -->
    <!-- margin bottom added to sticky top bar -->
    <v-main style="margin-bottom: 75px">
      <router-view></router-view>
    </v-main>
  </v-app>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRoute } from "vue-router";

// Components
import TopNavBar from "@/components/navigation/TopNavBar.vue";
import SideNavigationBar from "@/components/navigation/SideNavigationBar.vue";

// Controls whether the navigation drawer is open or closed
const toggleNav = ref(true);
const route = useRoute();
const title = computed(() => route.meta.title);
</script>

<style></style>
