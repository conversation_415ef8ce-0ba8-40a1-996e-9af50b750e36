<template>
  <v-container fluid class="px-0">
    <v-card border rounded="lg" width="100%">
      <v-data-table
        class="table-bordered"
        :items="items"
        :headers="spoilageViewHeaders"
        items-per-page="-1"
        hide-default-footer
      >
        <template #item.index="{ index }">{{ index + 1 }}</template>

        <template #item.itemName="{ item }">
          <formatted-item-name :item="item"></formatted-item-name>
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>

<script setup>
import { spoilageViewHeaders } from "@/helpers/tableHeaders";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
});
</script>
