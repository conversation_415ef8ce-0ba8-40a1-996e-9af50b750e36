<template>
  <v-container fluid>
    <Pie :data="chartData" :options="chartOptions" />
  </v-container>
</template>

<script setup>
import { Pie } from "vue-chartjs";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

const props = defineProps({
  chartData: { type: Object, required: true },
  chartOptions: {
    type: Object,
    default: () => {},
  },
});
</script>

<style></style>
