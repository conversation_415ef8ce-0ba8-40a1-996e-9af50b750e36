// Inventory Dashboard Helper

// -------------------------------
// Top-level KPI Metrics (Cards)
// -------------------------------
// These are summary metrics for quick visibility into inventory state.
// Each metric has:
// - id/key: unique identifier
// - title: displayed on card
// - subtitle: short description
// - icon: Material Design Icon name
// - color: color for card/icon
// - value: default 0
// - highlight: true/false to visually differentiate critical metrics

export const metricDefs = [
  {
    id: "openingStock",
    key: "openingStock",
    title: "Opening Stock Value",
    subtitle: "Stock value at start of the period",
    icon: "mdi-warehouse-outline",
    color: "blue-lighten-3",
    value: 0,
    highlight: false,
  },
  {
    id: "closingStock",
    key: "closingStock",
    title: "Closing Stock Value",
    subtitle: "Stock value at end of the period",
    icon: "mdi-warehouse",
    color: "blue-lighten-2",
    value: 0,
    highlight: false,
  },
  {
    id: "purchasesVendor",
    key: "purchasesVendor",
    title: "Purchases from Vendor",
    subtitle: "Total purchase amount during the period",
    icon: "mdi-truck-delivery-outline",
    color: "teal-lighten-2",
    value: 0,
    highlight: false,
  },
  {
    id: "ibtIn",
    key: "ibtIn",
    title: "IBT In from Locations",
    subtitle: "Inventory transfers received from other locations",
    icon: "mdi-warehouse-import-outline",
    color: "teal-lighten-3",
    value: 0,
    highlight: false,
  },
  {
    id: "returnsFromWork",
    key: "returnsFromWork",
    title: "Returns from Work Areas",
    subtitle: "Returned goods from production or outlets",
    icon: "mdi-tray-arrow-down",
    color: "lime-lighten-2",
    value: 0,
    highlight: false,
  },
  {
    id: "indentConsumed",
    key: "indentConsumed",
    title: "Indents / Usage Consumed",
    subtitle: "Stock consumed for production and service",
    icon: "mdi-cart-outline",
    color: "amber-lighten-2",
    value: 0,
    highlight: false,
  },
  {
    id: "ibtOut",
    key: "ibtOut",
    title: "IBT Out to Locations",
    subtitle: "Inventory transfers sent to other locations",
    icon: "mdi-warehouse-export-outline",
    color: "orange-lighten-2",
    value: 0,
    highlight: false,
  },
  {
    id: "returnsVendor",
    key: "returnsVendor",
    title: "Returns to Vendor",
    subtitle: "Returned items sent back to supplier",
    icon: "mdi-undo-variant",
    color: "red-lighten-3",
    value: 0,
    highlight: false,
  },
  {
    id: "spoilageLoss",
    key: "spoilageLoss",
    title: "Spoilage / Loss",
    subtitle: "Loss due to spoilage, wastage, or breakage",
    icon: "mdi-alert-octagon-outline",
    color: "deep-orange-lighten-3",
    value: 0,
    highlight: false,
  },
  {
    id: "activeSkus",
    key: "activeSkus",
    title: "Active SKUs",
    subtitle: "Total inventory items active in operations",
    icon: "mdi-format-list-bulleted-type",
    color: "purple-lighten-2",
    value: 0,
    highlight: false,
  },
  {
    id: "netMovement",
    key: "netMovement",
    title: "Net Movement (Build-up)",
    subtitle: "Net stock movement (inflow - outflow)",
    icon: "mdi-trending-up",
    color: "deep-purple-lighten-2",
    value: 0,
    highlight: false,
  },
  {
    id: "turnoverEfficiency",
    key: "turnoverEfficiency",
    title: "Turnover Efficiency",
    subtitle: "Measures inventory usage efficiency (x)",
    icon: "mdi-speedometer-slow",
    color: "red-lighten-2",
    value: 0,
    highlight: true,
  },
];

// -------------------------------
// Dashboard Widgets (Charts)
// -------------------------------
// Widgets provide deeper visual insights using line, bar, or pie charts.
// Each widget has:
// - id: unique identifier
// - title & subtitle: displayed above chart
// - topN: limit of top items
// - byQty / byValue: labels for toggle view
// - viewMode: 1=Count, 2=Amount
// - graphType: "line" | "bar" | "pie"
// - icon: material icon
// - color: widget color
// - dataset: placeholder array (to be filled from API/server)

export const widgetDefs = {
  financialOverview: {
    id: "financialOverview",
    title: "Complete Inventory Movement Tracking - FINANCIAL OVERVIEW",
    subtitle: "Strategic: Track every rupee from opening to closing stock",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "pie",
    icon: "mdi-cash-multiple",
    color: "#42A5F5", // blue
    dataset: []
  },
  rupeeValueTracking: {
    id: "rupeeValueTracking",
    title: "Smart Movement Analysis - RUPEE VALUE TRACKING",
    subtitle: "Strategic: Category activity and flow balance for operational optimization",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "line",
    icon: "mdi-currency-inr",
    color: "#26A69A", // teal
    dataset: []
  },
  varianceAction: {
    id: "varianceAction",
    title: "Physical vs System Stock Variance - IMMEDIATE ACTION",
    subtitle: "Critical: Identify discrepancies between physical counts and system records",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "bar",
    icon: "mdi-alert-octagon-outline",
    color: "#EF5350", // red
    dataset: []
  },
  riskIntelligence: {
    id: "riskIntelligence",
    title: "Smart Risk Intelligence - URGENT ATTENTION NEEDED",
    subtitle: "Critical: Multi-factor risk assessment to prioritize management attention",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "bar",
    icon: "mdi-shield-alert-outline",
    color: "#FF7043", // orange
    dataset: []
  },
  topInvestmentItems: {
    id: "topInvestmentItems",
    title: "Top 10 Investment Items - FOCUS ON HIGH-VALUE ASSETS",
    subtitle: "Strategic: Your highest-value inventory items requiring focused management",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "line",
    icon: "mdi-cube-outline",
    color: "#AB47BC", // purple
    dataset: []
  },
  categoryPortfolio: {
    id: "categoryPortfolio",
    title: "Inventory Value by Category: Business Portfolio Overview",
    subtitle: "Understand which product categories drive your inventory investment",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "pie",
    icon: "mdi-tag-multiple-outline",
    color: "#26C6DA", // cyan
    dataset: []
  },
};
