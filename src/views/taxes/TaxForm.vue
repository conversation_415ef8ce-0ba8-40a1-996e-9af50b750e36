<template>
  <v-dialog v-model="model" width="420" persistent>
    <v-card class="custom-card">
      <v-card-title
        class="d-flex justify-space-between align-center dialog-title-background"
      >
        <p>{{ isEdit ? "Edit Tax" : "Create Tax" }}</p>
        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider class="mb-4" />

      <v-card-text class="py-4" style="padding: 15px">
        <v-form ref="form">
          <!-- Tax Name -->
          <v-text-field
            v-model.trim="tag.name"
            class="mb-4"
            label="Enter tax name*"
            color="primary"
            variant="outlined"
            density="compact"
            hide-details="auto"
            @blur="cleanName"
            :rules="[rules.require, rules.maxLength(100)]"
          />

          <!-- Tax Value -->
          <v-text-field
            v-model.number="tag.value"
            class="mb-4"
            label="Enter tax value (%)*"
            color="primary"
            variant="outlined"
            density="compact"
            hide-details="auto"
            suffix="%"
            :rules="[rules.require]"
          />

          <!-- Sub Taxes -->
          <v-row>
            <v-col cols="6">
              <v-text-field
                v-model.trim="subTaxName"
                label="Sub tax name"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
              />
            </v-col>
            <v-col cols="6">
              <v-text-field
                v-model.number="subTaxValue"
                label="Value (%)"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                @keyup.enter="addSubTax"
                suffix="%"
              />
            </v-col>
          </v-row>

          <div class="d-flex justify-end mb-2">
            <v-tooltip text="ADD" location="top">
              <template #activator="{ props }">
                <v-btn
                  v-bind="props"
                  color="primary"
                  variant="tonal"
                  size="small"
                  @click="addSubTax"
                  class="mt-3"
                  :disabled="isSubTaxInvalid"
                >
                  <v-icon start>mdi-plus</v-icon>
                  Add Sub-Tax
                </v-btn>
              </template>
            </v-tooltip>
          </div>

          <!-- Error message -->
          <p v-if="subTaxError" class="text-error text-caption mt-n2 mb-3">
            {{ subTaxError }}
          </p>

          <!-- Display Sub-Taxes -->
          <v-row>
            <v-col cols="12" class="d-flex flex-wrap ga-2">
              <v-chip
                v-for="sub in record.subTaxes"
                :key="sub.name"
                closable
                color="primary"
                variant="elevated"
                @click:close="removeSubTax(sub.name)"
              >
                {{ sub.name }} - {{ sub.value }}%
              </v-chip>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-divider class="mt-4" />

      <v-card-actions class="d-flex flex-column mx-2 mb-2">
        <p class="text-primary text-caption text-center">
          * Indicates a Required Field.
        </p>
        <div class="d-flex justify-end ga-2 w-100">
          <v-btn
            color="primary"
            variant="flat"
            @click="submit(true)"
            :loading="loadBtn === 'save'"
            :disabled="loadBtn !== null"
          >
            Save
          </v-btn>
          <v-btn
            v-if="!isEdit"
            color="primary"
            variant="flat"
            @click="submit(false)"
            :loading="loadBtn === 'continue'"
            :disabled="loadBtn !== null"
          >
            Save & Continue
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import { useTaxStore } from "@/stores/taxes";
import { formatName } from "@/helpers/formatter";
import rules from "@/helpers/rules";

const props = defineProps({
  isEdit: Boolean,
  tagData: Object,
  refresh: Function,
});

const model = defineModel();
const taxStore = useTaxStore();
const form = ref(null);
const loadBtn = ref(null);

const tag = ref({ name: "", value: null });
const record = ref({ subTaxes: [] });
const subTaxName = ref("");
const subTaxValue = ref(null);
const subTaxError = ref("");

const emit = defineEmits(["saved"]);

const totalSubTaxValue = computed(() =>
  record.value.subTaxes.reduce((sum, s) => sum + Number(s.value || 0), 0)
);

// ✅ format tax name
const cleanName = () => {
  tag.value.name = formatName(tag.value.name);
};

// ✅ reset form
const resetForm = () => {
  tag.value = { name: "", value: null };
  record.value.subTaxes = [];
  subTaxName.value = "";
  subTaxValue.value = null;
};

// ✅ when opening dialog
watch(model, (open) => {
  if (!open) return;

  if (props.isEdit && props.tagData) {
    tag.value = {
      name: props.tagData.name,
      value: props.tagData.value,
    };
    record.value.subTaxes = props.tagData.components || [];
  } else {
    resetForm();
  }
});

// ✅ submit create/update
const submit = async (closeAfter = true) => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  loadBtn.value = closeAfter ? "save" : "continue";

  // base payload (no ID)
  const payload = {
    name: tag.value.name,
    value: tag.value.value,
    components: record.value.subTaxes,
  };

  try {
    if (props.isEdit && props.tagData?.id) {
      await taxStore.updateTax(props.tagData.id, payload);
    } else {
      await taxStore.createTax(payload);
    }
    emit("saved");

    loadBtn.value = null;
    if (closeAfter) model.value = false;
    else resetForm();
  } catch (error) {
    console.error("Error saving tax:", error);
    loadBtn.value = null;
  }
};

const isSubTaxInvalid = computed(() => {
  const exceeds =
    tag.value.value &&
    totalSubTaxValue.value + (subTaxValue.value || 0) > tag.value.value;
  if (exceeds) {
    subTaxError.value = "Total sub-tax value cannot exceed main tax value.";
  } else {
    subTaxError.value = "";
  }
  return (
    !subTaxName.value ||
    !subTaxValue.value ||
    isDuplicateSubTax.value ||
    exceeds
  );
});

// ✅ add sub-tax
const addSubTax = () => {
  const name = formatName(subTaxName.value);
  const value = Number(subTaxValue.value);

  if (!name || value === null || value === "" || isDuplicateSubTax.value)
    return;

  // ✅ Check if adding this exceeds main tax
  if (tag.value.value && totalSubTaxValue.value + value > tag.value.value) {
    subTaxError.value = "Total sub-tax value cannot exceed main tax value.";
    return;
  }

  record.value.subTaxes.push({ name, value });
  subTaxName.value = "";
  subTaxValue.value = null;
  subTaxError.value = "";
};

// ✅ prevent duplicates
const isDuplicateSubTax = computed(() => {
  const input = formatName(subTaxName.value).toLowerCase();
  return record.value.subTaxes.some(
    (s) => formatName(s.name).toLowerCase() === input
  );
});

// ✅ remove sub-tax
const removeSubTax = (name) => {
  const index = record.value.subTaxes.findIndex((s) => s.name === name);
  if (index !== -1) record.value.subTaxes.splice(index, 1);
};

// ✅ close dialog
const closeDialog = () => {
  model.value = false;
};
</script>
