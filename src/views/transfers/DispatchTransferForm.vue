<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Export PDF"
          @click="exportPDF"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />

        <v-btn
          v-if="canDispatch"
          text="Dispatch"
          @click="dispatchTransfer"
          variant="flat"
          color="primary"
          class="ml-2"
          :disabled="!enableDispatch || loading"
          :loading="loading"
        />
      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid>
      <transfer-overview :data="record" />

      <v-container fluid class="pa-0">
        <v-row no-gutters>
          <v-card border rounded="lg" class="my-2" width="100%">
            <v-data-table
              class="table-bordered"
              :headers="dispatchTransferHeaders"
              :items="record.items"
              items-per-page="-1"
              hide-default-footer
            >
              <template #item.pkgName="{ item }">
                {{ item?.pkg.id === 'default' ? item.countingUOM : item?.pkg.name }}
              </template>
              
              <template #item.dispatchedQuantity="{ item }" v-if="canDispatch">
                <v-text-field
                  v-model.number="item.dispatchedQuantity"
                  type="number"
                  density="compact"
                  variant="outlined"
                  hide-details
                  color="primary"
                  :min="0"
                  :max="
                    Math.min(item.disPendingQuantity, item.availableQuantity)
                  "
                  @focus="onFocus(item, 'dispatchedQuantity')"
                  @blur="onBlur(item, 'dispatchedQuantity')"
                  @keypress="preventKeys"
                  @update:model-value="
                    (val) => {
                      const minValue = Math.min(
                        item.disPendingQuantity,
                        item.availableQuantity
                      );

                      if (val > minValue) item.dispatchedQuantity = minValue;
                      if (val < 0) item.dispatchedQuantity = 0;
                    }
                  "
                >
                  <template v-slot:append-inner>
                    {{ item?.pkg.id === 'default' ? item.countingUOM : item?.pkg.name }}
                  </template>
                </v-text-field>
              </template>
            </v-data-table>
          </v-card>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed, inject } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTransferStore } from "@/stores/transfer";
import { dispatchTransferHeaders } from "@/helpers/tableHeaders";
import { useSnackbarStore } from "@/stores/snackBar";
import TransferOverview from "@/views/transfers/TransferOverview.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import httpClient from "@/plugin/Axios"; 

const transferStore = useTransferStore();
const { showSnackbar } = useSnackbarStore();

const $loader = inject("loader");

const router = useRouter();
const route = useRoute();
const transferId = route.params.id;

const record = ref({ items: [] });
const loading = ref(false);

const enableDispatch = computed(() =>
  record.value.items.some((item) => item.dispatchedQuantity > 0)
);

const canDispatch = computed(() => record.value.dispatchStatus !== "completed");

const navigatePrevious = () => {
  router.push({ name: "transfers" });
};

const onFocus = (item, value) => {
  if (Number(item[value]) === 0) {
    item[value] = null;
  }
};

const onBlur = (item, value) => {
  if (item[value] === null || item[value] === "") {
    item[value] = 0;
  }
};

const preventKeys = (event) => {
  if (event.key === "-" || event.key === "+" || event.key === "e") {
    event.preventDefault();
  }
};

const dispatchTransfer = async () => {
  loading.value = true;
  try {
    const payload = {
      id: record.value.id,
      items: record.value.items.map(i => ({
        itemId: i.itemId,
        dispatchedQuantity: i.dispatchedQuantity,
        pkg: i.pkg
      }))
    };
    await transferStore.dispatchTransfer(payload);
    navigatePrevious();
  } catch (err) {
    console.error(err);
    showSnackbar("error", "Failed to dispatch transfer");
  } finally {
    loading.value = false;
  }
};

const exportPDF = async () => {
  $loader.show("Please wait...");
  try {
    await httpClient.get(`transfers/${record.value.id}/pdf`, { responseType: "blob" });
  } catch ({ response }) {
    console.error(response.data.message);
  } finally {
    $loader.hide();
  }
};

onBeforeMount(async () => {
  loading.value = true;
  try {
    const result = await transferStore.fetchTransferById(
      transferId,
      "dispatch"
    );
    record.value = result;

    record.value.items = record.value.items.map((item) => {
      const stockQty = item.availableQuantity;
      const disQty = item.dispatchedQuantity ? item.dispatchedQuantity : 0;
      const pendingQty = item.requestedQuantity - disQty;
      const dispatchQty = Math.min(pendingQty, stockQty);

      return {
        ...item,
        dispatchedQuantity: !canDispatch.value
          ? item.dispatchedQuantity
          : dispatchQty,
        disPendingQuantity: pendingQty,
      };
    });
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped></style>
