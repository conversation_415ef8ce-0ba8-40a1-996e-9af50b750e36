<template>
  <v-container fluid max-width="800">
    <v-row class="pt-10">
      <!-- Search card -->
      <v-col cols="12" class="d-flex">
        <search-card />
      </v-col>

      <!-- Procurement Overview -->
      <v-col cols="12" class="d-flex" v-if="summaryItems.length > 0">
        <v-card
          class="w-100"
          border
          rounded="lg"
          title="Procurement Overview"
          subtitle="Overview of active procurement activities and quick actions"
          prepend-icon="mdi-package-variant-closed"
        >
          <!-- Reload button -->
          <template #append>
            <v-btn
              prepend-icon="mdi-reload"
              size="small"
              variant="tonal"
              color="primary"
              @click="fetchSummary"
              :loading="loading"
              :disabled="loading"
              title="Reload Summary"
              text="Reload"
            />
          </template>

          <v-divider />

          <v-list lines="two">
            <div v-for="(item, index) in summaryItems" :key="item.id">
              <!-- Clickable list item -->
              <v-list-item
                :title="item.title"
                :subtitle="item.subtitle"
                :to="item.link ? {name: item.link}: undefined"
              >
                <!-- Show numeric value on the left -->
                <template #prepend>
                  <v-skeleton-loader type="list-item-avatar" v-if="loading"></v-skeleton-loader>
                  <v-avatar
                    v-else
                    color="primary"
                    variant="tonal"
                    class="font-weight-bold"
                  >{{ item.value }}</v-avatar>
                </template>

                <!-- "+ New" button on right side -->
                <template #append>
                  <v-btn
                    v-if="item.addAction"
                    size="small"
                    color="primary"
                    variant="tonal"
                    :to="{ name: item.addAction }"
                  >+ {{ item.actionText }}</v-btn>
                </template>
              </v-list-item>
              <v-divider v-if="index < summaryItems.length - 1" />
            </div>
          </v-list>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import httpClient from "@/plugin/Axios";
import { PRIV_CODES } from "@/constants/privilegeCodes";
import { checkUserPrivilege } from "@/router/middleware";

import SearchCard from "@/components/navigation/SearchCard.vue";
import { useTenantStore } from "@/stores/tenant";

const tenantStore = useTenantStore();
const router = useRouter();
const loading = ref(false);

// Summary definitions
const components = ref([
  {
    id: "active_pr",
    key: "active_pr",
    title: "Active Purchase Requests",
    subtitle: "Total active purchase requests",
    link: "purchase-requests",
    addAction: "create-purchase-request",
    actionText: "PR",
    privilege: PRIV_CODES.PUR_PR,
    value: 0
  },
  {
    id: "active_po",
    key: "active_po",
    title: "Active Purchase Orders",
    subtitle: "Total active purchase orders",
    link: "purchase-orders",
    addAction: "create-purchase-order",
    actionText: "PO",
    privilege: PRIV_CODES.PUR_PO,
    value: 0
  },
  {
    id: "active_transfers",
    key: "active_transfers",
    title: "Active Transfers",
    subtitle: "Total active transfers",
    link: "transfers",
    addAction: "create-transfer",
    actionText: "Transfer",
    privilege: PRIV_CODES.TRANSFER,
    value: 0
  },
  {
    id: "active_dispatch",
    key: "active_dispatch",
    title: "Active Dispatches",
    subtitle: "Total active dispatches",
    value: 0,
    link: null,
    addAction: null,
    actionText: null,
    privilege: PRIV_CODES.DISPATCH
  }
  /*{
    id: "par_level",
    key: "par_level",
    title: "Par Level Items",
    subtitle: "Items below minimum stock level",
    value: 0,
    addAction: null,
    actionText: null,
    link: "inventory-par-level",
    privilege: PRIV_CODES.PUR_PO
  }*/
]);

// Data store for summary
const summaryData = ref({});

// Fetch purchase summary data
const fetchSummary = async () => {
  try {
    loading.value = true;
    const response = await httpClient.get("/action-center/summary");
    summaryData.value = response.data; // Expected: { active_pr: 10, active_po: 5, ... }
  } catch (err) {
    console.error("Failed to fetch purchase summary", err);
  } finally {
    loading.value = false;
  }
};

// Computed merged summary list
const summaryItems = computed(() => {
  return components.value
    .map(item => ({
      ...item,
      value: summaryData.value[item.key] ?? 0
    }))
    .filter(item => checkUserPrivilege(item.privilege));
});

// Initial fetch
onMounted(() => {
  fetchSummary();
  tenantStore.fetchTenants();
});
</script>

<style scoped>
</style>
