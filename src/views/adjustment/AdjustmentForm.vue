<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
          <!-- <v-btn
          text="Import Data"
          @click="loadData"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        /> -->
        <v-btn
          text="Submit"
          @click="create"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />

      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid>
      <v-form ref="form" v-model="formValid">
        <v-row>
          <!-- From Location -->
          <v-col cols="12" sm="6" md="3">
            <location-field
              v-model="adjustmentLocation"
              label="Adjustment Location"
              hint="Adjustment Location"
              persistent-hint
              mandatory
              return-object
              @update:model-value="locationChange"
            />
          </v-col>

          <!-- From WorkArea -->
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              ref="fromRef"
              v-model="adjustmentWorkArea"
              label="Adjustment WorkArea"
              hint="Adjustment WorkArea"
              persistent-hint
              mandatory
              return-object
              :locations="adjustmentLocation ? [adjustmentLocation.id] : null"
              :disabled="!adjustmentLocation"
              @update:model-value="workAreaChange"
            />
          </v-col>

          <!-- Adjustment Date -->
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              v-model="adjustmentDate"
              label="Adjustment Date*"
              color="primary"
              :max="todayDate"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
        </v-row>
      </v-form>

      <!-- Table -->
      <adjustment-table
        :adjustmentList="tableItems"
        :formValid="formValid"
        @addItem="openForm = true"
        @removeItem="remove"
      />

      <adjustment-item-form
        v-if="adjustmentLocation"
        v-model="openForm"
        :formValid="formValid"
        :locationId="adjustmentLocation ? adjustmentLocation.id : null"
        @add="add"
        :existingItems="tableItems"
      />
    </v-container>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, inject } from "vue";
import { useRouter } from "vue-router";
import { useAdjustmentStore } from "@/stores/adjustment";
import { useSnackbarStore } from "@/stores/snackBar";
import rules from "@/helpers/rules";
import AdjustmentTable from "./AdjustmentTable.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import LocationField from "@/components/fields/LocationField.vue";
import AdjustmentItemForm from "./AdjustmentItemForm.vue";

const router = useRouter();
const { showSnackbar } = useSnackbarStore();
const adjustmentStore = useAdjustmentStore();
const $confirm = inject("confirm");


const openForm = ref(false);
const form = ref(null);
const formValid = ref(false);
const loading = ref(false);

const adjustmentLocation = ref(null);
const adjustmentWorkArea = ref(null);
const adjustmentDate = ref(null);

const fromRef = ref(null);

const tableItems = ref([]);
const todayDate = new Date().toISOString().split("T")[0];

const add = (pr) => {
  tableItems.value.unshift(pr);
};

const remove = (index) => {
  tableItems.value.splice(index, 1);
};

const create = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  if (!tableItems.value.length) {
    showSnackbar("primary", "At least one item is required");
    return;
  }  
  loading.value = true;
  try {
    const payload = {
      adjustmentDate: adjustmentDate.value,
      locationId: adjustmentLocation.value?.id,
      locationName: adjustmentLocation.value?.name,
      workAreaId: adjustmentWorkArea.value?.id,
      workAreaName: adjustmentWorkArea.value?.name,
      items: tableItems.value.map((i) => ({
        itemId: i.itemId,
        itemName: i.itemName,
        itemCode: i.itemCode,
        categoryId: i.categoryId,
        subcategoryId: i.subCategoryId,
        categoryName: i.categoryName,
        subcategoryName: i.subCategoryName,
        adjustmentQuantity: i.adjustmentQuantity,
        adjustmentType: i.adjustmentType.id,
        countingUOM: i.countingUOM,
        // unitCost: i.unitCost ? i.unitCost : 0,
        pkg: {
          ...i.pkg,
          id: i.pkg?.id ? i.pkg.id : "default",
          name: i.pkg?.name ? i.pkg.name : "Default",
        },
      })),
    };      

    await adjustmentStore.createAdjustment(payload);

    // Clear form data after successful creation
    tableItems.value = [];
    adjustmentLocation.value = null;
    adjustmentWorkArea.value = null;
    adjustmentDate.value = null;

    navigatePrevious();
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

const loadData = async () => {}

const navigatePrevious = () => router.push({ name: "adjustment" });


const locationChange = async (loc) => {
  // Check if table has items and show confirmation dialog
  if (tableItems.value.length > 0) {
    const confirmed = await $confirm(
      "Changing the location will reset the table data. Do you want to proceed?",
      { title: "Reset Table Data", confirmText: "Yes" }
    );
    if (!confirmed) {
      return; // Do not proceed if user cancels
    }
    // Clear table data if user confirms
    tableItems.value = [];
  }

  adjustmentLocation.value = loc;
  adjustmentWorkArea.value = null;
  if (loc && fromRef.value) {
    fromRef.value.setDefault(loc.inventoryLocationId);
  }
};

const workAreaChange = async (workArea) => {
  // Check if table has items and show confirmation dialog
  if (tableItems.value.length > 0) {
    const confirmed = await $confirm(
      "Changing the location will reset the table data. Do you want to proceed?",
      { title: "Reset Table Data", confirmText: "Yes" }
    );
    if (!confirmed) {
      return; // Do not proceed if user cancels
    }
    // Clear table data if user confirms
    tableItems.value = [];
  }

  adjustmentWorkArea.value = workArea;
}

const focusFirstField = () => {
  // Focus on the first available field (From Location)
  const firstInput = document.querySelector('.v-input input');
  if (firstInput) firstInput.focus();
};

onMounted(async () => {
  await nextTick();
  focusFirstField();

  window.addEventListener('keydown', (e) => {
    if (e.key === 'Tab' && document.activeElement === document.body) {
      e.preventDefault();
      focusFirstField();
    }
  });
});

</script>
  