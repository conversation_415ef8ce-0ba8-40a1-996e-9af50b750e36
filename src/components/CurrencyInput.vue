<template>
  <v-text-field
    ref="inputRef"
    v-model.lazy="formattedValue"
    :label="label"
    variant="outlined"
    density="compact"
    hide-details="auto"
    color="primary"
    outlined
    dense
    :rules="[rules.price]"
  ></v-text-field>
</template>
<script setup>
import { useCurrencyInput } from "vue-currency-input";
import rules from "@/helpers/rules";
import { watch } from "vue";

defineProps({
  label: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["valueChange"]);

const options = {
  currency: "INR",
  locale: "en-IN",
  currencyDisplay: "hidden",
  autoDecimalDigits: true,
  precision: 3,
};

const currencyValue = defineModel();

const { inputRef, formattedValue, setValue, numberValue } = useCurrencyInput(
  options,
  false
);

watch(
  () => currencyValue.value,
  (v) => {
    setValue(v);
  }
);

watch(
  () => numberValue.value,
  (v) => emit("valueChange", v)
);
</script>
