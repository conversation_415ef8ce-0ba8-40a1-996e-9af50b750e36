<template>
  <div>
    <PageLoader v-if="loading" />

    <v-empty-state
      v-else-if="message"
      :icon="message.icon"
      :color="message.color"
      size="30"
      :title="message.title"
      :text="message.text"
    />
  </div>
</template>

<script setup>
import { computed } from "vue";
import { ResultType } from "@/plugin/Axios";

import PageLoader from "./PageLoader.vue";

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  rt: {
    type: [String, null],
    default: null
  },
  error: {
    type: [String, null],
    default: null
  }
});

const message = computed(() => {
  // Case 1: No rt → initial state
  if (!props.rt) {
    return {
      title: "Search to Get Results",
      text:
        "Start by choosing a date range and applying filters or entering a query to view your data."
    };
  }

  // Case 2: Error present
  if (props.error) {
    return {
      icon: "mdi-alert-circle",
      color: "error",
      title: "Something Went Wrong. Please try again.",
      text: props.error
    };
  }

  // Case 3: JSON → show table (no message)
  if (props.rt === ResultType.JSON) {
    return null;
  }

  // Case 4: Download completed or other type
  return {
    icon: "mdi-check-circle",
    color: "success",
    title: "Download Completed",
    text:
      "Your file is ready. You can search again or choose a new date range to download more data."
  };
});
</script>
