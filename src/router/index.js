// src/router/index.js
import { createRouter, createWebHashHistory } from "vue-router";
import { createRoute, tenantRoutes } from "./routes";
import { authGuard, checkUserPrivilege } from "./middleware";
import { PRIV_CODES } from "@/constants/privilegeCodes";

// Define main routes
const routes = [
  createRoute({
    title: "Login",
    name: "login",
    path: "/login",
    component: () => import("@/views/auth/Login.vue"),
    noAuth: true,
  }),
  createRoute({
    title: "Login",
    path: "/oAuth/exchange",
    name: "oAuthExchange",
    component: () => import("@/views/auth/OAuthExchange.vue"),
    noAuth: true,
  }),
  createRoute({
    title: "Access Not Permitted",
    name: "access-not-permitted",
    path: "/not-permitted",
    component: () => import("@/views/403.vue"),
    noAuth: true,
  }),
  createRoute({
    title: "Page Not Found",
    name: "page-not-found",
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/404.vue"),
    noAuth: true,
  }),
  createRoute({
    title: "Tenant Selection",
    path: "/select-tenant",
    name: "select-tenant",
    component: () => import("@/views/SelectTenant.vue"),
  }),
  {
    ...createRoute({
      title: "Home",
      name: "home",
      path: "/",
      component: () => import("@/views/Home.vue"),
    }),
    redirect: () => {
      if (checkUserPrivilege(PRIV_CODES.DASH_PUR)) {
        return "/action-center"
      }
      return "/welcome"
    },
    children: [
      ...tenantRoutes
    ],
  }
];

// Create router
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
});

// Apply middleware
router.beforeEach(authGuard);

export default router;
