<template>
  <div class="house-units-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search House Units"
      add-label="House Unit"
      sheets="House Units"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      @apply-filters="applyFilters"
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No house units found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear
                :height="2"
                indeterminate
                color="primary"
              ></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
                >{{ item.name }}</span
              >
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="House Unit"
                :id="item.id"
                :name="item.name"
                activate-url="/house-units/:id/activate"
                deactivate-url="/house-units/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <house-unit-form
      ref="formRef"
      :isEditing="isEditing"
      v-model="dialog"
      :record="record"
      :unit-conversions="unitConversions"
      :title="isEditing ? 'Edit' : 'Create'"
      :loading="isLoading"
      @handle-save="save"
    ></house-unit-form>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { tableHeaders } from "@/helpers/tableHeaders";
import { houseUnitRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useHouseUnitStore } from "@/stores/houseUnit";
import HouseUnitForm from "@/views/houseunits/HouseUnitForm.vue";
import { filterData } from "@/helpers/searchFilter";

const houseUnitStore = useHouseUnitStore();

const searchQuery = ref("");
const allItems = computed(() => houseUnitStore.getHouseUnits || []);
const loading = ref(false);
const editLoading = ref(null);
const isLoading = ref(false);

const headers = ref(tableHeaders["houseUnits"]);
const sortBy = ref([{ key: "name", order: "asc" }]);

const selectedStatus = ref(null);
const selectedUnitType = ref(null);

const selectStatus = (status) => {
  selectedStatus.value = status ?? null;
};
const selectUnitType = (unitType) => {
  selectedUnitType.value = unitType ?? null;
};

const filteredItems = computed(() => {
  const query = searchQuery.value?.toLowerCase() || "";
  let result = query ? filterData(allItems.value, query) : allItems.value;
  if (selectedStatus.value !== null) {
    result = result.filter(
      (item) => item.activeStatus === selectedStatus.value
    );
  }
  if (selectedUnitType.value !== null) {
    result = result.filter((item) => item.unitType === selectedUnitType.value);
  }
  return result;
});

const applyFilters = (filters) => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);
  if (filters.unitType !== undefined) selectUnitType(filters.unitType ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders["setHouseUnits"](filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter((h) => h.enable));

// Add search functionality
const handleSearch = (query) => {
  searchQuery.value = query;
};

const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const unitConversions = ref([]);

const dialog = ref(false);
const isEditing = ref(false);

const add = () => {
  isEditing.value = false;
  record.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
  unitConversions.value = [];
  dialog.value = true;
};

const edit = async (id) => {
  try {
    editLoading.value = id;
    const selected = await houseUnitStore.fetchHouseUnitById(id);
    if (selected) {
      record.value = { ...selected };
      unitConversions.value = selected.conversions || [];
      isEditing.value = true;
      dialog.value = true;
    }
  } catch (error) {
    // Error handling without console.error
  } finally {
    editLoading.value = null;
  }
};

const toggleActivate = async ({ id }) => {
  try {
    await houseUnitStore.updateHouseUnitsActiveStatus(id);
  } catch (error) {
    console.error(error);
  }
};

const formRef = ref(null);

const save = async (data, closeAfter) => {
  if (isLoading.value) return;
  isLoading.value = true;

  try {
    if (isEditing.value) {
      await houseUnitStore.updateHouseUnits(data);
    } else {
      await houseUnitStore.createHouseUnits(data);
    }

    // ✅ Always stop loading
    if (formRef.value) formRef.value.resetLoading();

    // ✅ Handle close or continue
    if (closeAfter) {
      // Close dialog after saving (both create or edit)
      dialog.value = false;
      isEditing.value = false;
      record.value = { ...DEFAULT_RECORD };
    } else {
      // Keep dialog open but reset form for next entry
      record.value = { ...DEFAULT_RECORD };
      if (formRef.value?.resetForm) formRef.value.resetForm();
    }
  } catch (error) {
    // Even on error, stop loading spinner
    if (formRef.value) formRef.value.resetLoading();
    console.error("Save failed:", error);
  } finally {
    isLoading.value = false;
  }
};

const fetch = async () => {
  await houseUnitStore.fetchHouseUnits();
};

const refresh = async () => {
  loading.value = true;
  try {
    loading.value = true;
    searchQuery.value = null;
    await fetch();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  refresh();
});
</script>

<style scoped></style>
