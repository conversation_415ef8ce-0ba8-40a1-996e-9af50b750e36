// src/helpers/routes.js
import { PRIV_CODES } from "@/constants/privilegeCodes";

export const createRoute = ({
  title,
  name,
  path,
  component,
  noAuth = false,
  privilege_code,
}) => {
  return {
    title,
    name,
    path,
    component,
    meta: {
      noAuth,
      title: title || name,
      privilege_code,
    },
  };
};

// report routes
export const procurementReportRoutes = [
  createRoute({
    title: "GRN Report",
    name: "grn-report",
    path: "/report/grn-report",
    component: () => import("@/views/reports/procurements/GRNReport.vue"),
  }),
  createRoute({
    title: "Detailed GRN Report",
    name: "grn-detailed-report",
    path: "/report/grn-detailed-report",
    component: () =>
      import("@/views/reports/procurements/DetailedGRNReport.vue"),
  }),
  createRoute({
    title: "Location-wise GRN Report",
    name: "grn-location-wise-report",
    path: "/report/grn-location-wise-report",
    component: () =>
      import("@/views/reports/procurements/Location-wiseGRNReport.vue"),
  }),
  createRoute({
    title: "Vendor-wise GRN Report",
    name: "grn-vendor-wise-report",
    path: "/report/grn-vendor-wise-report",
    component: () =>
      import("@/views/reports/procurements/Vendor-wiseGRNReport.vue"),
  }),
  createRoute({
    title: "Sub-Category-wise GRN Report",
    name: "grn-sub-category-wise-report",
    path: "/report/grn-sub-category-wise-report",
    component: () =>
      import("@/views/reports/procurements/SubCategory-wiseGRNReport.vue"),
  }),
  createRoute({
    title: "Category-wise GRN Report",
    name: "grn-category-wise-report",
    path: "/report/grn-category-wise-report",
    component: () =>
      import("@/views/reports/procurements/Category-wiseGRNReport.vue"),
  }),
  createRoute({
    title: "Item-wise GRN Report",
    name: "grn-item-wise-report",
    path: "/report/grn-item-wise-report",
    component: () =>
      import("@/views/reports/procurements/Item-wiseGRNReport.vue"),
  }),
  createRoute({
    title: "Daily GRN Report",
    name: "grn-daily-report",
    path: "/report/grn-daily-report",
    component: () => import("@/views/reports/procurements/DailyGRNReport.vue"),
  }),
];

export const stockMovementReportRoutes = [
  createRoute({
    title: "Transfer Report",
    name: "transfer-list-report",
    path: "/report/transfer-list-report",
    component: () =>
      import("@/views/reports/stockMovements/TransferListReport.vue"),
  }),
  createRoute({
    title: "Dispatch Transfer Report",
    name: "dispatch-transfer-report",
    path: "/report/dispatch-transfer-report",
    component: () =>
      import("@/views/reports/stockMovements/DispatchTransferReport.vue"),
  }),
  createRoute({
    title: "Detailed Transfer Report",
    name: "detailed-transfer-report",
    path: "/report/detailed-transfer-report",
    component: () =>
      import("@/views/reports/stockMovements/DetailedTransferReport.vue"),
  }),
];

export const tenantRoutes = [
  // Child routes
  createRoute({
    title: "Settings",
    name: "tenant-settings",
    path: "/tenant-settings",
    component: () => import("@/views/tenants/TenantSettings.vue"),
    privilege_code: PRIV_CODES.SET_TENANT,
  }),
  createRoute({
    title: "Welcome",
    name: "welcome",
    path: "/welcome",
    component: () => import("@/views/Welcome.vue"),
  }),
  createRoute({
    title: "Action Center",
    name: "action-center",
    path: "/action-center",
    component: () => import("@/views/actionCenter/Index.vue"),
    privilege_code: PRIV_CODES.DASH_PUR,
  }),
  createRoute({
    title: "Inventory Dashboard",
    name: "inventory-dashboard",
    path: "/dashboards/inventory",
    component: () => import("@/views/dashboards/InventoryDashboard.vue"),
    privilege_code: PRIV_CODES.DASH_INV,
  }),
  createRoute({
    title: "Purchase Dashboard",
    name: "purchase-dashboard",
    path: "/dashboards/purchases",
    component: () => import("@/views/dashboards/PurchaseDashboard.vue"),
    privilege_code: PRIV_CODES.DASH_PUR,
  }),
  createRoute({
    title: "COGS Dashboard",
    name: "cogs-dashboard",
    path: "/dashboards/cogs",
    component: () => import("@/views/dashboards/CogsDashboard.vue"),
    privilege_code: PRIV_CODES.DASH_COGS,
  }),
  createRoute({
    title: "Vendors",
    name: "vendors",
    path: "/vendors",
    component: () => import("@/views/vendor/VendorView.vue"),
    privilege_code: PRIV_CODES.PC_VIEW,
  }),
  createRoute({
    title: "Create Vendor",
    name: "create-vendor",
    path: "/vendors/create",
    component: () => import("@/views/vendor/VendorForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Edit Vendor",
    name: "edit-vendor",
    path: "/vendors/:id",
    component: () => import("@/views/vendor/VendorForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Categories",
    name: "categories",
    path: "/categories",
    component: () => import("@/views/category/CategoryList.vue"),
    privilege_code: PRIV_CODES.PC_VIEW,
  }),
  createRoute({
    title: "Create Category",
    name: "create-category",
    path: "/categories/create",
    component: () => import("@/views/category/CategoryForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Edit Category",
    name: "edit-category",
    path: "/categories/:id",
    component: () => import("@/views/category/CategoryForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Work/Storage Area",
    name: "work-areas",
    path: "/workAreas",
    component: () => import("@/views/workAreas/WorkArea.vue"),
    privilege_code: PRIV_CODES.SET_LOC,
  }),
  createRoute({
    title: "House Units",
    name: "house-units",
    path: "/house-units",
    component: () => import("@/views/houseunits/HouseUnitView.vue"),
    privilege_code: PRIV_CODES.PC_VIEW,
  }),
  createRoute({
    title: "Taxes",
    name: "taxes",
    path: "/taxes",
    component: () => import("@/views/taxes/TaxList.vue"),
    privilege_code: PRIV_CODES.PC_VIEW,
  }),
  createRoute({
    title: "Recipes",
    name: "recipes",
    path: "/recipes",
    component: () => import("@/views/recipe/ReceipeCreationView.vue"),
    privilege_code: PRIV_CODES.PC_VIEW,
  }),
  createRoute({
    title: "Create Recipe",
    name: "create-recipe",
    path: "/recipes/create",
    component: () => import("@/views/recipe/RecipeForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Edit Recipe",
    name: "edit-recipe",
    path: "/recipes/:id",
    component: () => import("@/views/recipe/RecipeForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Inventory Items",
    name: "inventory-items",
    path: "/inventoryItems",
    component: () => import("@/views/inventoryItem/InventoryView.vue"),
    privilege_code: PRIV_CODES.PC_VIEW,
  }),
  createRoute({
    title: "Create Inventory Item",
    name: "create-inventory-item",
    path: "/inventoryItems/create",
    component: () => import("@/views/inventoryItem/inventoryItemForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Edit Inventory Item",
    name: "edit-inventory-item",
    path: "/inventoryItems/:id",
    component: () => import("@/views/inventoryItem/inventoryItemForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Import Export",
    name: "import-export",
    path: "/import-export",
    component: () => import("@/views/importExport/ImportExportView.vue"),
    privilege_code: PRIV_CODES.PC_IMPORT,
  }),
  createRoute({
    title: "Stocks",
    name: "stocks",
    path: "/stocks",
    component: () => import("@/views/stock/StockView.vue"),
    privilege_code: PRIV_CODES.STK_VIEW,
  }),
  createRoute({
    title: "Stock Ledgers",
    name: "stock-ledgers",
    path: "/stockLog",
    component: () => import("@/views/stock/StockLogView.vue"),
    privilege_code: PRIV_CODES.STK_VIEW,
  }),
  createRoute({
    title: "Locations",
    name: "locations",
    path: "/locations",
    component: () => import("@/views/locations/StoreForm.vue"),
  }),
  createRoute({
    title: "Menu Items",
    name: "menu-items",
    path: "/menuItem",
    component: () => import("@/views/menuItem/MenuView.vue"),
  }),
  createRoute({
    title: "Edit Menu Item",
    name: "edit-menu-item",
    path: "/menuItem/:id",
    component: () => import("@/views/menuItem/MenuItemForm.vue"),
  }),
  createRoute({
    title: "Modifiers",
    name: "modifiers",
    path: "/modifier",
    component: () => import("@/views/modifier/ModifierView.vue"),
  }),
  createRoute({
    title: "Edit Modifier",
    name: "edit-modifier",
    path: "/modifier/:id",
    component: () => import("@/views/modifier/ModifierForm.vue"),
  }),
  createRoute({
    title: "Purchase Requests",
    name: "purchase-requests",
    path: "/purchaseRequests",
    component: () => import("@/views/purchaseRequest/PRList.vue"),
    privilege_code: PRIV_CODES.PUR_PR,
  }),
  createRoute({
    title: "Create Purchase Request",
    name: "create-purchase-request",
    path: "/purchaseRequest/create",
    component: () => import("@/views/purchaseRequest/PRCreate.vue"),
    privilege_code: PRIV_CODES.PUR_PR,
  }),
  createRoute({
    title: "Edit Purchase Request",
    name: "edit-purchase-request",
    path: "/purchaseRequest/:id",
    component: () => import("@/views/purchaseRequest/PRCreate.vue"),
    privilege_code: PRIV_CODES.PUR_PR,
  }),
  createRoute({
    title: "View Purchase Request",
    name: "view-purchase-request",
    path: "/purchaseRequest/:id/view",
    component: () => import("@/views/purchaseRequest/PRView.vue"),
    privilege_code: PRIV_CODES.PUR_PR,
  }),
  createRoute({
    title: "Purchase Order",
    name: "purchase-orders",
    path: "/purchaseOrders",
    component: () => import("@/views/purchaseOrder/POList.vue"),
    privilege_code: PRIV_CODES.PUR_PO,
  }),
  createRoute({
    title: "Create Purchase Order",
    name: "create-purchase-order",
    path: "/purchaseOrder/create",
    component: () => import("@/views/purchaseOrder/POCreate.vue"),
    privilege_code: PRIV_CODES.PUR_PR,
  }),
  createRoute({
    title: "Edit Purchase Order",
    name: "edit-purchase-order",
    path: "/purchaseOrder/:id",
    component: () => import("@/views/purchaseOrder/POCreate.vue"),
    privilege_code: PRIV_CODES.PUR_PO,
  }),
  createRoute({
    title: "View Purchase Order",
    name: "view-purchase-order",
    path: "/purchaseOrder/:id/view",
    component: () => import("@/views/purchaseOrder/POView.vue"),
    privilege_code: PRIV_CODES.PUR_PR,
  }),
  createRoute({
    title: "Create GRN",
    name: "create-grn",
    path: "/purchaseOrder/:id/create-grn",
    component: () => import("@/views/purchaseOrder/POForm.vue"),
    privilege_code: PRIV_CODES.PUR_PR,
  }),
  createRoute({
    title: "GRN",
    name: "goods-received-notes",
    path: "/grn",
    component: () => import("@/views/grn/GrnList.vue"),
    privilege_code: PRIV_CODES.PUR_GRN,
  }),
  createRoute({
    title: "Edit GRN",
    name: "edit-grn",
    path: "/grn/:id",
    component: () => import("@/views/grn/GrnEdit.vue"),
    privilege_code: PRIV_CODES.PUR_GRN,
  }),
  createRoute({
    title: "View GRN",
    name: "view-grn",
    path: "/grn/:id/view",
    component: () => import("@/views/grn/GrnView.vue"),
    privilege_code: PRIV_CODES.PUR_GRN,
  }),
  createRoute({
    title: "Closing",
    name: "create-closing",
    path: "/closing/create",
    component: () => import("@/views/closing/ClosingForm.vue"),
  }),
  createRoute({
    title: "Closing",
    name: "view-closing",
    path: "/closing/:id/view",
    component: () => import("@/views/closing/ClosingView.vue"),
  }),
  createRoute({
    title: "Closing",
    name: "closing",
    path: "/closing",
    component: () => import("@/views/closing/ClosingList.vue"),
  }),
  createRoute({
    title: "Transfers",
    name: "transfers",
    path: "/transfers",
    component: () => import("@/views/transfers/TransferList.vue"),
    privilege_code: PRIV_CODES.PUR_INDENT,
  }),
  createRoute({
    title: "Request Transfer",
    name: "create-transfer",
    path: "/transfer/create",
    component: () => import("@/views/transfers/CreateTransferForm.vue"),
    privilege_code: PRIV_CODES.PUR_INDENT,
  }),
  createRoute({
    title: "Dispatch Transfer",
    name: "dispatch-transfer",
    path: "/transfer/:id/dispatch",
    component: () => import("@/views/transfers/DispatchTransferForm.vue"),
    privilege_code: PRIV_CODES.PUR_INDENT,
  }),
  createRoute({
    title: "Receive Transfer",
    name: "receive-transfer",
    path: "/transfer/:id/receive",
    component: () => import("@/views/transfers/ReceiveTransferForm.vue"),
    privilege_code: PRIV_CODES.PUR_INDENT,
  }),
  createRoute({
    title: "View Transfer",
    name: "view-transfer",
    path: "/transfer/:id/view",
    component: () => import("@/views/transfers/ViewTransferForm.vue"),
    privilege_code: PRIV_CODES.PUR_INDENT,
  }),
  createRoute({
    title: "View Dispatch",
    name: "view-dispatch",
    path: "/transfer/:id/view",
    component: () => import("@/views/transfers/ViewDispatchForm.vue"),
    privilege_code: PRIV_CODES.PUR_DISPATCH,
  }),
  createRoute({
    title: "Roles",
    name: "roles",
    path: "/roles",
    component: () => import("@/views/role/RoleList.vue"),
    privilege_code: PRIV_CODES.SET_USER,
  }),
  createRoute({
    title: "Create Role",
    name: "create-role",
    path: "/role-operation",
    component: () => import("@/views/role/RoleForm.vue"),
    privilege_code: PRIV_CODES.SET_USER,
  }),
  createRoute({
    title: "Edit Role",
    name: "edit-role",
    path: "/role-operation/:id",
    component: () => import("@/views/role/RoleForm.vue"),
    privilege_code: PRIV_CODES.SET_USER,
  }),
  createRoute({
    title: "Users",
    name: "users",
    path: "/users",
    component: () => import("@/views/users/UserView.vue"),
    privilege_code: PRIV_CODES.SET_USER,
  }),
  createRoute({
    title: "Tags",
    name: "tags",
    path: "/tags",
    component: () => import("@/views/tags/TagView.vue"),
  }),
  createRoute({
    title: "Contract",
    name: "contract",
    path: "/contract",
    component: () => import("@/views/contract/ContractList.vue"),
  }),
  createRoute({
    title: "Create Contract",
    name: "create-contract",
    path: "/contract/create",
    component: () => import("@/views/contract/ContractForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Edit contract",
    name: "edit-contract",
    path: "/contract/:id",
    component: () => import("@/views/contract/ContractForm.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "View contract",
    name: "view-contract",
    path: "/contract/:id",
    component: () => import("@/views/contract/ViewContract.vue"),
    privilege_code: PRIV_CODES.PC_EDIT,
  }),
  createRoute({
    title: "Spoilage",
    name: "spoilage",
    path: "/spoilage",
    component: () => import("@/views/spoilage/SpoilageList.vue"),
  }),
  createRoute({
    title: "Create Spoilage",
    name: "create-spoilage",
    path: "/spoilage/create",
    component: () => import("@/views/spoilage/SpoilageForm.vue"),
  }),
  createRoute({
    title: "View Spoilage",
    name: "view-spoilage",
    path: "/spoilage/:id",
    component: () => import("@/views/spoilage/SpoilageView.vue"),
  }),
  createRoute({
    title: "Adjustment",
    name: "adjustment",
    path: "/adjustment",
    component: () => import("@/views/adjustment/AdjustmentList.vue"),
  }),
  createRoute({
    title: "Create Adjustment",
    name: "create-adjustment",
    path: "/adjustment/create",
    component: () => import("@/views/adjustment/AdjustmentForm.vue"),
  }),
  createRoute({
    title: "View Adjustment",
    name: "view-adjustment",
    path: "/adjustment/:id",
    component: () => import("@/views/adjustment/AdjustmentView.vue"),
  }),
  ...procurementReportRoutes,
  ...stockMovementReportRoutes,
];
