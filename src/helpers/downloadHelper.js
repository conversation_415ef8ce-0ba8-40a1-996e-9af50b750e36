/**
 * Triggers a browser download for a blob-based Axios response.
 *
 * @param {object} response - The Axios response containing blob data.
 * @param {Blob} response.data - The blob to be downloaded.
 * @returns {object} The original Axios response.
 */
export function download(response) {
  const blob = response.data;
  const url = window.URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;

  link.download = getFileName(response);
  link.click();

  // Clean up
  window.URL.revokeObjectURL(url);
  link.remove();

  // download and forward the response
  return response;
}

function getFileName(response) {
  // Use filename from config
  if (response.config.filename) {
    return response.config.filename;
  }

  // Use filename from headers
  const contentDisposition = response.headers["content-disposition"];
  if (contentDisposition) {
    const match = contentDisposition.match(/filename="?([^"]+)"?/);
    if (match && match[1]) {
      return match[1];
    }
  }

  // fallbackName
  if (response.config.fallbackName) {
    return response.config.fallbackName;
  }

  // Fallback to timestamp
  const ts = + new Date();
  const ext = response.config.resultType ? `.${response.config.resultType}` : "";
  return `${ts}${ext}`;
}