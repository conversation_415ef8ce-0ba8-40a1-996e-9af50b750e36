import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useTagStore = defineStore("tag", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const stores = ref([]);

  const getTags = computed(() => stores.value);

  const setTags = (data) => {
    stores.value = data;
  };

  const fetchTags = async () => {
    try {
      const response = await httpClient.get(`tags`);
      setTags(response.data.payload || []);
    } catch ({ response }) {
      showSnackbar("error", response?.data?.message || "Something went wrong");
      throw Error(response?.data?.message || "Request failed");
    }
  };

  const fetchTagById = async (id) => {
    try {
      const response = await httpClient.get(`tags/${id}`);
      return response.data?.payload;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createTag = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await httpClient.post("tags", data);
      showSnackbar("green", "Tag Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateTag = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await httpClient.put(`tags/${data.id}`, data);
      showSnackbar("green", "Tag Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getTags,
    fetchTags,
    fetchTagById,
    createTag,
    updateTag,
  };
});
