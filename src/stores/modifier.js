import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useModifierStore = defineStore("modifier", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const modifiers = ref([]);

  const getModifiers = computed(() => modifiers.value);
  const setModifiers = (data) => {
    modifiers.value = data;
  };

  const fetchModifiers = async () => {
    try {
      const response = await httpClient.get("modifiers");
      setModifiers(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchModifierById = async (id, accountId) => {
    try {
      const response = await httpClient.get(`modifiers/${id}`, {
        params: { accountId }
      });
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateModifier = async (data) => {
    const accId = data.account.id;
    data.posId = data.posId ? data.posId : "123456";
    data.tenantId = tenantId.value;
    try {
      await httpClient.put(`modifiers/${data.id}`, data, {
        params: { accountId: accId }
      });
      await fetchModifiers();
      showSnackbar("success", "Modifier updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getModifiers,
    setModifiers,
    fetchModifiers,
    fetchModifierById,
    updateModifier
  };
});
