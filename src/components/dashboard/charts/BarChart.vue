<template>
  <v-container fluid class="d-flex justify-center">
    <Bar :data="chartData" :options="chartOptions" />
  </v-container>
</template>

<script setup>
import { Bar } from "vue-chartjs";
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale
);

defineProps({
  chartData: {
    type: Object,
    require: true,
  },
  chartOptions: {
    type: Object,
    default: () => {},
  },
});
</script>

<style></style>
