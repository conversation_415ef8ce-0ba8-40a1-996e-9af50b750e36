import { setActivePinia, createPinia } from "pinia";
import { describe, beforeEach, it, vi, expect } from "vitest";
import httpClient from "@/plugin/Axios";
import { useVendorStore } from "@/stores/vendor.js";

const showSnackbarMock = vi.fn();

// Axios mock
vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Snackbar store mock
vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

// Reusable helper
function mockAxiosError(message) {
  return {
    response: {
      data: { message },
    },
  };
}

describe("Vendor store", () => {
  let vendorStore;

  beforeEach(() => {
    setActivePinia(createPinia());
    vendorStore = useVendorStore();

    httpClient.get.mockReset();
    httpClient.post.mockReset();
    httpClient.put.mockReset();
    httpClient.delete.mockReset();
    showSnackbarMock.mockReset();
  });

  describe("fetchVendors", () => {
    describe("success", () => {
      it("should fetch vendors", async () => {
        const mockVendors = [{ id: 1, name: "Vendor A" }];
        httpClient.get.mockResolvedValue({ data: mockVendors });

        await vendorStore.fetchVendors();

        expect(vendorStore.getVendors).toEqual(mockVendors);
        expect(httpClient.get).toHaveBeenCalledWith(
          expect.stringContaining("/vendors")
        );
      });
    });
    describe("failure", () => {
      it("should handle error when fetch vendors fails", async () => {
        const errorMessage = "Something went wrong";
        httpClient.get.mockRejectedValue(mockAxiosError(errorMessage));

        await expect(vendorStore.fetchVendors()).rejects.toThrow(errorMessage);

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
        expect(httpClient.get).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe("fetchVendorById", () => {
    describe("success", () => {
      it("should fetch vendor by id", async () => {
        const mockVendor = { id: 1, name: "Vendor A" };
        httpClient.get.mockResolvedValue({ data: mockVendor });

        await vendorStore.fetchVendorById(1);
        expect(httpClient.get).toHaveBeenCalledWith(
          expect.stringContaining("/vendors")
        );
      });
    });
    describe("failure", () => {
      it("should handle error when fetch vendor by id fails", async () => {
        const errorMessage = "Something went wrong";
        httpClient.get.mockRejectedValue(mockAxiosError(errorMessage));

        await expect(vendorStore.fetchVendorById(1)).rejects.toThrow(
          errorMessage
        );
        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("createVendor", () => {
    describe("success", () => {
      it("should create vendor", async () => {
        const newVendorData = { name: "Test vendor" };

        //mock Api responses
        httpClient.post.mockResolvedValueOnce({ status: 201 });
        httpClient.get.mockResolvedValueOnce({
          data: [{ id: 1, name: "Test vendor" }],
        });

        await vendorStore.createVendor(newVendorData);

        expect(httpClient.post).toHaveBeenLastCalledWith(
          expect.stringContaining("/vendors"),
          newVendorData
        );
        expect(httpClient.get).toHaveBeenCalledTimes(1);
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Vendor created successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when create vendor fails", async () => {
        const newVendorData = { name: "Fail vendor" };
        const errorMessage = "Vendor creation failed";

        httpClient.post.mockRejectedValueOnce(mockAxiosError(errorMessage));

        await expect(vendorStore.createVendor(newVendorData)).rejects.toThrow(
          errorMessage
        );

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("updateVendor", () => {
    describe("success", () => {
      it("should update vendor", async () => {
        const editedData = { id: 1, name: "edited vendor" };

        httpClient.put.mockResolvedValueOnce({ status: 200 });
        httpClient.get.mockResolvedValueOnce({ data: [editedData] });

        await vendorStore.updateVendor(editedData);

        expect(httpClient.put).toHaveBeenCalledWith(
          expect.stringContaining("/vendors"),
          editedData
        );
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Vendor updated successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when update vendor fails", async () => {
        const editedVendorData = { id: 1, name: "Fail vendor" };
        const errorMessage = "Vendor update failed";

        httpClient.put.mockRejectedValueOnce(mockAxiosError(errorMessage));

        await expect(
          vendorStore.updateVendor(editedVendorData)
        ).rejects.toThrow(errorMessage);

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("deleteVendor", () => {
    describe("success", () => {
      it("should delete vendor", async () => {
        const vendorId = 123;

        httpClient.delete.mockResolvedValueOnce({ status: 200 });
        httpClient.get.mockResolvedValueOnce({ data: [] });

        await vendorStore.deleteVendor(vendorId);

        expect(httpClient.delete).toHaveBeenCalledWith(
          expect.stringContaining(`/vendors/${vendorId}`)
        );
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Vendor deleted successfully"
        );
      });
    });

    describe("failure", () => {
      it("should handle error when delete vendor fails", async () => {
        const vendorId = 123;
        const errorMessage = "delete vendor failed";

        httpClient.delete.mockRejectedValueOnce(
          mockAxiosError(errorMessage)
        );

        await expect(vendorStore.deleteVendor(vendorId)).rejects.toThrow(
          errorMessage
        );
        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });
});
