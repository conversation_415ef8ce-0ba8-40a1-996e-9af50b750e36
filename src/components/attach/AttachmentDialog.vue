<template>
  <v-dialog persistent v-model="show" max-width="600px">
    <v-card title="Attachments">
      <template #append>
        <!-- open upload dialog -->
        <attachment-upload-dialog
          v-if="add"
          :type="type"
          :item-id="itemId"
          :update-db-url="updateDbUrl"
          @uploaded="handleUploaded" 
          @updated="$emit('update:attachments', $event)"
        />

        <!-- close -->
        <v-btn icon="mdi-close" color="error" variant="text" @click="show = false"></v-btn>
      </template>
      <v-divider></v-divider>
      <v-card-text>
        <!-- show attachments -->
        <Attachments :type="type" :item-id="itemId" :attachments="attachments" @deleted="$emit('deleted', $event)"/>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import httpClient from "@/plugin/Axios";

import AttachmentUploadDialog from "./AttachmentUploadDialog.vue";
import Attachments from "./Attachments.vue";

const props = defineProps({
  itemId: {
    type: String,
    required: true
  },
  attachments: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    required: true
  },
  add: {
    type: Boolean,
    default: false
  },
  updateDbUrl: {
    type: String,
    default: ""
  }
});

const show = defineModel({ type: Boolean, default: false });

const emit = defineEmits(["update:attachments", "uploaded", "deleted"]);

const handleUploaded = () => {  
  show.value = false; 
  emit("uploaded");   
};

</script>
