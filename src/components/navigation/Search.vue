<template>
  <v-menu v-if="showSearch" v-model="menu" :close-on-content-click="false" eager>
    <template #activator="{ props }">
      <v-icon
        v-bind="props"
        title="Open search (Ctrl + K)"
        icon="mdi-clipboard-text-search-outline"
        size="large"
        class="mr-4"
      />
    </template>

    <!-- Search Card -->
    <search-card @close="menu = false"/>
  </v-menu>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { useRoute } from "vue-router";

import SearchCard from "./SearchCard.vue";

const menu = ref(false);
const route = useRoute();

// Hide search if current route is 'action-center'
const showSearch = computed(() => route.name !== "action-center");
/**
 * Keyboard shortcut: Ctrl + K (Windows/Linux) or Cmd + K (Mac)
 * Toggles the search menu
 */
const handleShortcut = (e) => {
  if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === "k") {
    e.preventDefault();
    menu.value = !menu.value;
  }
};

onMounted(() => {
  window.addEventListener("keydown", handleShortcut);
});

onBeforeUnmount(() => {
  window.removeEventListener("keydown", handleShortcut);
});
</script>

<style scoped>
</style>
