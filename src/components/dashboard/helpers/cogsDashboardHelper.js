// COGS Dashboard Helper

// -------------------------------
// Top-level KPI Metrics (Cards)
// -------------------------------
// Metrics show high-level COGS performance and profitability.
// Each metric has:
// - id/key: unique identifier
// - title: displayed on card
// - subtitle: formula or explanation
// - icon: Material Design Icon
// - color: for card/icon
// - value: default 0
// - highlight: true/false for critical metrics

export const metricDefs = [
  {
    id: "totalRevenue",
    key: "totalRevenue",
    title: "Total Revenue",
    subtitle: "Formula: Sum of all department sales revenue ∑(Department Sales)",
    icon: "mdi-cash-multiple",
    color: "blue-lighten-2",
    value: 0,
    highlight: false,
  },
  {
    id: "totalCogs",
    key: "totalCogs",
    title: "Total COGS",
    subtitle: "Formula: Sum of all department cost of goods sold ∑(Department Consumption)",
    icon: "mdi-cash-minus",
    color: "red-lighten-2",
    value: 0,
    highlight: true,
  },
  {
    id: "cogsRatio",
    key: "cogsRatio",
    title: "COGS Ratio",
    subtitle: "Formula: (Total COGS ÷ Total Revenue) × 100 | Target: ≤30%",
    icon: "mdi-chart-line",
    color: "orange-lighten-2",
    value: 0,
    highlight: true,
  },
  {
    id: "grossProfit",
    key: "grossProfit",
    title: "Gross Profit",
    subtitle: "Formula: Total Revenue - Total COGS | Margin: Revenue - COGS",
    icon: "mdi-cash-check",
    color: "green-lighten-2",
    value: 0,
    highlight: false,
  },
];

// -------------------------------
// Dashboard Widgets (Charts / Analysis)
// -------------------------------
// Widgets provide deeper insights into COGS performance.
// Each widget has:
// - id: unique identifier
// - title & subtitle: displayed above chart
// - topN: optional limit of top items
// - byQty / byValue: labels for toggle view
// - viewMode: 1=Count, 2=Amount
// - graphType: "line" | "bar" | "pie" | "summary"
// - icon: Material Design Icon
// - color: widget color
// - dataset: placeholder array

export const widgetDefs = {
  performanceAnalysis: {
    id: "performanceAnalysis",
    title: "Performance Analysis",
    subtitle: "Strategic: Monitor COGS ratios by department for operational efficiency | Target: ≤30%",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "bar",
    icon: "mdi-chart-box-outline",
    color: "#42A5F5", // blue
    dataset: []
  },
  actionDashboard: {
    id: "actionDashboard",
    title: "Action Dashboard",
    subtitle: "Performance Status: 2 departments performing well, 1 needs attention | Target: ≤30%",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "bar",
    icon: "mdi-alert-circle-outline",
    color: "#EF5350", // red
    dataset: []
  },
  reconciliation: {
    id: "reconciliation",
    title: "Reconciliation",
    subtitle: "Opening + Store Transfer + Workareas Transfer - Closing = Consumption",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "line",
    icon: "mdi-file-tree-outline",
    color: "#26A69A", // teal
    dataset: []
  },
  transferWorkareas: {
    id: "transferWorkareas",
    title: "Transfer In/Out (Workareas)",
    subtitle: "Transfer In - Transfer Out - Return To Store + Spoilage + Cross-Category Indents | Cross-Category Validation: ✅",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "bar",
    icon: "mdi-forklift",
    color: "#FF7043", // orange
    dataset: []
  },
  transferStore: {
    id: "transferStore",
    title: "Transfer In/Out (Store)",
    subtitle: "Purchase + IBT In - IBT Out - Return Qty + Spoilage",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "line",
    icon: "mdi-store-outline",
    color: "#AB47BC", // purple
    dataset: []
  },
  costAnalysis: {
    id: "costAnalysis",
    title: "Cost Analysis Dashboard - Consumption Values",
    subtitle: "Sales vs Consumption Values breakdown across department groups • Target: <30% cost ratio",
    topN: 10,
    byQty: "Count",
    byValue: "Value",
    viewMode: 2,
    graphType: "pie",
    icon: "mdi-finance",
    color: "#26C6DA", // cyan
    dataset: []
  },
};
