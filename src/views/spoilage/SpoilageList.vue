<template>
  <div>
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Spoilages"
      add-label="Spoilage"
      :hide-filter="true"
      :hide-import-export="true"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
    >
      <template #prepend-actions>
        <v-btn
          variant="tonal"
          color="primary"
          prepend-icon="mdi-filter-variant"
          class="ml-2"
          @click="toggleFilter"
        >
          <span class="d-none d-md-flex">Filters</span>
        </v-btn>
      </template>
    </list-actions-bar>

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="spoilageListHeaders"
            :items="filteredItems"
            :loading="loading"
            no-data-text="No Spoilages found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear
                :height="2"
                indeterminate
                color="primary"
              ></v-progress-linear>
            </template>

            <template #item.spoilageNumber="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="view(item)"
              >
                {{ item.spoilageNumber }}</span
              >
            </template>

            <!-- content -->
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <filter-nav-drawer
      ref="editFilter"
      :tabs="tabs"
      :filtersData="filters"
      @apply-filter="applyFilters"
    >
      <template #filters>
        <v-container fluid>
          <v-row>
            <v-col cols="12">
              <v-autocomplete
                v-model="stockCorrection"
                :items="options"
                item-title="name"
                item-value="id"
                label="Stock Correction"
                variant="outlined"
                density="compact"
                color="primary"
                clearable
                hide-details="auto"
                return-object
              />
            </v-col>
          </v-row>
        </v-container>
      </template>
    </filter-nav-drawer>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useSpoilageStore } from "@/stores/spoilage";
import { spoilageListHeaders } from "@/helpers/tableHeaders";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import FilterNavDrawer from "@/components/filters/NavDrawer.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";
import { useLocationStore } from "@/stores/location";
import { useStoreStore } from "@/stores/store";
import ToggleFilter from "@/components/filters/ToggleFilter.vue";

const spoilageStore = useSpoilageStore();
const locationStore = useStoreStore();
const workAreaStore = useLocationStore();
const router = useRouter();

const loading = ref(false);
const search = ref(null);
const editFilter = ref(null);

const items = computed(() => spoilageStore.getSpoilages);
const locations = computed(() => locationStore.getStores || []);
const workAreas = computed(() => workAreaStore.getLocations || []);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  return result;
});

const tabs = [{ value: 1, label: "filters" }];

const filters = computed(() => [
  {
    component: ToggleFilter,
    key: "status",   
    showDateOnly: true,  
    items: [],           
  },
  {
    component: AutoComplete,
    title: "Location",
    key: "locations",
    items: locations.value,
  },
  {
    component: AutoComplete,
    title: "WorkArea/Storage",
    key: "inventoryLocations",
    items: workAreas.value,
  },
]);

const dataFilters = ref({});

const loadData = ref(true);

const applyFilters = (filters) => {
  dataFilters.value = {
    ...filters,
    stockCorrection: stockCorrection.value?.id,
  };
  if (!loadData.value) {
    refresh();
  }
};

const stockCorrection = ref(null);

const options = ref([
  { id: "all", name: "All" },
  { id: "yes", name: "Yes" },
  { id: "no", name: "No" },
]);

const toggleFilter = () => {
  editFilter.value.toggle();
};

const handleSearch = (v) => {
  search.value = v;
};

const add = () => {
  router.push({ name: "create-spoilage" });
};

const view = (item) => {
  router.push({ name: "view-spoilage", params: { id: item.id } });
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await spoilageStore.fetchSpoilages(dataFilters.value);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await Promise.all([
    locationStore.fetchStores(),
    workAreaStore.fetchLocations(),
  ]);  
  await refresh();
  loadData.value = false;
});
</script>

<style scoped></style>
