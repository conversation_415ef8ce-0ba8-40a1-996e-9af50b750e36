import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";


export const useReceipeStore = defineStore("recipe", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const receipes = ref([]);

  const getReceipes = computed(() => receipes.value);
  const setReceipes = (data) => {
    receipes.value = data;
  };

  const fetchReceipes = async () => {
    try {
      const response = await httpClient.get(`recipes`);
      setReceipes(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchReceipeById = async (id) => {
    try {
      const response = await httpClient.get(`recipes/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createReceipe = async (data) => {
    data.tenantId = tenantId.value;
    try {
      const response = await httpClient.post("recipes", data);
      showSnackbar("green", "Receipe Created Successfully");
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateReceipe = async (data) => {
    try {
      await httpClient.put(`recipes/${data.id}`, data);
      showSnackbar("green", "Recipe Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getReceipes,
    setReceipes,
    fetchReceipes,
    fetchReceipeById,
    createReceipe,
    updateReceipe,
    // exportReceipes
  };
});
