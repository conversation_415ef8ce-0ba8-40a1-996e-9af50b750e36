import { vi, describe, it, beforeEach, expect } from "vitest";
import { useCategoryStore } from "../category";
import httpClient from "@/plugin/Axios";
import { createPinia, setActivePinia } from "pinia";

const showSnackbarMock = vi.fn();

vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Snackbar store mock
vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

const mockAxiosError = (message) => ({
  response: {
    data: { message },
  },
});
describe("category store", () => {
  let categoryStore;
  beforeEach(() => {
    setActivePinia(createPinia());
    categoryStore = useCategoryStore();

    httpClient.get.mockReset();
    httpClient.post.mockReset();
    httpClient.put.mockReset();
    httpClient.delete.mockReset();
    showSnackbarMock.mockReset();

    vi.stubGlobal("localStorage", {
      getItem: vi.fn(() => "10057"),
    });
  });
  describe("fetchCategories", () => {
    describe("success", () => {
      it("should fetch all categories", async () => {
        const tenantId = "10057";
        const mockCategories = [{ id: 1, name: "Category 1" }];

        httpClient.get.mockResolvedValue({ data: mockCategories });
        await categoryStore.fetchCategories();

        expect(categoryStore.getCategories).toEqual(mockCategories);
        expect(httpClient.get).toHaveBeenCalledWith(
          expect.stringContaining("/categories"),
          { params: { tenantId } }
        );
      });
    });
    describe("failure", () => {
      it("should handle error when fetch categories fails", async () => {
        const errorMessage = "Something went wrong";
        httpClient.get.mockRejectedValue(mockAxiosError(errorMessage));
        await expect(categoryStore.fetchCategories()).rejects.toThrow(
          errorMessage
        );
        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("fetchCategoryById", () => {
    describe("success", () => {
      it("should fetch all categories", async () => {
        const tenantId = "10057";
        const categoryId = 1;
        const mockCategories = { id: 1, name: "Category 1", tenantId };

        httpClient.get.mockResolvedValue({ data: mockCategories });
        await categoryStore.fetchCategoryById(categoryId);

        expect(httpClient.get).toHaveBeenCalledWith(
          expect.stringContaining(`/categories/${categoryId}`),
          { params: { tenantId } }
        );
      });
    });
    describe("failure", () => {
      it("should handle error when fetch category by id fails", async () => {
        const errorMessage = "Something went wrong";
        const categoryId = 1;

        httpClient.get.mockRejectedValue(mockAxiosError(errorMessage));
        await expect(
          categoryStore.fetchCategoryById(categoryId)
        ).rejects.toThrow(errorMessage);
        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("createCategory", () => {
    describe("success", () => {
      it("should create category", async () => {
        const tenantId = "10057";
        const categoryData = { name: "Test Category", tenantId };

        httpClient.post.mockResolvedValueOnce({ status: 201 });
        httpClient.get.mockResolvedValueOnce({
          data: [{ id: 1, ...categoryData }],
        });

        await categoryStore.createCategory(categoryData);
        expect(httpClient.post).toHaveBeenCalledWith(
          expect.stringContaining("/categories"),
          categoryData
        );
        expect(httpClient.get).toHaveBeenCalledTimes(1);
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Category created successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when create category fails", async () => {
        const tenantId = "10057";
        const errorMessage = "Something went wrong";
        const categoryData = { name: "Test Category", tenantId };

        httpClient.post.mockRejectedValue(mockAxiosError(errorMessage));

        await expect(
          categoryStore.createCategory(categoryData)
        ).rejects.toThrow(errorMessage);

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("updateCategory", () => {
    describe("success", () => {
      it("should update category", async () => {
        const tenantId = "10057";
        const categoryId = 1;
        const categoryData = { id: 1, name: "Test Category", tenantId };

        httpClient.put.mockResolvedValueOnce({ status: 200 });
        httpClient.get.mockResolvedValueOnce({
          data: [{ id: 1, ...categoryData }],
        });

        await categoryStore.updateCategory(categoryData);
        expect(httpClient.put).toHaveBeenCalledWith(
          expect.stringContaining(`/categories/${categoryId}`),
          categoryData
        );
        expect(httpClient.get).toHaveBeenCalledTimes(1);
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Category updated successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when update category fails", async () => {
        const errorMessage = "Something went wrong";
        const categoryData = {
          id: 1,
          name: "Test Category",
          tenantId: "10057",
        };

        httpClient.put.mockRejectedValue(mockAxiosError(errorMessage));

        await expect(
          categoryStore.updateCategory(categoryData)
        ).rejects.toThrow(errorMessage);

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("updateSubCategory", () => {
    describe("success", () => {
      it("should update subcategory", async () => {
        const categoryData = {
          id: 1,
          name: "Test Category",
          tenantId: "10057",
          subCategories: [
            {
              name: "beer",
            },
          ],
        };

        httpClient.post.mockResolvedValueOnce({ status: 200 });

        await categoryStore.updateSubCategory(categoryData);
        expect(httpClient.post).toHaveBeenCalledWith(
          expect.stringContaining(`/categories/1/subcategories`),
          categoryData
        );
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Subcategory added successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when update subcategory fails", async () => {
        const errorMessage = "Something went wrong";
        const categoryData = {
          id: 1,
          name: "Test Category",
          tenantId: "10057",
          subCategories: [
            {
              name: "beer",
            },
          ],
        };

        httpClient.post.mockRejectedValue(mockAxiosError(errorMessage));

        await expect(
          categoryStore.updateSubCategory(categoryData)
        ).rejects.toThrow(errorMessage);

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("deleteCategory", () => {
    describe("success", () => {
      it("should delete category", async () => {
        const categoryId = 1;
        const mockCategories = [{ id: 1, name: "Category 1" }];

        httpClient.delete.mockResolvedValue({ status: 200 });
        httpClient.get.mockResolvedValue([]);
        await categoryStore.deleteCategory(categoryId);

        expect(httpClient.delete).toHaveBeenCalledWith(
          expect.stringContaining(`/categories/${categoryId}`)
        );
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Category deleted successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when category delete fails", async () => {
        const errorMessage = "Something went wrong";
        const categoryId = 1;

        httpClient.delete.mockRejectedValue(mockAxiosError(errorMessage));
        await expect(categoryStore.deleteCategory(categoryId)).rejects.toThrow(
          errorMessage
        );
        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });
});
