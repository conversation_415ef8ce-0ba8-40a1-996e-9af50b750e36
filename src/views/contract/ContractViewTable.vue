<template>
  <v-container fluid class="px-0">
    <v-card border rounded="lg" width="100%">
      <v-data-table
        class="table-bordered"
        :items="items"
        :headers="contractViewHeaders"
        items-per-page="-1"
        fixed-header
        hide-default-footer
      >
        <template #item.index="{ index }">{{ index + 1 }}</template>

        <template #item.itemName="{ item }">
          <formatted-item-name :item="item" />
        </template>

        <template #item.pkgName="{ item }">
          {{ item?.pkg.id === 'default' ? item.countingUOM : item?.pkg.name }}
        </template>

        <!-- Match type id to type name -->
        <template #item.contractType="{ item }">
          {{ getTypeName(item.contractType) }}
        </template>

        <template #item.contractPrice="{ item }">
          {{ item.contractPrice }} {{ item.inclTax ? '(Incl Tax)' : '(Excl Tax)' }}
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>

<script setup>
import { ref } from "vue";
import { contractViewHeaders } from "@/helpers/tableHeaders";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
});

const types = ref([
  { id: "fixed", name: "Fixed contract price" },
  { id: "discount", name: "% discount on MRP" },
  { id: "absolute", name: "Absolute price reduction on MRP" },
]);

// Function to get readable name
const getTypeName = (id) => {
  const type = types.value.find((t) => t.id === id);
  return type ? type.name : id;
};
</script>
