<template>
  <div class="taxes-list">
    <list-actions-bar
      v-model="filterState"
      search-label="Search Taxes"
      add-label="Tax"
      sheets="Taxes"
      @refresh="refresh"
      @add="add"
      :filters="filters"
      @apply-filters="applyFilters"
    />

    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No taxes found"
          >
            <template #loading>
              <v-skeleton-loader type="table-row@10" />
            </template>

            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary" />
            </template>

            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >
                {{ item.name }}
              </span>
            </template>

            <!-- ✅ Add this -->
            <template #item.value="{ item }">
              {{ item.value }}%
            </template>
            <template v-slot:item.data-table-expand="{ internalItem, isExpanded, toggleExpand }">
  <div v-if="internalItem.raw.components && internalItem.raw.components.length">
    <v-btn
      :append-icon="isExpanded(internalItem) ? 'mdi-chevron-up' : 'mdi-chevron-down'"
      text="Sub Taxes"
      color="medium-emphasis"
      size="small"
      variant="tonal"
      border
      slim
      @click="toggleExpand(internalItem)"
    ></v-btn>
  </div>
</template>

            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Tax"
                :id="item.id"
                :name="item.name"
                activate-url="/taxes/:id/activate"
                deactivate-url="/taxes/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
            <template v-slot:expanded-row="{ columns, item }">
  <tr>
    <td :colspan="headers.length">
      <v-card tile flat>
        <v-card-text>
          <div v-if="item.components && item.components.length > 0">
            <div
              v-for="(sub, index) in item.components"
              :key="`${item.id}-${index}`"
              class="subcategory-item py-1"
            >
              {{ index + 1 }}. {{ sub.name }} - {{ sub.value }}%
            </div>
          </div>
          <div v-else class="text-center pa-2 text-medium-emphasis">
            No sub-taxes found
          </div>
        </v-card-text>
      </v-card>
    </td>
  </tr>
</template>
          </v-data-table>

        </v-card>
      </v-row>
    </v-container>

    <!-- ✅ Tax Dialog -->
    <tax-form
      v-model="dialog"
      :isEdit="isEdit"
      :tagData="editTagData"
      @saved="refresh"
    />
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";
import { useTaxStore } from "@/stores/taxes";
import { tableHeaders } from "@/helpers/tableHeaders";
import { vendorFilters } from "@/helpers/filterConfig";
import { filterData } from "@/helpers/searchFilter";
import TaxForm from "@/views/taxes/TaxForm.vue";

const taxStore = useTaxStore();
const loading = ref(false);
const headers = tableHeaders.taxes;
const sortBy = ref([{ key: "name", order: "asc" }]);
const filters = vendorFilters;
const filterState = ref({
  search: "",
  filters: { status: null },
});
const dialog = ref(false);
const isEdit = ref(false);
const editTagData = ref(null);

const filteredItems = computed(() => {
  const query = filterState.value?.search?.toLowerCase() || "";
  let result = (taxStore.getTaxes || []).map((tax) => ({
  ...tax,
  valueDisplay: `${tax.value}%`,
}));


  if (query) result = filterData(result, query);

  const status = filterState.value?.filters?.status;
  if (status !== null && status !== undefined)
    result = result.filter((item) => item.activeStatus === status);

  return result;
});

const filteredHeaders = computed(() => headers.value.filter((h) => h.enable));

const add = () => {
  dialog.value = true;
  isEdit.value = false;
  editTagData.value = null;
};

const edit = async (id) => {
  try {
    const tag = await taxStore.fetchTaxById(id);
    editTagData.value = tag;
    dialog.value = true;
    isEdit.value = true;
  } catch (error) {
    console.error("Error fetching tax:", error);
  }
};

const resetFilter = () => {
  filterState.value = {
    search: null,
    filters: { status: null },
    columns: headers,
    options: {},
  };
};

const fetch = async () => {
  await taxStore.fetchTaxes();
};

const refresh = async () => {
  try {
    loading.value = true;
    resetFilter();
    await fetch();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const applyFilters = ({ columns }) => {
  if (columns) tableHeaders.setTaxes(columns);
};

onBeforeMount(() => {
  refresh();
});
</script>
