<template>
  <div>
    <!-- no attachments -->
    <div v-if="!modelAttachments.length" class="text-medium-emphasis">— No attachments available —</div>

    <div v-else-if="!disablePreviewOption" class="d-flex justify-end pb-2">
      <v-btn
        color="primary"
        variant="tonal"
        size="small"
        block
        :prepend-icon="preview? 'mdi-close' : 'mdi-eye'"
        :text="preview? 'Close Preview' : 'Preview'"
        @click="preview = !preview"
      />
    </div>

    <PreviewAttachment v-if="preview" :type="type" :files="modelAttachments" />

    <!-- attachments -->
    <v-list v-else>
      <v-list-item
        v-for="file in modelAttachments"
        :key="file.filePath"
        border
        rounded
        variant="text"
        class="my-2"
      >
        <!-- file icon -->
        <template #prepend>
          <v-icon icon="mdi-file-document-outline"></v-icon>
        </template>

        <!-- file name -->
        <template #title>{{ file.fileName }}</template>

        <!-- actions -->
        <template #append>
          <DownloadButton :type="type" :filePath="file.filePath" :disabled="disabled"/>
          <v-btn icon="mdi-close-circle" color="error" variant="text" @click="remove(file)"></v-btn>
        </template>
      </v-list-item>
    </v-list>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import httpClient from "@/plugin/Axios";

import PreviewAttachment from "./PreviewAttachment.vue";
import DownloadButton from "./DownloadButton.vue";

const props = defineProps({
  itemId: {
    type: String,
    required: true
  },
  attachments: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    required: true
  },
  updateDbUrl: {
    type: String,
    default: ""
  },
  disabled: {
    type: Boolean,
    default: false
  },
  disablePreviewOption: {
    type: Boolean,
    default: false
  },
  showPreview: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["update:attachments","deleted"]);
const modelAttachments = ref([...props.attachments]);
const status = reactive({});
const preview = ref(props.showPreview);

// delete via signed url
const remove = async file => {
  // const key = file.name || file.fileName;
  const key = file.fileName;
  status[key] = { deleting: true };

  try {
    const url =
      props.type == "grn"
        ? `/purchases/grn/${props.itemId}/attachments`
        : `/contracts/${props.itemId}/attachments`;

    const { data } = await httpClient.delete(url, {
      data: {
        type: props.type,
        id: props.itemId,
        fileName: key,
        filePath: file.filePath,
        action: "delete"
      }
    });

    // @todo: check if this is the right place
    modelAttachments.value = modelAttachments.value.filter(f => f !== file);
    emit("update:attachments", modelAttachments.value);
    emit("deleted", file);
  } catch (err) {
    console.error("Error deleting attachment:", err);
  } finally {
    status[key].deleting = false;
  }
};

watch(
  () => props.attachments,
  val => (modelAttachments.value = [...val])
);
</script>
