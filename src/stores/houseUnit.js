import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useHouseUnitStore = defineStore("houseUnit", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const HouseUnits = ref([]);

  const getHouseUnits = computed(() => HouseUnits.value);

  const setHouseUnits = (data) => {
    HouseUnits.value = data;
  };

  const fetchHouseUnits = async () => {
    try {
      const response = await httpClient.post(`house-units/get-house-units`);
      setHouseUnits(response.data);
    } catch ({ response }) {
      showSnackbar("error", response?.data?.message || "Error fetching data");
      throw new Error(response?.data?.message || "Error fetching data");
    }
  };

  const createHouseUnits = async (data) => {
    try {
      await httpClient.post("house-units", {
        ...data,
        tenantId: tenantId.value,
      });
      await fetchHouseUnits();
      showSnackbar("green", "House Unit Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateHouseUnits = async (data) => {
    try {
      await httpClient.put(`house-units/${data.id}`, {
        ...data,
        tenantId: tenantId.value,
      });
      await fetchHouseUnits();
      showSnackbar("green", "House Unit Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const deleteHouseUnits = async (id) => {
    try {
      await httpClient.delete(`house-units/${id}`);
      await fetchHouseUnits();
      showSnackbar("green", "House Unit deleted successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchAllHouseUnits = async () => {
    try {
      const response = await httpClient.get(`house-units`);
      setHouseUnits(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchHouseUnitById = async (id) => {
    try {
      const response = await httpClient.get(`house-units/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  // Returns tenant house units without mutating the shared state
  const fetchHouseUnitsBytenantList = async () => {
    try {
      const response = await httpClient.get(`house-units`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response?.data?.message || "Error fetching data");
      throw new Error(response?.data?.message || "Error fetching data");
    }
  };

  const getRelatedHouseUnits = async (unit) => {
    try {
      const response = await httpClient.post(`house-units/related`, { unit });
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getHouseUnits,
    fetchHouseUnits,
    fetchAllHouseUnits,
    createHouseUnits,
    updateHouseUnits,
    deleteHouseUnits,
    fetchHouseUnitById,
    // exportHouseUnits,
    fetchHouseUnitsBytenantList,
    getRelatedHouseUnits,
  };
});
