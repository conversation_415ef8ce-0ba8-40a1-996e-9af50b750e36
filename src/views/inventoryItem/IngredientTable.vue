<template>
  <v-card border rounded="lg" width="100%">
    <v-data-table
      :headers="headers"
      :items="items"
      class="table-bordered"
      items-per-page="-1"
      hide-default-footer
      hide-no-data
    >
      <template v-slot:item="{ item, index }">
        <tr>
          <td class="py-2 text-center">{{ index + 1 }}</td>
          <td class="py-2">
            <span>{{ item.name }}</span>
          </td>
          <td class="py-2">
            <v-text-field
              v-model="item.quantity"
              type="number"
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.require, rules.positive]"
              @keydown.up.prevent
              @keydown.down.prevent
            >
              <template v-slot:append-inner>
                {{ item.recipeUnit?.symbol || "" }}
              </template>
            </v-text-field>
          </td>
          <td class="py-2">
            <div class="d-flex justify-center align-center">
              <v-icon color="error" @click="removeRow(index)">
                mdi-close
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
      <template v-slot:body.append>
        <tr>
          <td class="py-2"></td>
          <td class="py-2">
            <v-autocomplete
              v-model="newRow.name"
              :items="inventoryList"
              item-title="itemName"
              item-value="id"
              density="compact"
              variant="outlined"
              single-line
              hide-details
              color="primary"
              return-object
              @update:model-value="(val) => setValues(val, null)"
              clearable
            ></v-autocomplete>
          </td>
          <td class="py-2">
            <v-text-field
              v-model="newRow.quantity"
              type="number"
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              @keydown.up.prevent
              @keydown.down.prevent
              @keypress.enter="addRow(newRow)"
              :rules="[rules.positive]"
            >
              <template v-slot:append-inner>
                {{ newRow.recipeUnit?.symbol || "" }}
              </template>
            </v-text-field>
          </td>
          <td class="py-2">
            <div class="d-flex justify-center align-center">
              <v-icon
                color="green"
                :disabled="!isRowValid(newRow)"
                @click="commitNewRow"
              >
                mdi-plus
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
    </v-data-table>
  </v-card>
</template>
<script setup>
import { ref, nextTick } from "vue";
import rules from "@/helpers/rules";
import { ingredientHeaders } from "@/helpers/tableHeaders";
import { ingredientRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

defineProps({
  inventoryList: {
    type: Array,
    default: () => [],
  },
});

const items = defineModel();
// const inputRef = ref(null);
const headers = ingredientHeaders;

const newRow = ref({ ...DEFAULT_RECORD });

const isRowValid = (row) => {
  return headers
    .filter((h) => h.key !== "actions")
    .every((h) => {
      if (h.key == "quantity") return row[h.key] > 0;
      return row[h.key];
    });
};
const commitNewRow = () => {
  if (!isRowValid(newRow.value)) return;
  items.value.push({ ...newRow.value });

  newRow.value = { ...DEFAULT_RECORD };
  // nextTick(() => {
  //   inputRef.value[0].focus();
  // });
};
const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow();
};
const removeRow = (index) => {
  items.value.splice(index, 1);
};

const setValues = (val, row = null) => {
  const target = row || newRow.value;
  if (!val) return;
  target.name = val.itemName;
  target.id = val.id;
  target.code = val.itemCode;
  target.purchaseUnit = val.purchaseUnit;
  target.countingUnit = val.countingUnit;
  target.recipeUnit = val.recipeUnit;
  target.unitCost = val.unitCost;
};
</script>
