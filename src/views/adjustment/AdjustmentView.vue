<template>
  <div>
    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      :back-to="{ name: 'adjustment' }"
      hide-submit
      :loading="loading"
    >
      <template #custom>#{{ record.adjustmentNumber }}</template>

      <!-- <template #actions>
        <GRNOptions status="completed" :itemId="grnId" />
      </template> -->
    </form-actions-bar>

    <!-- loader -->
    <page-loader v-if="loading" />

    <!-- Overview/Content -->
    <v-container v-else fluid>
      <!-- Overview -->
      <AdjustmentOverview :data="record" />

      <!-- Item Table -->
      <AdjustmentTableItems :items="tableItems" :record="record" />
    </v-container>
  </div>
</template>
<script setup>
import { onBeforeMount, ref } from "vue";
import { useRoute } from "vue-router";
import { useAdjustmentStore } from "@/stores/adjustment";

// Components
import PageLoader from "@/components/utils/PageLoader.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import AdjustmentTableItems from "./AdjustmentTableItems.vue";
import AdjustmentOverview from "./AdjustmentOverview.vue";

const adjustmentStore = useAdjustmentStore();
const route = useRoute();

const adjustmentId = route.params.id;
const loading = ref(false);
const record = ref({});
const tableItems = ref([]);

const get = async () => {
  loading.value = true;
  try {
    const { items, ...result } = await adjustmentStore.fetchAdjustmentById(adjustmentId);
    record.value = result;
    tableItems.value = items;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await get();
});
</script>
