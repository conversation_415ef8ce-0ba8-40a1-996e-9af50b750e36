<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #custom>
        <span class="text-subtitle-1">#{{ record.contractNumber }}</span>
      </template>
      <template #actions>
        <contract-options :item="record" @update="refresh"/>
      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid>
      <contract-overview :data="record" :status="status" @update="refresh"/>
      <contract-view-table :items="items" />
    </v-container>
  </div>
</template>

<script setup>
import { ref, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";
import { contractRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useContractStore } from "@/stores/contract";

import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import ContractOptions from "./ContractOptions.vue";
import ContractOverview from "./ContractOverview.vue";
import ContractViewTable from "./ContractViewTable.vue";

const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

const loading = ref(false);
const route = useRoute();
const router = useRouter();
const contractId = route.params.id;
const status = route.query.status;

const items = ref([]);
const contractStore = useContractStore();

const navigatePrevious = () => {
  router.push({ name: "contract" });
};

const refresh = async () => {
  loading.value = true;
  try {
    const result = await contractStore.fetchContractById(contractId);
    record.value = result;
    items.value = result.items;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(refresh);
</script>

