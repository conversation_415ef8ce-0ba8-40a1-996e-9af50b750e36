<template>
  <v-card flat class="w-100">
    <v-divider></v-divider>
    <v-card-text>
      <v-row no-gutters v-for="(item, i) in summaryItems" :key="i">
        <v-col class="text-end">{{ item.label }}:</v-col>
        <v-col
          cols="auto"
          class="pl-3 text-end text-subtitle-1"
          style="width: 250px"
          >{{ item.value }}</v-col
        >
      </v-row>
      <!-- Charges -->
      <div v-if="editable && charges.length">
        <v-row dense v-for="charge in charges">
          <v-col class="text-end">{{ charge.name }}:</v-col>
          <v-col cols="auto" class="pl-3 text-end" style="width: 250px">
            <v-text-field
              v-model.number="charge.valueAmt"
              density="compact"
              variant="outlined"
              type="number"
              hide-details
              color="primary"
              @update:model-value="$emit('updateCharges')"
            />
          </v-col>
        </v-row>
      </div>
      <v-row no-gutters class="py-2">
        <v-col class="text-end text-subtitle-1">Total:</v-col>
        <v-col
          cols="auto"
          class="pl-3 text-end text-subtitle-1 font-weight-bold"
          style="width: 250px"
          >{{ data.total.toFixed(2) }}</v-col
        >
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  editable: {
    type: Boolean,
    default: false,
  },
  showTaxes: {
    type: Boolean,
    default: false,
  },
  charges: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["updateCharges"]);

const summaryItems = computed(() => {
  let res = [
    { label: "Subtotal", value: `${props.data.subtotal.toFixed(2)}` },
    { label: "Discount", value: `- ${props.data.discount.toFixed(2)}` },
    { label: "FOC", value: `- ${props.data.focAmount.toFixed(2)}` },
    {
      label: "Tax Amount",
      value: `${props.data.totalTaxAmount.toFixed(2)}`,
      info: true,
    },
  ];

  if (!props.editable && props.charges.length) {
    props.charges.forEach((charge) => {
      res.push({
        label: charge.name,
        value: `${charge.valueAmt.toFixed(2)}`,
      });
    });
  }
  return res;
});
</script>

<style scoped></style>
